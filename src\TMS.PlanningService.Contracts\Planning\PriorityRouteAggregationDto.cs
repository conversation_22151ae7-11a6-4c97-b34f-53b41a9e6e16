﻿using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// DTO for route aggregation summary with order enrichment
/// </summary>
public class PriorityRouteAggregationDto
{
    public string RouteKey { get; set; } = string.Empty;
    public string FromOfficeId { get; set; } = string.Empty;
    public string FromOfficeName { get; set; } = string.Empty;
    public string ToOfficeId { get; set; } = string.Empty;
    public string ToOfficeName { get; set; } = string.Empty;

    public DateTime? ActualFromTime { get; set; }
    public DateTime? PlanFromTime { get; set; }
    public DateTime? ActualToTime { get; set; }
    public DateTime? PlanToTime { get; set; }

    public Guid? PriorityPlanId { get; set; }
    public string PriorityPlanName { get; set; } = string.Empty;    

    public int TotalOrders { get; set; }
    public int TotalAlmostDueOrders { get; set; }
    public int TotalOverdueOrders { get; set; }
    public decimal TotalWeight { get; set; }

    public PagedResult<PriorityRouteAggregationDetailDto>? Details { get; set; }
}

public class PriorityRouteAggregationDetailDto
{
    public string MailerId { get; set; } = string.Empty;
    public string CurrentParentId { get; set; } = string.Empty;
    public string CurrentPackingListId { get; set; } = string.Empty;
    public string ServiceTypeId { get; set; } = string.Empty;
    public DateTime? ActualFromTime { get; set; }
    public DateTime? PlanFromTime { get; set; }
    public DateTime? ActualToTime { get; set; }
    public DateTime? PlanToTime { get; set; }
    public decimal? Weight { get; set; }
    public decimal? RealWeight { get; set; }
    public decimal? CalWeight { get; set; }

    public PriorityRouteAggregationOrderStatus Status => GetStatus();
    public PriorityRouteAggregationOrderStatus GetStatus()
    {
        // Nếu không có thời gian bắt đầu dự kiến, coi như không thể xác định → OnTime
        if (PlanFromTime == null)
            return PriorityRouteAggregationOrderStatus.OnTime;

        var diff = PlanFromTime.Value - DateTime.Now;

        // Nếu đã quá hạn mà chưa có ActualFromTime
        if (DateTime.Now > PlanFromTime && ActualFromTime == null)
            return PriorityRouteAggregationOrderStatus.Overdue;

        // Nếu chuyến thực tế bắt đầu sau thời gian dự kiến
        if (ActualFromTime != null && ActualFromTime > PlanFromTime)
            return PriorityRouteAggregationOrderStatus.Overdue;

        // Nếu còn ≤ 30 phút đến giờ chạy mà chưa xuất phát
        if (diff.TotalMinutes >= 0 &&
            diff.TotalMinutes <= 30 &&
            ActualFromTime == null)
            return PriorityRouteAggregationOrderStatus.AlmostDue;

        // Còn lại → đúng hạn
        return PriorityRouteAggregationOrderStatus.OnTime;
    }
}
