﻿using MediatR;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Constants.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.UpdatePriorities;

public class UpdatePlanningTemplatePrioritiesCommandHandler : IRequestHandler<UpdatePlanningTemplatePrioritiesCommand, string>
{
    private readonly IBaseRepository<PlanningTemplateEntity> _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentFactorProvider _current;

    public UpdatePlanningTemplatePrioritiesCommandHandler(
    IBaseRepository<PlanningTemplateEntity> repository,
    IUnitOfWork unitOfWork,
    ICurrentFactorProvider currentFactorProvider)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _current = currentFactorProvider;
    }

    public async Task<string> Handle(UpdatePlanningTemplatePrioritiesCommand request, CancellationToken cancellationToken)
    {
        var map = request.Request.Items
            .GroupBy(i => i.Id)
            .ToDictionary(g => g.Key, g => g.Last().PriorityNumber); // handle duplicates by taking last

        var ids = map.Keys.ToList();

        // scope by company when available
        var companyId = _current.CompanyId;
        var entities = await _repository.FindAsync(
            e => ids.Contains(e.Id) && !e.IsDeleted && (companyId == Guid.Empty || e.CompanyId == companyId),
            cancellationToken);

        var foundIds = entities.Select(e => e.Id).ToHashSet();
        var missingIds = ids.Where(id => !foundIds.Contains(id)).ToList();
        if (missingIds.Count > 0)
            throw new BusinessRuleValidationException(nameof(PlanningTemplateEntity.Id), MessageFormatter.FormatNotFound(nameof(PlanningTemplateEntity), string.Join(',', missingIds)));

        foreach (var e in entities)
        {
            if (map.TryGetValue(e.Id, out var pr))
                e.PriorityNumber = pr;
        }

        _repository.UpdateRange(entities);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return string.Format(CommonMessages.EntityUpdated, nameof(PlanningTemplateEntity));
    }
}
