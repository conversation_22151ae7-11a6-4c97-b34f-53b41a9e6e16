﻿using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Domain.Entities;

/// <summary>
/// Represents the relationship between route aggregations and orders/mailers
/// Snapshot of orders on a route at the time of aggregation
/// Each snapshot (RouteAggregationId) has its own set of order records
/// </summary>
public class RouteAggregationOrderEntity : EntityBase
{
    /// <summary>
    /// Foreign key to RouteAggregation (part 1 of composite FK)
    /// </summary>
    public Guid RouteAggregationId { get; set; }

    /// <summary>
    /// Foreign key to RouteAggregation (part 2 of composite FK)
    /// Must match parent RouteAggregationEntity.CreatedAt for referential integrity
    /// </summary>
    public DateTime RouteAggregationCreatedAt { get; set; }

    /// <summary>
    /// Mailer ID (Order ID)
    /// </summary>
    public string MailerId { get; set; } = string.Empty;

    /// <summary>
    /// Child Mailer ID (Order Item ID)
    /// </summary>
    public string ChildMailerId { get; set; } = string.Empty;

    /// <summary>
    /// Current status of the order on this route
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Service type for this order
    /// </summary>
    public string? ServiceTypeId { get; set; }

    /// <summary>
    /// Extra services for this order
    /// </summary>
    public string? ExtraService { get; set; }

    /// <summary>
    /// Order weight
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// Order real weight
    /// </summary>
    public decimal? RealWeight { get; set; }

    /// <summary>
    /// Order calculated weight
    /// </summary>
    public decimal? CalWeight { get; set; }

    /// <summary>
    /// Timestamp when this record was created (snapshot time)
    /// Used as partition key for table partitioning
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// 
    /// 
    /// </summary>
    public bool? IsDeleted { get; set; }

    /// <summary>
    /// Navigation property to RouteAggregation
    /// </summary>
    public RouteAggregationEntity? RouteAggregation { get; set; }
}
