﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Infra.Data.Configurations;

public class RouteAggregationOrderConfiguration : IEntityTypeConfiguration<RouteAggregationOrderEntity>
{
    public void Configure(EntityTypeBuilder<RouteAggregationOrderEntity> builder)
    {
        builder.ToTable("route_aggregation_orders");

        // Primary key (composite for partitioning)
        // PostgreSQL partitioning requires partition key (created_at) to be part of primary key
        builder.HasKey(x => new { x.Id, x.CreatedAt });

        builder.Property(x => x.Id)
            .HasColumnName("id")
            .IsRequired();

        builder.Property(x => x.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(x => x.RouteAggregationId)
            .HasColumnName("route_aggregation_id")
            .IsRequired();

        builder.Property(x => x.RouteAggregationCreatedAt)
            .HasColumnName("route_aggregation_created_at")
            .IsRequired();

        builder.Property(x => x.MailerId)
            .HasColumnName("mailer_id")
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.ChildMailerId)
            .HasColumnName("child_mailer_id")
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.Status)
            .HasColumnName("status")
            .HasMaxLength(50);

        builder.Property(x => x.ServiceTypeId)
            .HasColumnName("service_type_id")
            .HasMaxLength(50);

        builder.Property(x => x.ExtraService)
            .HasColumnName("extra_service")
            .HasMaxLength(150);

        builder.Property(x => x.Weight)
            .HasColumnName("weight")
            .HasPrecision(18, 2);

        builder.Property(x => x.RealWeight)
            .HasColumnName("real_weight")
            .HasPrecision(18, 2);

        builder.Property(x => x.CalWeight)
            .HasColumnName("cal_weight")
            .HasPrecision(18, 2);

        builder.Property(x => x.IsDeleted)
           .HasColumnName("is_deleted");

        // Foreign key relationship to RouteAggregation (composite FK)
        // RouteAggregation has composite PK (Id, CreatedAt) for partitioning
        // This entity has matching composite FK (RouteAggregationId, RouteAggregationCreatedAt)
        // Enables proper navigation property support in EF Core
        builder.HasOne(x => x.RouteAggregation)
            .WithMany(x => x.Orders)
            .HasForeignKey(x => new { x.RouteAggregationId, x.RouteAggregationCreatedAt })
            .HasPrincipalKey(x => new { x.Id, x.CreatedAt })
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        builder.HasIndex(x => x.RouteAggregationId)
            .HasDatabaseName("ix_route_aggregation_orders_route_id");

        builder.HasIndex(x => new { x.MailerId, x.ChildMailerId })
            .HasDatabaseName("ix_route_aggregation_orders_mailer");

        builder.HasIndex(x => x.Status)
            .HasDatabaseName("ix_route_aggregation_orders_status");

        // Composite index for finding orders on a route by status
        // Includes both FK columns for optimal join performance with partitioned parent table
        builder.HasIndex(x => new { x.RouteAggregationId, x.RouteAggregationCreatedAt, x.Status })
            .HasDatabaseName("ix_route_aggregation_orders_route_status");
    }
}
