﻿using MediatR;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Features.PriorityPlan.Queries.GetPriorityPlanById;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.PmsSync;
using TMS.PlanningService.Contracts.Provinces;
using TMS.PlanningService.Contracts.Routes;
using TMS.PlanningService.Contracts.Wards;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Entities.Interfaces;
using Entity = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Features.LeadTime.Queries.GetLeadTimeById;

public class GetLeadTimeByIdQueryHandler : IRequestHandler<GetLeadTimeByIdQuery, LeadTimeConfigDto?>
{
    private readonly IBaseRepository<Entity.LeadTime> _leadTimeRepository;
    private readonly ICostServiceApi _costServiceApi;
    private readonly IMetadataCacheService _metadataCacheService;
    public GetLeadTimeByIdQueryHandler(
        IBaseRepository<Entity.LeadTime> leadTimeRepository,
        ICostServiceApi costServiceApi,
        IMetadataCacheService metadataCacheService)
    {
        _leadTimeRepository = leadTimeRepository;
        _costServiceApi = costServiceApi;
        _metadataCacheService = metadataCacheService;
    }

    public async Task<LeadTimeConfigDto?> Handle(GetLeadTimeByIdQuery request, CancellationToken cancellationToken)
    {
        var data = await _leadTimeRepository.FindWithIncludeAsync(predicate: x => x.LeadTimeId == request.LeadTimeId,
                                                                  includes: x => x.LeadTimePartner);
        var entity = data.FirstOrDefault();
        if (entity == null)
            return null;

        var dto = new LeadTimeConfigDto
        {
            LeadTimeId = entity.LeadTimeId,
            FromOfficeId = entity.FromOfficeId,
            ToOfficeId = entity.ToOfficeId,
            FromZoneId = entity.FromZoneId,
            ToZoneId = entity.ToZoneId,
            StartTime = entity.StartTime,
            EndTime = entity.EndTime,
            AddDays = entity.AddDays,
            LeadTimeTypeName = entity.LeadTimeTypeName,
            TransportMethodName = entity.TransportMethodName,
            TransportVehicleTypeName = entity.TransportVehicleTypeName,
            IsActive = entity.IsActive,
            LeadTimePartnerDto = new LeadTimePartnerDto
            {
                Id = entity.LeadTimePartner?.Id,
                LeadTimeId = entity.LeadTimePartner?.LeadTimeId,
                FromTime = entity.LeadTimePartner?.FromTime,
                ToTime = entity.LeadTimePartner?.ToTime,
                FromAddDays = entity.LeadTimePartner?.FromAddDays,
                ToAddDays = entity.LeadTimePartner?.ToAddDays,
                PartnerId = entity.LeadTimePartner?.PartnerId,
                SenderPostOffice = entity.LeadTimePartner?.SenderPostOffice,
                ReceivePostOffice = entity.LeadTimePartner?.ReceivePostOffice,
                Description = entity.LeadTimePartner?.Description,
            }
        };

        var offices = new List<string> 
        {
            dto.ToOfficeId ?? "",
            dto.FromOfficeId ?? "",
            dto.LeadTimePartnerDto.SenderPostOffice ?? "",
            dto.LeadTimePartnerDto.ReceivePostOffice ?? ""
        }.Where(x => !string.IsNullOrEmpty(x));

        var postOfficesDict = await _metadataCacheService.GetPostOfficesAsync<PostOfficeDto>(offices.ToList());
        var formPostOffice = postOfficesDict.GetValueOrDefault(entity.FromOfficeId)?.PostOfficeName;
        var toPostOffice = postOfficesDict.GetValueOrDefault(entity.ToOfficeId)?.PostOfficeName;
        dto.FromPostOfficeName = formPostOffice;
        dto.ToPostOfficeName = toPostOffice;

        var leadTimePartner = dto.LeadTimePartnerDto;
        if (leadTimePartner != null && leadTimePartner.PartnerId != null)
        {
           var partner = (await _costServiceApi
                                .GetPartnersByIdsAsync(new List<Guid> { leadTimePartner.PartnerId ?? Guid.Empty })
                         ).FirstOrDefault();

            var senderOffice = postOfficesDict.GetValueOrDefault(dto.LeadTimePartnerDto.SenderPostOffice ?? "");
            var receiveOffice = postOfficesDict.GetValueOrDefault(dto.LeadTimePartnerDto.ReceivePostOffice ?? "");
            dto.LeadTimePartnerDto.PartnerName = partner?.PartnerName;

            dto.LeadTimePartnerDto.SenderPostOffice = senderOffice?.PostOfficeName;
            dto.LeadTimePartnerDto.SenderPostOfficeAddress = senderOffice?.StreetAddress;
            dto.LeadTimePartnerDto.ReceivePostOffice = receiveOffice?.PostOfficeName;
            dto.LeadTimePartnerDto.ReceivePostOfficeAddress = receiveOffice?.StreetAddress;
        }    

        return dto;
    }



}
