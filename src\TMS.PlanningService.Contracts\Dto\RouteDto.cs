﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.Dto;
public class RouteDto
{
    public Guid Id { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public decimal? LoadCapacity { get; set; }
    public int TotalVehicles { get; set; }
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public Guid CreatedBy { get; set; }
    public Guid UpdatedBy { get; set; }
    public List<RouteOperationDto> RouteOperations { get; set; } = new();
    public List<RoutePostOfficeDto> PostOffices { get; set; } = new();
    public List<RouteVehicleTypeDto> VehicleTypes { get; set; } = new();
    
}

public class RouteOperationDto
{
    public Guid RouteId { get; init; }
    /// <summary>
    /// BusinessOperation The enum must be the same as the one in Route, but with two additional values for extended management
    /// </summary>
    public BusinessOperation OperationId { get; set; }
    public string OperationName { get; set; } = string.Empty;
}

public class RoutePostOfficeDto
{
    public Guid RouteId { get; init; }
    public Guid PostOfficeId { get; init; }
    public string PostOfficeCode { get; init; } = string.Empty;
    public string PostOfficeName { get; init; } = string.Empty;
    public string StreetAddress { get; init; } = string.Empty;
    public double Latitude { get; init; }
    public double Longitude { get; init; }
    public string PostOfficeTypeName { get; set; } = string.Empty;

}

public class RouteVehicleTypeDto
{
    public Guid VehicleTypeId { get; set; }
    public int Quantity { get; set; }
    public string? Code { get; set; }
    public string? Type { get; set; }
    public string? Name { get; set; }
    public decimal? CapacityValue { get; set; }
    public string? UnitCode { get; set; }
    public Guid RouteId { get; set; }
}

