﻿using Refit;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.PmsSync;

namespace TMS.PlanningService.ApiClient;

public interface IPlanningServiceApi
{
    [Get("/api/LeadTime/GetAll")]
    Task<PmsSyncResponse> FetchDataLeadTimeAsync(CancellationToken cancellationToken);

    [Get("/api/MasterData/GetAllServiceType")]
    Task<PmsDataResponse<List<ServiceTypeDto>>> GetServiceTypeAsync();

    [Get("/api/MasterData/GetAllExtraService")]
    Task<PmsDataResponse<List<ExtraServiceDto>>> GetAllExtraServiceAsync();

    [Get("/api/MasterData/GetStatus")]
    Task<PmsDataResponse<List<OrderStatusDto>>> GetOrderStatusAsync();
}

