﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMS.PlanningService.Contracts.Dto;

namespace TMS.PlanningService.Contracts.PmsSync;
  
public record PmsSyncResponse
{
    public int TotalItems { get; set; }
    public bool IsError { get; set; }
    public string? ErrorMessage { get; set; }
    public string? ErrorCode { get; set; }
    public List<string>? Errors { get; set; }
    public string? TransId { get; set; }
    public List<LeadTimeDto> Data { get; set; } = new List<LeadTimeDto>();
 
}

public class LeadTimeDto
{
    public string? LeadTimeId { get; set; }
    public string? LeadTimeTypeId { get; set; }          
    public string? LeadTimeTypeName { get; set; }         
    public string? FromOfficeId { get; set; }            
    public string? FromOfficeProvinceId { get; set; }
    public string? ToOfficeId { get; set; }              
    public string? ToOfficeProvinceId { get; set; }
    public string? ToProvinceId { get; set; }             
    public string? StartTime { get; set; }                
    public string? EndTime { get; set; }                 
    public string? CutOffTime { get; set; }
    public int AddDays { get; set; }                      
    public string? Description { get; set; }            
    public DateTime CreatedDate { get; set; }
    public string? CreatedUserId { get; set; }
    public string? LastUpdateUser { get; set; }
    public DateTime? LastUpdateDate { get; set; }
    public int IsActive { get; set; }
    public string? ServiceTypeId { get; set; }           
    public string? MailerTypeId { get; set; }           
    public string? ExtraService { get; set; }            
    public int FromTimeDelay { get; set; }
    public int ToTimeDelay { get; set; }                  
    public double FromWeight { get; set; }               
    public double ToWeight { get; set; }
    public bool IsConnectPickRequest { get; set; }        
    public bool IsExpress { get; set; }                   
    public bool IsInternationalTrip { get; set; }
    public string? ConnectionType { get; set; }           
    public string? FromZoneId { get; set; }              
    public string? ToZoneId { get; set; }                 
    public int FromPostOfficeType { get; set; }
    public string? FromPostOfficeTypeName { get; set; }   
    public int ToPostOfficeType { get; set; }
    public string? ToPostOfficeTypeName { get; set; }    
    public string? TransportProviderType { get; set; }   
    public string? TransportProviderTypeName { get; set; }
    public string? TransportVehicleType { get; set; }    
    public string? TransportVehicleTypeName { get; set; }
    public string? TransportMethodId { get; set; }       
    public string? TransportMethodName { get; set; }
}

public class LeadTimeConfigDto
{
    public string? LeadTimeId { get; set; }
    public string? LeadTimeTypeId { get; set; }
    public string? LeadTimeTypeName { get; set; }
    public string? FromOfficeId { get; set; }
    public string? ToOfficeId { get; set; }
    public string? StartTime { get; set; }
    public string? EndTime { get; set; }
    public int AddDays { get; set; }
    public bool? IsActive { get; set; }
    public string? FromZoneId { get; set; }
    public string? ToZoneId { get; set; }
    public string? TransportProviderTypeName { get; set; }
    public string? TransportVehicleTypeName { get; set; }
    public string? TransportMethodName { get; set; }

    public string? ToPostOfficeName { get; set; }
    public string? FromPostOfficeName { get; set; }
    public LeadTimePartnerDto? LeadTimePartnerDto { get; set; }
}
