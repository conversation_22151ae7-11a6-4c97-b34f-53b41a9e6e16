﻿using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Application.Services.Step2.Plan;

/// <summary>
/// Service for real-time aggregation of adjust plans by FromTime:ToTime:FromOffice:ToOffice routes
/// Route keys are formatted with time first for easy chronological sorting
/// Updates aggregations incrementally via RabbitMQ events without database queries
/// </summary>
public interface IPlanningAggregationService
{
    /// <summary>
    /// Updates route aggregation in real-time when adjust plans or plan routes change
    /// Called from RabbitMqPlanningMessageHandler for each planning event
    /// If MailerAdjustRoutes are available, they are used; otherwise MailerPlanRoutes are used
    /// </summary>
    /// <param name="routes">List of routes (MailerAdjustRoute or MailerPlanRoute) from the planning event</param>
    /// <param name="actualRoutes">List of actual routes from the planning event</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <param name="originalPlanningEvent">Optional original planning event to send to OrderService if metrics not found</param>
    Task UpdateRouteAggregationsAsync(
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        List<MailerPlanRoute> planRoutes,
        bool isUsingAdjustRoutes,
        CancellationToken cancellationToken = default,
        RabbitMqPlanningEvent? originalPlanningEvent = null);

    /// <summary>
    /// Gets all cached route aggregations from Redis
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of cached route aggregation summaries</returns>
    Task<List<RouteAggregationSummary>> GetCurrentAggregationsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 
    /// </summary>
    /// <param name="routeKeys">routeKeys</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns></returns>
    Task<List<RouteAggregationSummary>> GetRouteAggregationsAsync(
        List<string> routeKeys,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets lightweight metadata for all route aggregations
    /// Much faster than GetCurrentAggregationsAsync as it only fetches timestamps and keys
    /// Used for efficient filtering before fetching full aggregations
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of route metadata</returns>
    Task<List<RouteMetadata>> GetRouteMetadataListAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets aggregation for a specific route
    /// </summary>
    /// <param name="routeKey">Composite route key: FromTime:ToTime:FromOfficeId:ToOfficeId</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Route aggregation summary or null if not found</returns>
    Task<RouteAggregationSummary?> GetRouteAggregationAsync(
        string routeKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Updates LastPersistedAt timestamp for route aggregations in Redis
    /// Called after successful persistence to database to track which aggregations have been saved
    /// </summary>
    /// <param name="routeKeys">List of route keys to update</param>
    /// <param name="persistedAt">Timestamp when aggregations were persisted</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateLastPersistedAtAsync(
        List<string> routeKeys,
        DateTime persistedAt,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Persists route aggregations from Redis to database using LastPersistedAt for change detection
    /// Called by snapshot job that has access to scoped repositories
    /// Only persists aggregations that have changed since last snapshot
    /// Also persists associated order details to route_aggregation_orders table
    /// </summary>
    /// <param name="routeAggregationRepository">Scoped repository for RouteAggregationEntity</param>
    /// <param name="routeOrderRepository">Scoped repository for RouteAggregationOrderEntity</param>
    /// <param name="unitOfWork">Unit of work for saving changes</param>
    /// <param name="snapshotTime">Snapshot timestamp</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of routes persisted</returns>
    Task<int> PersistRouteAggregationsAsync(
        TMS.SharedKernel.Domain.IBaseRepository<TMS.PlanningService.Domain.Entities.RouteAggregationEntity> routeAggregationRepository,
        TMS.SharedKernel.Domain.IBaseRepository<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity> routeOrderRepository,
        TMS.SharedKernel.Domain.IUnitOfWork unitOfWork,
        DateTime snapshotTime,
        CancellationToken cancellationToken = default);
}
