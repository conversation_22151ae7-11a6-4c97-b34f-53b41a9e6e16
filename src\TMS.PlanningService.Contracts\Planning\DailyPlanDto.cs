﻿namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// Daily plan summary for listing
/// </summary>
public class DailyPlanDto
{
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public Guid PlanningTemplateId { get; set; }
    public Guid RouteId { get; set; }
    public DateOnly ExecutionDate { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public double TotalDistance { get; set; }
    public double TotalDuration { get; set; }
    public int OfficeCount { get; set; }
    public Guid VehicleTypeId { get; set; }
    public string VehicleTypeName { get; set; }
    public string RouteCode { get; set; } = string.Empty;
    public string PostOfficeCodes { get; set; } = string.Empty;
    public int PriorityNumber { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? ActualStartTime { get; set; }
    public DateTime? ActualEndTime { get; set; }
    public bool IsActive { get; set; }
    public decimal TotalWeight { get; set; }
    public decimal TotalRealWeight { get; set; }

    /// <summary>
    /// Number of route aggregations in this plan
    /// </summary>
    public int RouteCount { get; set; }

    /// <summary>
    /// Total orders across all routes in this plan
    /// </summary>
    public int TotalOrders { get; set; }

    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public List<DailyPlanPostOfficeDto> PostOfficeDtos { get; set; }
    public decimal TotalDiffWeight { get; set; }
}

public class DailyPlanPostOfficeDto
{
    public string FromCode { get; set; } = string.Empty;
    public string FromName { get; set; } = string.Empty;
    public string ToCode { get; set; } = string.Empty;
    public string ToName { get; set; } = string.Empty;
}
