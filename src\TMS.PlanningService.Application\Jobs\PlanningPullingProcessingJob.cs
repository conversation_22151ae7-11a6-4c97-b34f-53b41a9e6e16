﻿using System.Collections.Concurrent;
using MediatR;
using Microsoft.Extensions.Logging;
using Quartz;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Jobs;

[DisallowConcurrentExecution]
public class PlanningPullingProcessingJob : IJob
{
    private readonly IMediator _mediator;
    private readonly ILogger<PlanningPullingProcessingJob> _logger;
    private readonly IPmsPullingService _pmsPullingService;

    private static readonly ConcurrentDictionary<Type, Func<object, DateTime>> LastUpdatedGetters = new();
     
    public PlanningPullingProcessingJob(
        IMediator mediator,
        ILogger<PlanningPullingProcessingJob> logger,
        IPmsPullingService pmsPullingService
        )
    {
        _mediator = mediator;
        _logger = logger;
        _pmsPullingService = pmsPullingService;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        try
        {
            var cancellationToken = context.CancellationToken;
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            //Sync Lead Time
            _logger.LogInformation("Sync lead time started...");
            var stats = await _pmsPullingService.SynLeadTimeAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {LeadTime}: Processed {TotalProcessed}, Inserted {TotalInserted}, Updated {TotalUpdated} in {ElapsedMs}ms",
                nameof(LeadTime), stats.TotalProcessed, stats.TotalInserted, stats.TotalUpdated, stopwatch.ElapsedMilliseconds);

            //Sync Service type
            _logger.LogInformation("Syncing service types...");
            var serviceTypeCount = await _pmsPullingService.SyncServiceTypeAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {Count} service types", serviceTypeCount);

            // Sync Extra Service
            _logger.LogInformation("Syncing extra service...");
            var extraServiceCount = await _pmsPullingService.SyncExtraServiceAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {Count} extra service", extraServiceCount);

            // Sync Extra Service
            _logger.LogInformation("Syncing order status...");
            var orderStatusCount = await _pmsPullingService.SyncOrderStatusAsync(cancellationToken);
            _logger.LogInformation("✓ Synced {Count} order status", orderStatusCount);

            // Sync Priority Plan
            _logger.LogInformation("Syncing priority plan...");
            var priorityPlanCount = await _pmsPullingService.InitialPriorityPlanAsync(cancellationToken);
            _logger.LogInformation("✓ Initial {Count} priority plan", priorityPlanCount);

            stopwatch.Stop();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Planning Pulling Job failed");
        }
    }

    // Helper classes
    public class SyncStats
    {
        public int TotalProcessed { get; set; }
        public int TotalInserted { get; set; }
        public int TotalUpdated { get; set; }
    } 
}
