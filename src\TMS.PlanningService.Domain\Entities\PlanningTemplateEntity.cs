﻿using System.ComponentModel.DataAnnotations.Schema;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Domain.Entities;

public class PlanningTemplateEntity : AuditableSoftDeleteEntity
{
    [Column("company_id")]
    public Guid CompanyId { get; set; }

    [Column("route_id")]
    public Guid RouteId { get; set; }

    [Column("code")]
    public string? Code { get; set; }

    [Column("name")]
    public string Name { get; set; } = string.Empty;

    [Column("total_distance")]
    public double TotalDistance { get; set; }

    [Column("total_duration")]
    public double TotalDuration { get; set; }

    [Column("office_count")]
    public int OfficeCount { get; set; }

    [Column("search_value")]
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public string? SearchValue { get; set; }

    [Column("priority_number")]
    public int PriorityNumber { get; set; } = 1;

    [Column("vehicle_type_id")]
    public Guid VehicleTypeId { get; set; }

    [Column("route_code")]
    public string RouteCode { get; set; }

    [Column("post_office_codes")]
    public string PostOfficeCodes { get; set; }

    [Column("is_active")]
    public bool IsActive { get; set; } = true;

    // Navigation property
    public ICollection<PlanningTemplateDetailEntity> Details { get; set; } = new List<PlanningTemplateDetailEntity>();
}
