﻿using Refit;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Provinces;
using TMS.PlanningService.Contracts.Routes;
using TMS.PlanningService.Contracts.Wards;

namespace TMS.PlanningService.ApiClient;
public interface IRouteServiceApi
{

    [Get("/api/v1/routes/{id}")]
    Task<RouteDto> GetRoutesByIdAsync(Guid id);

    [Post("/api/v1/routes/by-ids")]
    Task<IEnumerable<RouteDto>> GetRoutesByIdsAsync([Body] IEnumerable<Guid> ids);

    [Post("/api/v1/routes/by-codes")]
    Task<IEnumerable<RouteDto>> GetRoutesByCodesAsync([Body] IEnumerable<string> codes);

    [Post("/api/v1/postoffices/by-codes")]
    Task<IEnumerable<PostOfficeDto>> GetPostOfficesByCodesAsync([Body] IEnumerable<string> codes);

    [Post("/api/v1/postoffices/by-ids")]
    Task<IEnumerable<PostOfficeDto>> GetPostOfficesByIdsAsync([Body] IdsRequest ids);

    [Post("/api/v1/provinces/by-ids")]
    Task<IEnumerable<ProvinceDto>> GetProvincesByIds([Body] IdsRequest ids);

    [Post("/api/v1/wards/by-ids")]
    Task<IEnumerable<WardDto>> GetWardsByIds([Body] IdsRequest ids);


    
}
