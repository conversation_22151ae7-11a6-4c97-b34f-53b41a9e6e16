﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TMS.PlanningService.Domain.Enum;

public enum PriorityRouteAggregationOrderStatus
{
    OnTime,
    AlmostDue,
    Overdue
}

public enum PriorityRouteAggregationSortOrder
{
    TotalOrderAcs,
    TotalOrderDesc,
    AlmostDueOrderAcs,
    AlmostDueOrderDesc,
    OverDueOrderAcs,
    OverDueOrderDesc,
    WeightAsc,
    WeightDesc,
}

public enum OrderParentType
{
    CTTAIKIEN
}
