﻿using MediatR;
using Entities = TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.DeletePriorityPlan;

public class DeletePriorityPlanCommandHandler : IRequestHandler<DeletePriorityPlanCommand, Unit>
{
    private readonly IBaseRepository<Entities.Metadata.PriorityPlan> _priorityPlanRepository;
    private readonly IUnitOfWork _unitOfWork;

    public DeletePriorityPlanCommandHandler(
        IBaseRepository<Entities.Metadata.PriorityPlan> priorityPlanRepository,
        IUnitOfWork unitOfWork)
    {
        _priorityPlanRepository = priorityPlanRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Unit> Handle(DeletePriorityPlanCommand request, CancellationToken cancellationToken)
    {
        var entity = await _priorityPlanRepository.GetByIdAsync(request.Id, cancellationToken);
        if (entity is null)
            throw new BusinessRuleValidationException(nameof(Entities.Metadata.PriorityPlan.Id), CommonErrorCodes.MS018);

        _priorityPlanRepository.Remove(entity);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
