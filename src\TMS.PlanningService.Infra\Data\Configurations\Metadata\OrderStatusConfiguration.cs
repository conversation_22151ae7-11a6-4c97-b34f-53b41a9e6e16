﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;
namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class OrderStatusConfiguration : IEntityTypeConfiguration<OrderStatus>
{
    public void Configure(EntityTypeBuilder<OrderStatus> builder)
    {
        builder.ToTable("order_status");

        builder.<PERSON><PERSON><PERSON>(o => new { o.StatusId });

        builder.Property(o => o.StatusId)
            .IsRequired()
            .HasMaxLength(50)
            .HasColumnName("status_id");

        builder.Property(o => o.StatusName)
            .HasMaxLength(250)
            .HasColumnName("status_name");

        builder.Property(o => o.StatusNameTracking)
            .HasMaxLength(250)
            .HasColumnName("status_name_tracking");

        builder.Property(o => o.StatusGroupId)
             .HasMaxLength(50)
             .HasColumnName("status_group_id");

        builder.Property(o => o.IsDataForMCC)
             .HasColumnName("is_data_for_mcc");
    }
}
