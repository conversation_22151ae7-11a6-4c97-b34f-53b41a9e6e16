﻿using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.PriorityPlan;

public class CreatePriorityPlanRequest
{
    public string? PriorityPlanName { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<CreatePriorityPlanGroupRequest> PriorityPlanGroups { get; set; } = new List<CreatePriorityPlanGroupRequest>();
}

public class CreatePriorityPlanGroupRequest
{
    public LogicalOperator? LogicOperator { get; set; }
    public List<CreatePriorityPlanGroupAttrRequest> PriorityPlanGroupAttributes { get; set; } = new List<CreatePriorityPlanGroupAttrRequest>();
}

public class CreatePriorityPlanGroupAttrRequest
{
    public PriorityType PropertyType { get; set; }
    public LocationType? LocationType { get; set; }
    public PriorityTypeOperator PropertyOperator { get; set; }
    public string? Values { get; set; }
    public LogicalOperator? LogicOperator { get; set; }
}

