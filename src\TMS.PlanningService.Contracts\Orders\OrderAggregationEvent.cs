using TMS.SharedKernal.Kafka.Abstractions;

namespace TMS.PlanningService.Contracts.Orders;

/// <summary>
/// Kafka event from OrderService containing aggregated order data for planning
/// </summary>
public class OrderAggregationEvent : IMessage
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// List of office summaries for planning
    /// </summary>
    public List<OrderSummaryByOffice> OfficeSummaries { get; set; } = new();

    /// <summary>
    /// Analysis period start date
    /// </summary>
    public DateTime? StartDate { get; set; }

    /// <summary>
    /// Analysis period end date
    /// </summary>
    public DateTime? EndDate { get; set; }

    /// <summary>
    /// Redis cache key for retrieving detailed data (if needed)
    /// </summary>
    public string CacheKey { get; set; } = string.Empty;

    /// <summary>
    /// Priority for planning processing
    /// </summary>
    public int Priority { get; set; }
}
