﻿using MediatR;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.PlanningTemplate;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Queries.GetPlanningTemplatesByRouteId;

public class GetPlanningTemplatesByRouteIdQueryHandler : IRequestHandler<GetPlanningTemplatesByRouteIdQuery, List<PlanningTemplateByRouteIdDto>>
{
    private readonly IBaseRepository<PlanningTemplateEntity> _repository;
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IExternalDataService _externalDataService;

    public GetPlanningTemplatesByRouteIdQueryHandler(
    IBaseRepository<PlanningTemplateEntity> repository,
    IExternalDataService externalDataService,
    ICurrentFactorProvider currentFactorProvider)
    {
        _repository = repository;
        _currentFactorProvider = currentFactorProvider;
        _externalDataService = externalDataService;
    }

    public async Task<List<PlanningTemplateByRouteIdDto>> Handle(GetPlanningTemplatesByRouteIdQuery request, CancellationToken cancellationToken)
    {
        var companyId = _currentFactorProvider.CompanyId;
        var entities = await _repository.FindAsync(
            x => !x.IsDeleted
            && x.CompanyId == companyId
            && x.RouteId == request.RouteId,
            cancellationToken);

        var dtos = entities
            .OrderBy(e => e.PriorityNumber)
            .Select(e => new PlanningTemplateByRouteIdDto
            {
                Id = e.Id,
                Code = e.Code ?? string.Empty,
                Name = e.Name,
                IsActive = e.IsActive,
                PriorityNumber = e.PriorityNumber,
                PostOffices = string.IsNullOrWhiteSpace(e.PostOfficeCodes)
                ? new List<PostOfficeDtos>()
                : e.PostOfficeCodes
                .Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries)
                .Select(code => new PostOfficeDtos
                {
                    Code = code,
                    Name = string.Empty
                })
                .ToList()
            }).ToList();

        await _externalDataService.EnrichWithPlanningTemplateByRouteAsync(dtos);

        return dtos;
    }
}
