﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FluentValidation" Version="11.9.0" />
    <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="11.9.0" />
    <PackageReference Include="LinqKit" Version="1.3.8" />
    <PackageReference Include="NPOI" Version="2.7.5" />
    <PackageReference Include="Mapster" Version="7.4.0" />
    <PackageReference Include="Mapster.DependencyInjection" Version="1.0.1" />
    <PackageReference Include="Quartz" Version="3.15.0" />
    <PackageReference Include="TMS.SharedKernel.Constants" Version="1.*" />
    <PackageReference Include="TMS.SharedKernal.Kafka" Version="1.*" />
    <PackageReference Include="TMS.SharedKernal.SmoothRedis" Version="1.*" />
    <PackageReference Include="TMS.SharedKernal.Caching" Version="1.*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TMS.PlanningService.ApiClient\TMS.PlanningService.ApiClient.csproj" />
    <ProjectReference Include="..\TMS.PlanningService.Domain\TMS.PlanningService.Domain.csproj" />
    <ProjectReference Include="..\TMS.PlanningService.Infra\TMS.PlanningService.Infra.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="Templates\Template_import_KHM.xlsx">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
