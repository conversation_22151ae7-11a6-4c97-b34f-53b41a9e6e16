﻿using Refit;
using TMS.PlanningService.Contracts.Dto;

namespace TMS.PlanningService.ApiClient;
public interface IFleetServiceApi
{
    [Post("/api/v1/vehicle-types/by-ids")]
    Task<IEnumerable<VehicleTypeDto>> GetVehicleTypesByIdsAsync([Body] GetVehiclesByIdsRequest req);

    [Post("/api/v1/vehicle-types/by-codes")]
    Task<IEnumerable<VehicleTypeDto>> GetVehicleTypesByCodesAsync([Body] IEnumerable<string> codes);
}
