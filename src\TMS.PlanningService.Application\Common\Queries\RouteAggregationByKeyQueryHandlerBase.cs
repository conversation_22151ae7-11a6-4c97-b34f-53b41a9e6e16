﻿using System.Data.Entity;
using System.Linq;
using LinqKit;
using Mapster;
using MediatR;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.PlanningService.Application.Common.Queries;

/// <summary>
/// Base class for GetByKey query handlers that fetch a single route aggregation with paginated order details
/// Implements Redis-first with database fallback pattern
/// Eliminates code duplication between GetRouteAggregationByKeyQueryHandler and GetPriorityRouteAggregationByKeyQueryHandler
/// </summary>
/// <typeparam name="TRequest">MediatR request type (query)</typeparam>
/// <typeparam name="TDto">DTO returned to client (RouteAggregationDto or PriorityRouteAggregationDto)</typeparam>
public abstract class RouteAggregationByKeyQueryHandlerBase<TRequest, TDto>
    where TRequest : IRequest<TDto?>
{
    protected abstract ILogger Logger { get; }
    protected abstract IRouteServiceApi RouteServiceApi { get; }
    protected abstract IMetadataCacheService MetadataCacheService { get; }
    protected abstract IBaseRepository<RouteAggregationEntity> RouteAggregationRepository { get; }
    protected abstract IBaseRepository<RouteAggregationOrderEntity> RouteAggregationOrderRepository { get; }

    /// <summary>
    /// Returns the aggregation type discriminator for database queries
    /// "normal" for standard routes, "priority" for priority routes
    /// </summary>
    protected abstract string AggregationType { get; }

    protected virtual bool IncludeChildMailer => true;

    /// <summary>
    /// Main execution method - orchestrates the Redis-first + DB fallback flow
    /// </summary>
    protected async Task<TDto?> ExecuteAsync(
        string routeKey,
        int pageNumber,
        int pageSize,
        string? searchTerm,
        CancellationToken cancellationToken)
    {
        try
        {
            Logger.LogInformation("Start fetching route aggregation for RouteKey: {RouteKey}", routeKey);

            // Step 1: Try Redis first with pagination
            Logger.LogDebug("Attempting to fetch from Redis for RouteKey: {RouteKey}", routeKey);
            var (summary, orderDetails) = await FetchFromRedisAsync(routeKey, pageNumber, pageSize, searchTerm, cancellationToken);

            // Step 2: Fallback to database if Redis returns null
            if (summary == null && (orderDetails == null || !orderDetails.Items.Any()))
            {
                Logger.LogWarning("No data found in Redis for RouteKey: {RouteKey}. Fetching from database...", routeKey);
                (summary, orderDetails) = await FetchFromDatabaseAsync(routeKey, pageNumber, pageSize, searchTerm, cancellationToken);
            }

            // Step 3: Return null if not found in either source
            if (summary == null)
            {
                Logger.LogError("Route aggregation not found in both Redis and database for RouteKey: {RouteKey}", routeKey);
                return default;
            }

            // Step 4: Fetch post office data for enrichment
            Logger.LogDebug("Fetching Post Office data for RouteKey: {RouteKey}", routeKey);
            var postOffices = await FetchPostOfficeDataAsync(new List<RouteAggregationSummary> { summary });

            // Step 5: Map to DTO with paginated details
            Logger.LogDebug("Mapping to DTO for RouteKey: {RouteKey}", routeKey);
            var dto = await MapToDtoAsync(summary, postOffices, orderDetails, cancellationToken);

            Logger.LogInformation("Successfully fetched and mapped route aggregation for RouteKey: {RouteKey}", routeKey);
            return dto;
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error occurred while fetching route aggregation for RouteKey: {RouteKey}", routeKey);
            return default;
        }
    }

    /// <summary>
    /// Fetches route aggregation summary from Redis cache
    /// Override in derived classes to use specific aggregation service
    /// </summary>
    protected abstract Task<RouteAggregationSummary?> GetRouteAggregationFromRedisAsync(
        string routeKey,
        CancellationToken cancellationToken);

    /// <summary>
    /// Fetches route aggregation and order details from Redis cache with pagination.
    /// </summary>
    private async Task<(RouteAggregationSummary?, PagedResult<OrderItemDetail>?)> FetchFromRedisAsync(
        string routeKey,
        int pageNumber,
        int pageSize,
        string? searchTerm,
        CancellationToken cancellationToken)
    {
        var summary = await GetRouteAggregationFromRedisAsync(routeKey, cancellationToken);

        if (summary == null)
        {
            Logger.LogWarning("[FetchFromRedisAsync] No route summary found in Redis for RouteKey: {RouteKey}", routeKey);
            return (null, null);
        }

        if (summary.TotalOrders <= 0 || summary.OrderDetails == null || summary.OrderDetails.Count == 0)
        {
            Logger.LogInformation("[FetchFromRedisAsync] Route {RouteKey} found but contains no order details.", routeKey);
            return (summary, new PagedResult<OrderItemDetail>(new List<OrderItemDetail>(), 0, pageNumber, pageSize));
        }

        // Apply search filter and pagination
        var query = summary.OrderDetails.AsQueryable();
         
        if (!string.IsNullOrWhiteSpace(searchTerm))
        {   
            var normalized = searchTerm.Trim();
            // Search supports:
            // 1. Exact MailerId match (dictionary key)
            // 2. ChildMailerId match (searches within Value.Items list)
            query = query.Where(x =>
                x.Key == normalized || // Exact MailerId match
                (x.Value.Items != null && x.Value.Items.Any(item => item.ChildMailerId == normalized)) // ChildMailerId match
            );
        }

        var totalItems = query.SelectMany(x => x.Value.Items)
                              .Where(x => IncludeChildMailer || x.MailerId == x.ChildMailerId).Count();

        var items = query.SelectMany(x => x.Value.Items.Where(item => item.IsDeleted == false))
           .Skip((pageNumber - 1) * pageSize)
           .Take(pageSize)
           .ToList();

        Logger.LogInformation(
            "[FetchFromRedisAsync] Fetched {ItemCount}/{TotalItems} orders from Redis for RouteKey: {RouteKey} (Page {Page}/{PageSize})",
            items.Count,
            totalItems,
            routeKey,
            pageNumber,
            pageSize);

        return (summary, new PagedResult<OrderItemDetail>(items, totalItems, pageNumber, pageSize));
    }

    /// <summary>
    /// Fetches route aggregation and order details from the database with pagination.
    /// </summary>
    private async Task<(RouteAggregationSummary?, PagedResult<OrderItemDetail>?)> FetchFromDatabaseAsync(
        string routeKey,
        int pageNumber,
        int pageSize,
        string? searchTerm,
        CancellationToken cancellationToken)
    {
        // Step 1: Query route aggregation entity
        var query = RouteAggregationRepository
            .GetQueryable() 
            .Where(ra => ra.AggregationType == AggregationType && ra.RouteKey == routeKey && ra.TotalOrders > 0);

        var routeEntity = query.FirstOrDefault();
        if (routeEntity == null)
        {
            Logger.LogWarning("[FetchFromDatabaseAsync] No route aggregation found in DB for RouteKey: {RouteKey}", routeKey);
            return (null, null);
        }

        // Step 2: Build predicate for order filtering
        var predicate = PredicateBuilder.New<RouteAggregationOrderEntity>(ro => ro.RouteAggregationId == routeEntity.Id);

        predicate = predicate.And(x => x.IsDeleted == false);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var normalized = searchTerm.Trim();
            // Search supports MailerId OR ChildMailerId match
            predicate = predicate.And(x => (x.MailerId == normalized || x.ChildMailerId == normalized) 
                                        && ( IncludeChildMailer || x.MailerId == x.ChildMailerId));
        }
         
        // Step 3: Query paged orders
        var pagedResult = await RouteAggregationOrderRepository.GetPagedAsync(
            pageNumber,
            pageSize,
            predicate,
            null,
            cancellationToken);

        // Step 4: Map to output models
        var summary = routeEntity.Adapt<RouteAggregationSummary>();
        var orders = pagedResult.Items.Adapt<List<OrderItemDetail>>();
        var pagedOrders = new PagedResult<OrderItemDetail>(orders, pagedResult.TotalCount, pageNumber, pageSize);

        Logger.LogInformation(
            "[FetchFromDatabaseAsync] Fetched {Count}/{Total} orders from DB for RouteKey: {RouteKey} (Page {Page}/{PageSize})",
            orders.Count,
            pagedResult.TotalCount,
            routeKey,
            pageNumber,
            pageSize);

        return (summary, pagedOrders);
    }

    /// <summary>
    /// Maps RouteAggregationSummary and paginated order details to DTO.
    /// Override in derived classes to implement specific DTO mapping logic
    /// </summary>
    /// <param name="summary">Route aggregation summary data</param>
    /// <param name="postOfficeNames">Dictionary of PostOfficeCode => PostOfficeName for O(1) lookup</param>
    /// <param name="orderDetails">Paginated order details</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected abstract Task<TDto> MapToDtoAsync(
        RouteAggregationSummary summary,
        Dictionary<string, string> postOfficeNames,
        PagedResult<OrderItemDetail>? orderDetails,
        CancellationToken cancellationToken);

    /// <summary>
    /// Fetches post office data for the given aggregations.
    /// Returns a dictionary of PostOfficeCode => PostOfficeName for O(1) lookup performance.
    /// First attempts to retrieve from cache, then falls back to API if needed.
    /// </summary>
    protected async Task<Dictionary<string, string>> FetchPostOfficeDataAsync(List<RouteAggregationSummary> aggregations)
    {
        try
        {
            var postOfficeCodes = aggregations
                .SelectMany(a => new[] { a.FromOfficeId, a.ToOfficeId })
                .Where(x => !string.IsNullOrEmpty(x))
                .Distinct()
                .ToList();

            if (!postOfficeCodes.Any())
            {
                return new Dictionary<string, string>();
            }

            // Try to get post offices from cache
            try
            {
                var cachedPostOffices = await MetadataCacheService.GetPostOfficesAsync<PostOfficeDto>(postOfficeCodes);

                if (cachedPostOffices != null && cachedPostOffices.Any())
                {
                    Logger.LogInformation("Retrieved {Count} post offices from cache", cachedPostOffices.Count);

                    var cachedCodes = cachedPostOffices.Keys.ToHashSet();
                    var missingCodes = postOfficeCodes.Where(code => !cachedCodes.Contains(code)).ToList();

                    if (!missingCodes.Any())
                    {
                        // All found in cache - convert to code => name dictionary
                        return cachedPostOffices.Values.ToDictionary(
                            po => po.PostOfficeCode,
                            po => po.PostOfficeName ?? string.Empty);
                    }

                    // Fetch missing codes from API and merge
                    Logger.LogInformation("Found {MissingCount} post offices not in cache, fetching from API", missingCodes.Count);
                    var missingPostOffices = await RouteServiceApi.GetPostOfficesByCodesAsync(missingCodes);

                    // Merge cached and API results into dictionary
                    var result = cachedPostOffices.Values.ToDictionary(
                        po => po.PostOfficeCode,
                        po => po.PostOfficeName ?? string.Empty);

                    foreach (var po in missingPostOffices)
                    {
                        result[po.PostOfficeCode] = po.PostOfficeName ?? string.Empty;
                    }

                    return result;
                }
            }
            catch (Exception cacheEx)
            {
                Logger.LogWarning(cacheEx, "Error fetching post office data from cache, falling back to API");
            }

            // Fallback: Get all post offices from API
            Logger.LogInformation("Fetching all {Count} post offices from API", postOfficeCodes.Count);
            var postOffices = await RouteServiceApi.GetPostOfficesByCodesAsync(postOfficeCodes);
            return postOffices.ToDictionary(
                po => po.PostOfficeCode,
                po => po.PostOfficeName ?? string.Empty);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error fetching post office data from RouteServiceApi");
            return new Dictionary<string, string>();
        }
    }
}
