﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class TransportVehicleTypeConfiguration : IEntityTypeConfiguration<TransportVehicleType>
{
    public void Configure(EntityTypeBuilder<TransportVehicleType> builder)
    {
        builder.ToTable("transport_vehicle_type");

        // Primary Key
        builder.HasKey(x => x.Id)
               .HasName("pk_transport_vehicle_type");

        builder.Property(x => x.Id)
            .HasColumnName("id")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.Name)
            .HasColumnName("name")
            .HasMaxLength(500);
    }
}
