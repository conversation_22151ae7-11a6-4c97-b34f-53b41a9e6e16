namespace TMS.PlanningService.Application.Services.Orders;

/// <summary>
/// Service for removing orders from routes across all aggregation types (normal, daily, priority)
/// Encapsulates the logic for finding routes by MailerId and delegating removal to aggregation services
/// </summary>
public interface IRemoveOrderOnRoute
{
    /// <summary>
    /// Removes order from all route aggregations (normal, daily, priority) by MailerId
    /// </summary>
    /// <param name="mailerId">Mailer ID (Order ID) to remove from routes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if routes were found and order removed successfully, false if no routes found</returns>
    Task<bool> RemoveOrderAsync(string mailerId, CancellationToken cancellationToken = default);
}
