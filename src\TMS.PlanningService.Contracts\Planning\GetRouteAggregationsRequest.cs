﻿using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// Request for getting route aggregation summaries with filtering and pagination
/// </summary>
public record GetRouteAggregationsRequest(
    string? SearchTerm,
    int Page,
    int PageSize,
    List<string>? FromOfficeIds,
    List<string>? ToOfficeIds,
    DateTime? StartPlanFromDate,
    DateTime? EndPlanFromDate,
    DateTime? StartPlanToDate,
    DateTime? EndPlanToDate,
    List<string>? VehicleTypeIds
);

