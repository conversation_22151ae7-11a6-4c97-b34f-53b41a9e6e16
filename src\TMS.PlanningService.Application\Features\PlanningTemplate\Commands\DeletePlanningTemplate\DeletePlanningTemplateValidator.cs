﻿using FluentValidation;
using TMS.SharedKernel.Constants;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.DeletePlanningTemplate;

public class DeletePlanningTemplateValidator : AbstractValidator<DeletePlanningTemplateCommand>
{
    public DeletePlanningTemplateValidator()
    {
        RuleFor(c => c.Id)
           .NotEmpty()
           .WithMessage(string.Format(ValidationMessages.Required, "Id"))
           .WithErrorCode("Id");
    }
}
