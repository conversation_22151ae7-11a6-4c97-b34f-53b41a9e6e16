﻿namespace TMS.PlanningService.Contracts.Dto;

public class LeadTimePartnerDto
{
    public Guid? Id { get; set; }
    public string? LeadTimeId { get; set; }
    public Guid? PartnerId { get; set; } 
    public string? PartnerName { get; set; }
    public TimeSpan? FromTime { get; set; }
    public TimeSpan? ToTime { get; set; }
    public int? FromAddDays { get; set; } = 0;
    public int? ToAddDays { get; set; } = 0;
    public string? SenderPostOffice { get; set; } = string.Empty;
    public string? ReceivePostOffice { get; set; } = string.Empty;
    public string? SenderPostOfficeName { get; set; } = string.Empty;
    public string? ReceivePostOfficeName { get; set; } = string.Empty;
    public string? SenderPostOfficeAddress { get; set; }
    public string? ReceivePostOfficeAddress { get; set; }
    public string? Description { get; set; }

}

