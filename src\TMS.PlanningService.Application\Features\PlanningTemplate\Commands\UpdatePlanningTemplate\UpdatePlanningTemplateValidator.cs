﻿using FluentValidation;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.UpdatePlanningTemplate;
 
public class UpdatePlanningTemplateValidator : AbstractValidator<UpdatePlanningTemplateCommand>
{
    public UpdatePlanningTemplateValidator()
    {
        RuleFor(x => x.ParamRequest.Details)
           .NotNull()
           .NotEmpty()
           .WithMessage("Individual detail cannot be null or empty")
           .Must(items => (items?.Count ?? 0) >= 2)
           .WithMessage("A route must have at least 2 stops");

        // Todo: Add more validation rules for Details if needed like checking for valid PostOfficeId, Time ranges, etc.
    }
}
