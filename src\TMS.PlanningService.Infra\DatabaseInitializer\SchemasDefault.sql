-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS pg_trgm;
CREATE EXTENSION IF NOT EXISTS unaccent;


-- Drop existing tables if they exist
DROP TABLE IF EXISTS mailer_adjust_routes CASCADE;
DROP TABLE IF EXISTS mailer_plan_routes CASCADE;
DROP TABLE IF EXISTS mailer_route_masters CASCADE;
DROP TABLE IF EXISTS mailer_actual_routes CASCADE;
DROP TABLE IF EXISTS lead_time CASCADE;
DROP TABLE IF EXISTS lead_time_partner CASCADE;
DROP TABLE IF EXISTS planning_template CASCADE;
DROP TABLE IF EXISTS planning_template_detail CASCADE;
DROP TABLE IF EXISTS code_sequence CASCADE;
DROP TABLE IF EXISTS real_plans CASCADE;
DROP TABLE IF EXISTS service_type CASCADE;
DROP TABLE IF EXISTS extra_service CASCADE;
DROP TABLE IF EXISTS priority_plan CASCADE;
DROP TABLE IF EXISTS priority_plan_group CASCADE;
DROP TABLE IF EXISTS priority_plan_group_attr CASCADE;
DROP TABLE IF EXISTS order_status CASCADE;
DROP TABLE IF EXISTS route_aggregation_orders CASCADE;
DROP TABLE IF EXISTS route_aggregations CASCADE;
DROP TABLE IF EXISTS lead_time_type CASCADE;
DROP TABLE IF EXISTS transport_method_type CASCADE;
DROP TABLE IF EXISTS transport_vehicle_type CASCADE;

CREATE EXTENSION IF NOT EXISTS unaccent;
CREATE OR REPLACE FUNCTION public.unaccent_lower(input TEXT)
RETURNS TEXT AS $$
BEGIN
    RETURN lower(unaccent(input));
END;
$$ LANGUAGE plpgsql IMMUTABLE;

-- ================================================================================
-- planning_template, planning_template_detail
-- ================================================================================
CREATE TABLE planning_template (
    id uuid DEFAULT uuid_generate_v4(),
    company_id uuid NOT NULL,
    route_id UUID NOT NULL,             
    code VARCHAR(50) NOT NULL,          
    name VARCHAR(255) NOT NULL, 
    total_distance DOUBLE PRECISION NOT NULL,
    total_duration DOUBLE PRECISION NOT NULL,
    office_count INTEGER NOT NULL,
    route_code VARCHAR(150) NULL,
    post_office_codes TEXT NULL,
    search_value VARCHAR(500) GENERATED ALWAYS AS (
       public.unaccent_lower(
        COALESCE(code, '') || ' ' ||
        COALESCE(name, '') || ' ' ||
        COALESCE(route_code, '') || ' ' ||
        COALESCE(post_office_codes, '')
  )
    ) STORED,
    priority_number INTEGER NOT NULL DEFAULT 1,
    vehicle_type_id UUID NOT NULL,      
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at timestamp with time zone NOT NULL,
    created_by uuid NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    updated_by uuid NOT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    CONSTRAINT pk_planning_template PRIMARY KEY (id)
);

CREATE TABLE planning_template_detail (
    id uuid DEFAULT uuid_generate_v4(),
    planning_template_id UUID NOT NULL,
    post_office_id UUID NOT NULL,
    post_office_code VARCHAR(50) NOT NULL,
    from_time TIME NOT NULL,                             
    from_add_days INT NOT NULL DEFAULT 0,
    to_time TIME NOT NULL,                        
    to_add_days INT NOT NULL DEFAULT 0,
    business_operation INT NOT NULL,           
    distance_between_points REAL NOT NULL,
    step_number INT NOT NULL,
    CONSTRAINT pk_planning_template_detail PRIMARY KEY (id),
    CONSTRAINT fk_planning_template FOREIGN KEY (planning_template_id) 
        REFERENCES planning_template(id) ON DELETE CASCADE
);

-- planning_template table indexes
CREATE INDEX idx_planning_template_company_id ON planning_template (company_id);
CREATE INDEX idx_planning_template_search_value ON planning_template (search_value);
CREATE INDEX idx_planning_template_route_id ON planning_template (route_id);

-- planning_template_detail table indexes
CREATE INDEX idx_planning_template_detail_company_id ON planning_template_detail (planning_template_id);
CREATE INDEX idx_planning_template_detail_search_value ON planning_template_detail (post_office_id);

--------------------------------------------------------------------------------
-- Table with composite unique key (Company + Prefix)
CREATE TABLE IF NOT EXISTS code_sequence (
    company_id UUID NOT NULL,
    prefix TEXT NOT NULL,
    last_number INT NOT NULL DEFAULT 0,
    CONSTRAINT PK_code_sequence PRIMARY KEY (company_id, prefix)
);

-- Function: generate a code based on company and prefix
CREATE OR REPLACE FUNCTION generate_code(p_company_id UUID, p_prefix TEXT)
RETURNS TEXT AS $$
DECLARE
    next_num INT;
BEGIN
    -- Ensure row exists for this company + prefix
    INSERT INTO code_sequence(company_id, prefix, last_number)
    VALUES (p_company_id, p_prefix, 0)
    ON CONFLICT (company_id, prefix) DO NOTHING;

    -- Increment and fetch
    UPDATE code_sequence
    SET last_number = last_number + 1
    WHERE company_id = p_company_id AND prefix = p_prefix
    RETURNING last_number INTO next_num;

    -- Return final formatted code: PREFIX + 0001 etc.
    RETURN p_prefix || LPAD(next_num::TEXT, 9, '0');
END;
$$ LANGUAGE plpgsql;

-- trigger Routes
CREATE OR REPLACE FUNCTION set_khm_code()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.code IS NULL THEN
        NEW.code := generate_code(NEW.company_id, 'KHM_');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trg_set_khm_code ON planning_template;
CREATE TRIGGER trg_set_khm_code
BEFORE INSERT ON planning_template
FOR EACH ROW
EXECUTE FUNCTION set_khm_code();

-- ================================================================================
-- CREATE PARTITIONED MAILER ROUTE MASTERS TABLE (PARENT TABLE)
-- ================================================================================
CREATE TABLE mailer_route_masters (
    -- Primary Key (Composite key for mailer identification)
    mailer_id VARCHAR(50) NOT NULL,
    child_mailer_id VARCHAR(50),

    -- Document and Status Information
    current_post_office VARCHAR(10),
    next_post_office VARCHAR(50),
    is_correct_route BOOLEAN NOT NULL DEFAULT FALSE,
    is_full_mailer_plan_route BOOLEAN NOT NULL DEFAULT FALSE,
    is_have_mailer_plan_route BOOLEAN NOT NULL DEFAULT FALSE,
    status VARCHAR(50),

    -- Route Information
    from_post_office_id VARCHAR(10),
    to_post_office_id VARCHAR(10),
    sender_address VARCHAR(500),
    receiver_address VARCHAR(500),
    last_po_in_plan VARCHAR(500),

    -- actual Timing Information
    actual_start_time TIMESTAMPTZ,
    actual_end_time TIMESTAMPTZ,

    -- Plan Timing Information
    plan_start_time TIMESTAMPTZ,
    plan_end_time TIMESTAMPTZ,
    plan_duration_minutes INTEGER NOT NULL DEFAULT 0,

    -- Adjust Timing Information
    adjust_start_time TIMESTAMPTZ,
    adjust_end_time TIMESTAMPTZ,
    adjust_duration_minutes INTEGER NOT NULL DEFAULT 0,

    -- Forwarding and Audit Information
    is_forwarded BOOLEAN NOT NULL DEFAULT FALSE,
    created_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_user_id VARCHAR(50),
    last_update_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    last_update_user VARCHAR(50),

    -- SLA Information
    pos_send_wrong_way  VARCHAR(500),
    current_sla_type VARCHAR(500),
    current_sla_time TIMESTAMPTZ,
    -- ================================================================================
    -- PRIMARY KEY (Partition key included)
    -- ================================================================================
    PRIMARY KEY (mailer_id, child_mailer_id, created_date)

) PARTITION BY RANGE (created_date);

-- ================================================================================
-- CREATE PARTITIONED MAILER PLAN ROUTES TABLE (PARENT TABLE)
-- ================================================================================
CREATE TABLE mailer_plan_routes (
    -- Primary Key 
    mailer_id VARCHAR(50) NOT NULL,
    child_mailer_id VARCHAR(50),
    step INTEGER NOT NULL,

    -- Route Information
    from_post_office_id VARCHAR(10),
    to_post_office_id VARCHAR(10),
    lead_time_id VARCHAR(50),

    -- Transport Information
    transport_provider_type VARCHAR(50),
    transport_provider_type_name VARCHAR(100),
    transport_vehicle_type VARCHAR(50),
    transport_vehicle_type_name VARCHAR(100),
    transport_method_id VARCHAR(10),
    transport_method_name VARCHAR(100),

    -- Timing Information
    from_time TIMESTAMPTZ,
    from_time_delay INTEGER NOT NULL DEFAULT 0,
    to_time TIMESTAMPTZ,
    to_time_delay INTEGER NOT NULL DEFAULT 0,
    add_days INTEGER NOT NULL DEFAULT 0,

    -- Type Information
    type VARCHAR(50),
    type_name VARCHAR(100),
    history_id INTEGER NOT NULL DEFAULT 0,
    previous_step INTEGER,
    lead_time_type_id VARCHAR(50),
    lead_time_type_name VARCHAR(200),
    service_type_id VARCHAR(200),
    service_type_name VARCHAR(500),
    extra_service VARCHAR(150),
    extra_service_name VARCHAR(500),
    -- Partition alignment field (denormalized from parent)
    master_created_date TIMESTAMPTZ NOT NULL,

    -- ================================================================================
    -- PRIMARY KEY AND FOREIGN KEY
    -- ================================================================================
    PRIMARY KEY (mailer_id, child_mailer_id, step, master_created_date),
    FOREIGN KEY (mailer_id, child_mailer_id, master_created_date) REFERENCES mailer_route_masters (mailer_id, child_mailer_id, created_date)
        ON DELETE CASCADE

) PARTITION BY RANGE (master_created_date);

-- ================================================================================
-- CREATE PARTITIONED MAILER ADJUST ROUTES TABLE (PARENT TABLE)
-- ================================================================================
CREATE TABLE mailer_adjust_routes (
   -- Primary Key 
    mailer_id VARCHAR(50) NOT NULL,
    child_mailer_id VARCHAR(50),
    step INTEGER NOT NULL,

    -- Route Information
    from_post_office_id VARCHAR(10),
    to_post_office_id VARCHAR(10),
    lead_time_id VARCHAR(50),

    -- Transport Information
    transport_provider_type VARCHAR(50),
    transport_provider_type_name VARCHAR(100),
    transport_vehicle_type VARCHAR(50),
    transport_vehicle_type_name VARCHAR(100),
    transport_method_id VARCHAR(10),
    transport_method_name VARCHAR(100),

    -- Timing Information
    from_time TIMESTAMPTZ,
    from_time_delay INTEGER NOT NULL DEFAULT 0,
    to_time TIMESTAMPTZ,
    to_time_delay INTEGER NOT NULL DEFAULT 0,
    add_days INTEGER NOT NULL DEFAULT 0,

    -- Type Information
    type VARCHAR(50),
    type_name VARCHAR(100),
    history_id INTEGER NOT NULL DEFAULT 0,
    previous_step INTEGER,
    lead_time_type_id VARCHAR(50),
    lead_time_type_name VARCHAR(200),
    service_type_id VARCHAR(200),
    service_type_name VARCHAR(500),
    extra_service VARCHAR(150),
    extra_service_name VARCHAR(500),
    -- Partition alignment field (denormalized from parent)
    master_created_date TIMESTAMPTZ NOT NULL,

    -- ================================================================================
    -- PRIMARY KEY AND FOREIGN KEY
    -- ================================================================================
    PRIMARY KEY (mailer_id, child_mailer_id, step, master_created_date),
    FOREIGN KEY (mailer_id, child_mailer_id, master_created_date) REFERENCES mailer_route_masters (mailer_id, child_mailer_id, created_date)
        ON DELETE CASCADE

) PARTITION BY RANGE (master_created_date);

-- ================================================================================
-- CREATE PARTITIONED MAILER ACTUAL TABLE (PARENT TABLE)
-- ================================================================================
CREATE TABLE mailer_actual_routes (
    -- Primary Key 
    mailer_id VARCHAR(50) NOT NULL,
    child_mailer_id VARCHAR(50),
    step INTEGER NOT NULL,

    -- Route Information
    from_post_office_id VARCHAR(10),
    to_post_office_id VARCHAR(10),
    lead_time_id VARCHAR(50),

    -- Transport Information
    transport_provider_type VARCHAR(50),
    transport_provider_type_name VARCHAR(100),
    transport_vehicle_type VARCHAR(50),
    transport_vehicle_type_name VARCHAR(100),
    transport_method_id VARCHAR(10),
    transport_method_name VARCHAR(100),

    -- Timing Information
    from_time TIMESTAMPTZ,
    from_time_delay INTEGER NOT NULL DEFAULT 0,
    to_time TIMESTAMPTZ,
    to_time_delay INTEGER NOT NULL DEFAULT 0,
    add_days INTEGER NOT NULL DEFAULT 0,

    -- Type Information
    type VARCHAR(50),
    type_name VARCHAR(100),
    history_id INTEGER NOT NULL DEFAULT 0,
    previous_step INTEGER,
    lead_time_type_id VARCHAR(50),
    lead_time_type_name VARCHAR(200),
    service_type_id VARCHAR(200),
    service_type_name VARCHAR(500),
    extra_service VARCHAR(150),
    extra_service_name VARCHAR(500),
    -- Partition alignment field (denormalized from parent)
    master_created_date TIMESTAMPTZ NOT NULL,

    -- ================================================================================
    -- PRIMARY KEY AND FOREIGN KEY
    -- ================================================================================
    PRIMARY KEY (mailer_id, child_mailer_id, step, master_created_date),
    FOREIGN KEY (mailer_id, child_mailer_id, master_created_date) REFERENCES mailer_route_masters (mailer_id, child_mailer_id, created_date)
        ON DELETE CASCADE

) PARTITION BY RANGE (master_created_date);

-- ================================================================================
-- CREATE PARTITIONED MAILER ROUTE TABLE (PARENT TABLE)
-- ================================================================================
 
CREATE TABLE lead_time (
    lead_time_id VARCHAR(50) NOT NULL,
    lead_time_type_id VARCHAR(50) NOT NULL,
    lead_time_type_name VARCHAR(500), 
    from_office_id VARCHAR(50) NOT NULL,
    from_office_province_id VARCHAR(50),
    to_office_id VARCHAR(50) NOT NULL,
    to_office_province_id VARCHAR(500), 
    to_province_id VARCHAR(50), 
    start_time VARCHAR(20) NOT NULL,
    end_time  VARCHAR(20) NOT NULL,
    cut_off_time VARCHAR(20), 
    add_days  INTEGER NOT NULL DEFAULT 0,
    description VARCHAR(500), 
    created_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    created_user_id VARCHAR(50),
    last_update_user VARCHAR(50),
    last_update_date TIMESTAMPTZ, 
    is_active BOOLEAN NOT NULL DEFAULT FALSE, 
    service_type_id VARCHAR(50),
    mailer_type_id VARCHAR(50),
    extra_service VARCHAR(50), 
    from_time_delay INTEGER NOT NULL DEFAULT 0,
    to_time_delay INTEGER NOT NULL DEFAULT 0, 
    from_weight DECIMAL(18,2),
    to_weight DECIMAL(18,2), 
    is_connect_pick_request BOOLEAN NOT NULL DEFAULT FALSE,
    is_express BOOLEAN NOT NULL DEFAULT FALSE,
    is_international_trip BOOLEAN NOT NULL DEFAULT FALSE,
    connection_type VARCHAR(10), 
    from_zone_id VARCHAR(50),
    to_zone_id VARCHAR(50), 
    from_post_office_type INTEGER NOT NULL DEFAULT 0,
    from_post_office_type_name   VARCHAR(500),
    to_post_office_type INTEGER NOT NULL DEFAULT 0,
    to_post_office_type_name     VARCHAR(500), 
    transport_provider_type VARCHAR(50),
    transport_provider_type_name VARCHAR(500), 
    transport_vehicle_type VARCHAR(50),
    transport_vehicle_type_name  VARCHAR(500), 
    transport_method_id VARCHAR(50),
    transport_method_name VARCHAR(500),

    CONSTRAINT pk_lead_time PRIMARY KEY (lead_time_id)
);

-- ================================================================================
-- CREATE LEAD TIME PARTNER TABLE
-- ================================================================================
 
CREATE TABLE lead_time_partner (
    id UUID,
    lead_time_id VARCHAR(50) NOT NULL,
    partner_id UUID NOT NULL,
    from_time time without time zone NOT NULL,
    from_add_days integer NOT NULL DEFAULT 0,
    to_time time without time zone NOT NULL,
    to_add_days integer NOT NULL DEFAULT 0,
    sender_post_office VARCHAR(50) NOT NULL,
    receive_post_office VARCHAR(50) NOT NULL,
    description VARCHAR(500),
    CONSTRAINT pk_lead_time_partner PRIMARY KEY (id),
    CONSTRAINT fk_lead_time_partner_leadtime FOREIGN KEY (lead_time_id) REFERENCES lead_time (lead_time_id) ON UPDATE CASCADE ON DELETE CASCADE
);

CREATE INDEX idx_lead_time_partner_lead_time_id ON lead_time_partner (lead_time_id);
CREATE INDEX idx_lead_time_partner_partner_id ON lead_time_partner (partner_id);
CREATE INDEX idx_lead_time_partner_sender_post_office ON lead_time_partner (sender_post_office);
CREATE INDEX idx_lead_time_partner_receive_post_office ON lead_time_partner (receive_post_office);

-- ================================================================================
-- CREATE LEAD TIME TYPE TABLE
-- ================================================================================
CREATE TABLE lead_time_type (
    id VARCHAR(50) NOT NULL,
    name VARCHAR(500),
    CONSTRAINT pk_lead_time_type PRIMARY KEY (id)
);

-- ================================================================================
-- CREATE TRANSPORT METHOD TABLE
-- ================================================================================
CREATE TABLE transport_method_type (
    id VARCHAR(50) NOT NULL,
    name VARCHAR(500),
    CONSTRAINT pk_transport_method_type PRIMARY KEY (id)
);

-- ================================================================================
-- CREATE TRANSPORT METHOD TABLE
-- ================================================================================
CREATE TABLE transport_vehicle_type (
    id VARCHAR(50) NOT NULL,
    name VARCHAR(500),
    CONSTRAINT pk_transport_vehicle_type PRIMARY KEY (id)
);

-- ================================================================================
-- CREATE REAL PLANS TABLES
-- Real execution plans generated daily from planning templates at midnight
-- ================================================================================
CREATE TABLE real_plans (
    id UUID DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL,
    planning_template_id UUID NOT NULL,
    route_id UUID NOT NULL,
    execution_date DATE NOT NULL,
    code VARCHAR(50) NOT NULL,
    name VARCHAR(255) NOT NULL,
    total_distance DOUBLE PRECISION NOT NULL DEFAULT 0,
    total_duration DOUBLE PRECISION NOT NULL DEFAULT 0,
    office_count INTEGER NOT NULL DEFAULT 0,
    vehicle_type_id UUID NOT NULL,
    route_code VARCHAR(150) NOT NULL,
    post_office_codes TEXT,
    priority_number INTEGER NOT NULL DEFAULT 1,
    status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    actual_start_time TIMESTAMPTZ,
    actual_end_time TIMESTAMPTZ,
    is_active BOOLEAN NOT NULL DEFAULT TRUE,
    created_at TIMESTAMPTZ NOT NULL,
    created_by UUID NOT NULL,
    updated_at TIMESTAMPTZ NOT NULL,
    updated_by UUID NOT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    total_weight DECIMAL(18,4),
    total_real_weight DECIMAL(18,4),
    total_diff_weight DECIMAL(18,4), 

    CONSTRAINT pk_real_plans PRIMARY KEY (id),
    CONSTRAINT uq_real_plan_template_date UNIQUE (planning_template_id, execution_date),
    CONSTRAINT fk_real_plan_template FOREIGN KEY (planning_template_id)
        REFERENCES planning_template(id) ON DELETE RESTRICT
);

-- ================================================================================
-- CREATE SERVICE TYPE TABLE
-- ================================================================================
CREATE TABLE service_type (
    service_type_id VARCHAR(50) NOT NULL,
    service_type_name VARCHAR(250),
    service_type_status VARCHAR(50), 
    is_active BOOLEAN DEFAULT FALSE,
    CONSTRAINT pk_service_type_id PRIMARY KEY (service_type_id)
);

-- ================================================================================
-- CREATE EXTRA SERVICE TABLE
-- ================================================================================
CREATE TABLE extra_service (
    service_id VARCHAR(50) NOT NULL,
    service_name VARCHAR(250),
    is_active BOOLEAN DEFAULT FALSE,
    CONSTRAINT pk_service_id PRIMARY KEY (service_id)
);

-- ================================================================================
-- CREATE ORDER STATUS TABLE
-- ================================================================================
CREATE TABLE order_status (
    status_id VARCHAR(50) NOT NULL,
    status_name VARCHAR(250),
    status_name_tracking VARCHAR(250),
    status_group_id VARCHAR(50),
    is_data_for_mcc BOOLEAN DEFAULT FALSE,
    CONSTRAINT pk_order_status_id PRIMARY KEY (status_id)
);

-- ================================================================================
-- CREATE ROUTE AGGREGATIONS TABLE
-- Stores current state of route aggregation data from Redis cache
-- Supports multiple aggregation types: normal, daily, priority
-- Upsert pattern: UPDATE existing routes, no historical snapshots
-- ================================================================================
CREATE TABLE route_aggregations (
    -- Primary Key (Simple UUID for easy management)
    id UUID DEFAULT uuid_generate_v4() NOT NULL,

    -- Route identifier
    route_key VARCHAR(100) NOT NULL,

    -- Aggregation type discriminator: 'normal', 'daily', 'priority'
    aggregation_type VARCHAR(20) NOT NULL DEFAULT 'normal',

    -- Daily planning reference (NULL for normal/priority, actual ID for daily)
    daily_planning_id UUID,

    -- Snapshot timestamp - when this record was last updated from Redis
    snapshot_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- Office identifiers
    from_office_id VARCHAR(20) NOT NULL,
    to_office_id VARCHAR(20) NOT NULL,

    -- Planned times
    from_time TIMESTAMPTZ,
    to_time TIMESTAMPTZ,

    -- Actual times (from MailerActualRoute)
    actual_from_time TIMESTAMPTZ,
    actual_to_time TIMESTAMPTZ,

    -- Duration metrics
    total_duration_minutes INTEGER NOT NULL DEFAULT 0,
    average_duration_minutes DOUBLE PRECISION NOT NULL DEFAULT 0,

    -- Time windows
    earliest_start_time TIMESTAMPTZ,
    latest_end_time TIMESTAMPTZ,

    -- Order metrics (from OrderService)
    total_orders INTEGER NOT NULL DEFAULT 0,
    total_items INTEGER NOT NULL DEFAULT 0,
    total_weight DECIMAL(18,2) NOT NULL DEFAULT 0,
    total_real_weight DECIMAL(18,2) NOT NULL DEFAULT 0,
    total_cal_weight DECIMAL(18,2) NOT NULL DEFAULT 0,
    total_diff_weight DECIMAL(18,2) NOT NULL DEFAULT 0,

    -- Priority and optimization
    priority_score INTEGER NOT NULL DEFAULT 0,
    needs_optimization BOOLEAN NOT NULL DEFAULT FALSE,
    priority_plan_id UUID,

    -- Transport breakdowns (JSON serialized)
    transport_provider_breakdown_json TEXT DEFAULT '[]',
    vehicle_type_breakdown_json TEXT DEFAULT '[]',
    transport_method_breakdown_json TEXT DEFAULT '[]',

    -- Original aggregation timestamp from Redis
    aggregated_at TIMESTAMPTZ NOT NULL,

    -- Record creation timestamp
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    from_business_operation INT NOT NULL,

    to_business_operation INT NOT NULL,

    -- ================================================================================
    -- PRIMARY KEY
    -- Simple UUID primary key for easy reference and management
    -- ================================================================================
    PRIMARY KEY (id, created_at)
) PARTITION BY RANGE (created_at);

-- ================================================================================
-- UNIQUE INDEX WITHOUT FK CONSTRAINT
-- ================================================================================
-- PostgreSQL treats NULL != NULL, so standard unique constraint allows duplicate NULLs
--
-- IMPORTANT: NO FOREIGN KEY CONSTRAINT to daily_planning table
-- Reasons:
-- 1. Allows ON CONFLICT to work with standard column names (no COALESCE)
-- 2. No FK validation overhead for normal/priority aggregations (99% of records have NULL)
-- 3. EF Core navigation still works for queries when daily_planning_id has a value
-- 4. Application enforces referential integrity
--
-- SOLUTION for NULL handling:
-- - PostgreSQL 15+: Use NULLS NOT DISTINCT (treats NULL = NULL for uniqueness)
-- - PostgreSQL <15: Use partial indexes (separate index for NULL vs non-NULL)
--
-- For maximum compatibility, we use the PostgreSQL 15+ syntax here.
-- For older versions, run: migrations/drop_daily_planning_fk.sql
-- ================================================================================
CREATE UNIQUE INDEX uq_route_aggregations_unique
ON route_aggregations (
    route_key,
    aggregation_type,
    daily_planning_id,
    created_at
) NULLS NOT DISTINCT;  -- PostgreSQL 15+: treats NULL = NULL for uniqueness

-- NOTE: If using PostgreSQL < 15, replace the above index with:
-- CREATE UNIQUE INDEX uq_route_aggregations_unique_non_null
-- ON route_aggregations (route_key, aggregation_type, daily_planning_id, created_at)
-- WHERE daily_planning_id IS NOT NULL;
--
-- CREATE UNIQUE INDEX uq_route_aggregations_unique_null
-- ON route_aggregations (route_key, aggregation_type, COALESCE(daily_planning_id, '00000000-0000-0000-0000-000000000000'::uuid), created_at)
-- WHERE daily_planning_id IS NULL;

-- ================================================================================
-- CREATE ROUTE AGGREGATION ORDERS TABLE (PARTITIONED)
-- Stores the relationship between route aggregations and orders/mailers
-- Snapshot of orders on a route at the time of aggregation
-- Each snapshot (route_aggregation_id) has its own set of order records
-- Partitioned by created_at for efficient data management
-- ================================================================================
CREATE TABLE route_aggregation_orders (
    -- Primary Key
    id UUID DEFAULT uuid_generate_v4() NOT NULL,

    -- Composite Foreign key to route_aggregations (both parts required for partitioned table)
    route_aggregation_id UUID NOT NULL,
    route_aggregation_created_at TIMESTAMPTZ NOT NULL,

    -- Mailer/Order identifiers
    mailer_id VARCHAR(50) NOT NULL,
    child_mailer_id VARCHAR(50) NOT NULL,

    -- Order status on this route
    status VARCHAR(50),

    -- Order details
    service_type_id VARCHAR(50),
    extra_service VARCHAR(150),
    weight DECIMAL(18,2),
    real_weight DECIMAL(18,2),
    cal_weight DECIMAL(18,2),
    is_deleted boolean NOT NULL DEFAULT false,

    -- Timestamp for partitioning
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),

    -- ================================================================================
    -- PRIMARY KEY (includes partition key for partitioning requirement)
    -- ================================================================================
    PRIMARY KEY (id, created_at),

    -- ================================================================================
    -- FOREIGN KEY (composite FK matching parent's composite PK for proper referential integrity)
    -- ================================================================================
    CONSTRAINT fk_route_aggregation_orders_route
        FOREIGN KEY (route_aggregation_id, route_aggregation_created_at)
        REFERENCES route_aggregations(id, created_at)
        ON DELETE CASCADE
) PARTITION BY RANGE (created_at);

-- ================================================================================
-- CREATE INDEXES FOR ROUTE AGGREGATION ORDERS TABLE
-- ================================================================================
CREATE INDEX ix_route_aggregation_orders_route_id ON route_aggregation_orders (route_aggregation_id);
CREATE INDEX ix_route_aggregation_orders_mailer ON route_aggregation_orders (mailer_id, child_mailer_id);
CREATE INDEX ix_route_aggregation_orders_status ON route_aggregation_orders (status);
-- Composite index including both FK columns for optimal join performance with partitioned parent table
CREATE INDEX ix_route_aggregation_orders_route_status ON route_aggregation_orders (route_aggregation_id, route_aggregation_created_at, status);

-- Unique constraint for upsert operations - ensures each order appears only once per snapshot
-- Includes partition key (created_at) as required by PostgreSQL for partitioned tables
CREATE UNIQUE INDEX ix_route_aggregation_orders_unique_order
ON route_aggregation_orders (route_aggregation_id, route_aggregation_created_at, mailer_id, child_mailer_id, created_at);

-- ================================================================================
-- CREATE PARTITIONS FOR ROUTE AGGREGATION ORDERS TABLE
-- ================================================================================

-- 2025 Partitions (starting from September)
CREATE TABLE route_aggregation_orders_2025_09 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');

CREATE TABLE route_aggregation_orders_2025_10 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');

CREATE TABLE route_aggregation_orders_2025_11 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');

CREATE TABLE route_aggregation_orders_2025_12 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- 2026 Partitions
CREATE TABLE route_aggregation_orders_2026_01 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

CREATE TABLE route_aggregation_orders_2026_02 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-02-01') TO ('2026-03-01');

CREATE TABLE route_aggregation_orders_2026_03 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-03-01') TO ('2026-04-01');

CREATE TABLE route_aggregation_orders_2026_04 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-04-01') TO ('2026-05-01');

CREATE TABLE route_aggregation_orders_2026_05 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-05-01') TO ('2026-06-01');

CREATE TABLE route_aggregation_orders_2026_06 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-06-01') TO ('2026-07-01');

CREATE TABLE route_aggregation_orders_2026_07 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-07-01') TO ('2026-08-01');

CREATE TABLE route_aggregation_orders_2026_08 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-08-01') TO ('2026-09-01');

CREATE TABLE route_aggregation_orders_2026_09 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-09-01') TO ('2026-10-01');

CREATE TABLE route_aggregation_orders_2026_10 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-10-01') TO ('2026-11-01');

CREATE TABLE route_aggregation_orders_2026_11 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-11-01') TO ('2026-12-01');

CREATE TABLE route_aggregation_orders_2026_12 PARTITION OF route_aggregation_orders
FOR VALUES FROM ('2026-12-01') TO ('2027-01-01');

-- Default partition for any dates outside the defined ranges
CREATE TABLE route_aggregation_orders_default PARTITION OF route_aggregation_orders DEFAULT;

-- ================================================================================
-- CREATE PRIORITY PLAN TABLE
-- CREATE PRIORITY PLAN GROUP TABLE
-- CREATE PRIORITY PLAN GROUP ATTR TABLE
-- ================================================================================
CREATE TABLE priority_plan (
    id uuid DEFAULT uuid_generate_v4(),
    company_id uuid NOT NULL,
    priority_plan_name VARCHAR(50) NOT NULL,
    description VARCHAR(250),
    is_active BOOLEAN DEFAULT FALSE,
    created_at timestamp with time zone NOT NULL,
    created_by uuid NOT NULL,
    updated_at timestamp with time zone NOT NULL,
    updated_by uuid NOT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    search_value VARCHAR(500) GENERATED ALWAYS AS (
       public.unaccent_lower(
        COALESCE(priority_plan_name, '')
    )) STORED,
    CONSTRAINT pk_priority_plan_id PRIMARY KEY (id)
);

CREATE TABLE priority_plan_group (
    id uuid DEFAULT uuid_generate_v4(),
    priority_plan_id uuid NOT NULL,
    logic_operator INT DEFAULT NULL,
    step_number INT NOT NULL,
    CONSTRAINT pk_priority_plan_group_id PRIMARY KEY (id),
    CONSTRAINT fk_priority_plan FOREIGN KEY (priority_plan_id) REFERENCES priority_plan(id) ON DELETE CASCADE
);

CREATE TABLE priority_plan_group_attr (
    id uuid DEFAULT uuid_generate_v4(),
    priority_plan_group_id UUID NOT NULL,
    property_type INT NOT NULL,
    location_type INT DEFAULT NULL,
    property_operator INT NOT NULL,
    values TEXT,
    logic_operator INT DEFAULT NULL,
    step_number INT NOT NULL,
    CONSTRAINT pk_priority_plan_group_attr_id PRIMARY KEY (id),
    CONSTRAINT fk_priority_plan_group FOREIGN KEY (priority_plan_group_id) REFERENCES priority_plan_group(id) ON DELETE CASCADE
);


-- ================================================================================
-- CREATE INDEXES FOR REAL PLANS TABLES
-- ================================================================================

-- real_plans table indexes
CREATE INDEX idx_real_plan_execution_date
ON real_plans (execution_date, company_id, is_active);

CREATE INDEX idx_real_plan_company_status
ON real_plans (company_id, status, execution_date);

CREATE INDEX idx_real_plan_route
ON real_plans (route_id);

CREATE INDEX idx_real_plan_template
ON real_plans (planning_template_id);

-- ================================================================================
-- CREATE INDIVIDUAL PARTITIONS FOR MAILER ROUTE MASTERS TABLE
-- Starting from 2025-09-01 as requested
-- ================================================================================

-- 2025 Partitions (starting from September)
CREATE TABLE mailer_route_masters_2025_09 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');

CREATE TABLE mailer_route_masters_2025_10 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');

CREATE TABLE mailer_route_masters_2025_11 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');

CREATE TABLE mailer_route_masters_2025_12 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- 2026 Partitions
CREATE TABLE mailer_route_masters_2026_01 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

CREATE TABLE mailer_route_masters_2026_02 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-02-01') TO ('2026-03-01');

CREATE TABLE mailer_route_masters_2026_03 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-03-01') TO ('2026-04-01');

CREATE TABLE mailer_route_masters_2026_04 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-04-01') TO ('2026-05-01');

CREATE TABLE mailer_route_masters_2026_05 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-05-01') TO ('2026-06-01');

CREATE TABLE mailer_route_masters_2026_06 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-06-01') TO ('2026-07-01');

CREATE TABLE mailer_route_masters_2026_07 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-07-01') TO ('2026-08-01');

CREATE TABLE mailer_route_masters_2026_08 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-08-01') TO ('2026-09-01');

CREATE TABLE mailer_route_masters_2026_09 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-09-01') TO ('2026-10-01');

CREATE TABLE mailer_route_masters_2026_10 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-10-01') TO ('2026-11-01');

CREATE TABLE mailer_route_masters_2026_11 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-11-01') TO ('2026-12-01');

CREATE TABLE mailer_route_masters_2026_12 PARTITION OF mailer_route_masters
FOR VALUES FROM ('2026-12-01') TO ('2027-01-01');

-- Default partition for future data
CREATE TABLE mailer_route_masters_default PARTITION OF mailer_route_masters DEFAULT;

-- ================================================================================
-- CREATE INDIVIDUAL PARTITIONS FOR MAILER PLAN ROUTES TABLE
-- Starting from 2025-09-01 as requested
-- ================================================================================

-- 2025 Partitions (starting from September)
CREATE TABLE mailer_plan_routes_2025_09 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');

CREATE TABLE mailer_plan_routes_2025_10 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');

CREATE TABLE mailer_plan_routes_2025_11 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');

CREATE TABLE mailer_plan_routes_2025_12 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- 2026 Partitions
CREATE TABLE mailer_plan_routes_2026_01 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

CREATE TABLE mailer_plan_routes_2026_02 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-02-01') TO ('2026-03-01');

CREATE TABLE mailer_plan_routes_2026_03 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-03-01') TO ('2026-04-01');

CREATE TABLE mailer_plan_routes_2026_04 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-04-01') TO ('2026-05-01');

CREATE TABLE mailer_plan_routes_2026_05 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-05-01') TO ('2026-06-01');

CREATE TABLE mailer_plan_routes_2026_06 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-06-01') TO ('2026-07-01');

CREATE TABLE mailer_plan_routes_2026_07 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-07-01') TO ('2026-08-01');

CREATE TABLE mailer_plan_routes_2026_08 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-08-01') TO ('2026-09-01');

CREATE TABLE mailer_plan_routes_2026_09 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-09-01') TO ('2026-10-01');

CREATE TABLE mailer_plan_routes_2026_10 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-10-01') TO ('2026-11-01');

CREATE TABLE mailer_plan_routes_2026_11 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-11-01') TO ('2026-12-01');

CREATE TABLE mailer_plan_routes_2026_12 PARTITION OF mailer_plan_routes
FOR VALUES FROM ('2026-12-01') TO ('2027-01-01');

-- Default partition for future data
CREATE TABLE mailer_plan_routes_default PARTITION OF mailer_plan_routes DEFAULT;

-- ================================================================================
-- CREATE INDIVIDUAL PARTITIONS FOR MAILER ADJUST ROUTES TABLE
-- Starting from 2025-09-01 as requested
-- ================================================================================

-- 2025 Partitions (starting from September)
CREATE TABLE mailer_adjust_routes_2025_09 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');

CREATE TABLE mailer_adjust_routes_2025_10 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');

CREATE TABLE mailer_adjust_routes_2025_11 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');

CREATE TABLE mailer_adjust_routes_2025_12 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- 2026 Partitions
CREATE TABLE mailer_adjust_routes_2026_01 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

CREATE TABLE mailer_adjust_routes_2026_02 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-02-01') TO ('2026-03-01');

CREATE TABLE mailer_adjust_routes_2026_03 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-03-01') TO ('2026-04-01');

CREATE TABLE mailer_adjust_routes_2026_04 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-04-01') TO ('2026-05-01');

CREATE TABLE mailer_adjust_routes_2026_05 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-05-01') TO ('2026-06-01');

CREATE TABLE mailer_adjust_routes_2026_06 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-06-01') TO ('2026-07-01');

CREATE TABLE mailer_adjust_routes_2026_07 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-07-01') TO ('2026-08-01');

CREATE TABLE mailer_adjust_routes_2026_08 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-08-01') TO ('2026-09-01');

CREATE TABLE mailer_adjust_routes_2026_09 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-09-01') TO ('2026-10-01');

CREATE TABLE mailer_adjust_routes_2026_10 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-10-01') TO ('2026-11-01');

CREATE TABLE mailer_adjust_routes_2026_11 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-11-01') TO ('2026-12-01');

CREATE TABLE mailer_adjust_routes_2026_12 PARTITION OF mailer_adjust_routes
FOR VALUES FROM ('2026-12-01') TO ('2027-01-01');

-- Default partition for future data
CREATE TABLE mailer_adjust_routes_default PARTITION OF mailer_adjust_routes DEFAULT;


-- ================================================================================
-- CREATE INDIVIDUAL PARTITIONS FOR MAILER ACTUAL ROUTES TABLE
-- Starting from 2025-09-01 as requested
-- ================================================================================

-- 2025 Partitions (starting from September)
CREATE TABLE mailer_actual_routes_2025_09 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');

CREATE TABLE mailer_actual_routes_2025_10 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');

CREATE TABLE mailer_actual_routes_2025_11 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');

CREATE TABLE mailer_actual_routes_2025_12 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- 2026 Partitions
CREATE TABLE mailer_actual_routes_2026_01 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

CREATE TABLE mailer_actual_routes_2026_02 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-02-01') TO ('2026-03-01');

CREATE TABLE mailer_actual_routes_2026_03 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-03-01') TO ('2026-04-01');

CREATE TABLE mailer_actual_routes_2026_04 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-04-01') TO ('2026-05-01');

CREATE TABLE mailer_actual_routes_2026_05 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-05-01') TO ('2026-06-01');

CREATE TABLE mailer_actual_routes_2026_06 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-06-01') TO ('2026-07-01');

CREATE TABLE mailer_actual_routes_2026_07 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-07-01') TO ('2026-08-01');

CREATE TABLE mailer_actual_routes_2026_08 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-08-01') TO ('2026-09-01');

CREATE TABLE mailer_actual_routes_2026_09 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-09-01') TO ('2026-10-01');

CREATE TABLE mailer_actual_routes_2026_10 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-10-01') TO ('2026-11-01');

CREATE TABLE mailer_actual_routes_2026_11 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-11-01') TO ('2026-12-01');

CREATE TABLE mailer_actual_routes_2026_12 PARTITION OF mailer_actual_routes
FOR VALUES FROM ('2026-12-01') TO ('2027-01-01');

-- Default partition for future data
CREATE TABLE mailer_actual_routes_default PARTITION OF mailer_actual_routes DEFAULT;

-- ================================================================================
-- CREATE INDIVIDUAL PARTITIONS FOR ROUTE AGGREGATIONS TABLE
-- Starting from 2025-09-01 as requested
-- ================================================================================

-- 2025 Partitions (starting from September)
CREATE TABLE route_aggregations_2025_09 PARTITION OF route_aggregations
FOR VALUES FROM ('2025-09-01') TO ('2025-10-01');

CREATE TABLE route_aggregations_2025_10 PARTITION OF route_aggregations
FOR VALUES FROM ('2025-10-01') TO ('2025-11-01');

CREATE TABLE route_aggregations_2025_11 PARTITION OF route_aggregations
FOR VALUES FROM ('2025-11-01') TO ('2025-12-01');

CREATE TABLE route_aggregations_2025_12 PARTITION OF route_aggregations
FOR VALUES FROM ('2025-12-01') TO ('2026-01-01');

-- 2026 Partitions
CREATE TABLE route_aggregations_2026_01 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-01-01') TO ('2026-02-01');

CREATE TABLE route_aggregations_2026_02 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-02-01') TO ('2026-03-01');

CREATE TABLE route_aggregations_2026_03 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-03-01') TO ('2026-04-01');

CREATE TABLE route_aggregations_2026_04 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-04-01') TO ('2026-05-01');

CREATE TABLE route_aggregations_2026_05 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-05-01') TO ('2026-06-01');

CREATE TABLE route_aggregations_2026_06 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-06-01') TO ('2026-07-01');

CREATE TABLE route_aggregations_2026_07 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-07-01') TO ('2026-08-01');

CREATE TABLE route_aggregations_2026_08 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-08-01') TO ('2026-09-01');

CREATE TABLE route_aggregations_2026_09 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-09-01') TO ('2026-10-01');

CREATE TABLE route_aggregations_2026_10 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-10-01') TO ('2026-11-01');

CREATE TABLE route_aggregations_2026_11 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-11-01') TO ('2026-12-01');

CREATE TABLE route_aggregations_2026_12 PARTITION OF route_aggregations
FOR VALUES FROM ('2026-12-01') TO ('2027-01-01');

-- Default partition for future data
CREATE TABLE route_aggregations_default PARTITION OF route_aggregations DEFAULT;

-- ================================================================================
-- CREATE INDEXES ON PARTITIONED TABLES
-- ================================================================================

-- mailer_route_masters table indexes
CREATE INDEX idx_mailer_route_masters_mailer_id ON mailer_route_masters (mailer_id);
CREATE INDEX idx_mailer_route_masters_child_mailer_id ON mailer_route_masters (child_mailer_id);
CREATE INDEX idx_mailer_route_masters_status ON mailer_route_masters (status);
CREATE INDEX idx_mailer_route_masters_created_date ON mailer_route_masters (created_date);
CREATE INDEX idx_mailer_route_masters_from_office ON mailer_route_masters (from_post_office_id);
CREATE INDEX idx_mailer_route_masters_to_office ON mailer_route_masters (to_post_office_id);
CREATE INDEX idx_mailer_route_masters_plan_start ON mailer_route_masters (plan_start_time);
CREATE INDEX idx_mailer_route_masters_plan_end ON mailer_route_masters (plan_end_time);

-- mailer_plan_routes table indexes
CREATE INDEX idx_mailer_plan_routes_mailer_id ON mailer_plan_routes (mailer_id, child_mailer_id, master_created_date);
CREATE INDEX idx_mailer_plan_routes_step ON mailer_plan_routes (step);
CREATE INDEX idx_mailer_plan_routes_from_office ON mailer_plan_routes (from_post_office_id);
CREATE INDEX idx_mailer_plan_routes_to_office ON mailer_plan_routes (to_post_office_id);
CREATE INDEX idx_mailer_plan_routes_type ON mailer_plan_routes (type);
CREATE INDEX idx_mailer_plan_routes_transport_method ON mailer_plan_routes (transport_method_id);

-- mailer_adjust_routes table indexes
CREATE INDEX idx_mailer_adjust_routes_mailer_id ON mailer_adjust_routes (mailer_id, child_mailer_id, master_created_date);
CREATE INDEX idx_mailer_adjust_routes_step ON mailer_adjust_routes (step);
CREATE INDEX idx_mailer_adjust_routes_from_office ON mailer_adjust_routes (from_post_office_id);
CREATE INDEX idx_mailer_adjust_routes_to_office ON mailer_adjust_routes (to_post_office_id);
CREATE INDEX idx_mailer_adjust_routes_type ON mailer_adjust_routes (type);
CREATE INDEX idx_mailer_adjust_routes_transport_method ON mailer_adjust_routes (transport_method_id);

-- mailer_actual_routes table indexes
CREATE INDEX idx_mailer_actual_routes_mailer_id ON mailer_actual_routes (mailer_id, child_mailer_id, master_created_date);
CREATE INDEX idx_mailer_actual_routes_step ON mailer_actual_routes (step);
CREATE INDEX idx_mailer_actual_routes_from_office ON mailer_actual_routes (from_post_office_id);
CREATE INDEX idx_mailer_actual_routes_to_office ON mailer_actual_routes (to_post_office_id);
CREATE INDEX idx_mailer_actual_routes_type ON mailer_actual_routes (type);
CREATE INDEX idx_mailer_actual_routes_transport_method ON mailer_actual_routes (transport_method_id);

-- route_aggregations table indexes
-- Unique index to prevent duplicate routes (must include partition key for partitioned tables)
-- Uses COALESCE to treat NULL daily_planning_id as default UUID for uniqueness
-- This ensures normal/priority aggregations (NULL plan ID) are also unique
CREATE INDEX idx_route_aggregations_aggregation_type ON route_aggregations (aggregation_type);

-- Index for daily planning lookups (only for non-NULL plan IDs)
CREATE INDEX idx_route_aggregations_daily_planning ON route_aggregations (daily_planning_id)
    WHERE daily_planning_id IS NOT NULL;
CREATE INDEX idx_route_aggregations_office_pair ON route_aggregations (from_office_id, to_office_id);
CREATE INDEX idx_route_aggregations_snapshot_at ON route_aggregations (snapshot_at);
CREATE INDEX idx_route_aggregations_from_time ON route_aggregations (from_time);
CREATE INDEX idx_route_aggregations_to_time ON route_aggregations (to_time);
CREATE INDEX idx_route_aggregations_needs_optimization ON route_aggregations (needs_optimization) WHERE needs_optimization = TRUE;

-- ================================================================================
-- CREATE TRIGGERS FOR AUTOMATIC LAST_UPDATE_DATE
-- ================================================================================
CREATE OR REPLACE FUNCTION update_last_update_date_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.last_update_date = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to mailer_route_masters table
CREATE TRIGGER update_mailer_route_masters_last_update_date
    BEFORE UPDATE ON mailer_route_masters
    FOR EACH ROW
    EXECUTE FUNCTION update_last_update_date_column();

-- ================================================================================
-- VERIFY PARTITIONED TABLES
-- ================================================================================
-- View partition information (PostgreSQL version compatible)
SELECT
    schemaname,
    tablename,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM pg_class c2
            JOIN pg_namespace n2 ON c2.relnamespace = n2.oid
            WHERE c2.relname = tablename
            AND n2.nspname = schemaname
            AND c2.relkind = 'p'
        ) THEN 'YES'
        ELSE 'NO'
    END as is_partitioned
FROM pg_tables
WHERE tablename IN ('mailer_route_masters', 'mailer_plan_routes', 'mailer_adjust_routes', 'mailer_actual_routes', 'route_aggregations')
   OR tablename LIKE 'mailer_route_masters_%'
   OR tablename LIKE 'mailer_plan_routes_%'
   OR tablename LIKE 'mailer_adjust_routes_%'
   OR tablename LIKE 'mailer_actual_routes_%'
   OR tablename LIKE 'route_aggregations_%'
ORDER BY tablename;

-- View partition constraints
SELECT
    n.nspname as schemaname,
    c.relname as tablename,
    pg_get_expr(c.relpartbound, c.oid) as partition_bounds,
    CASE c.relkind
        WHEN 'p' THEN 'PARTITIONED TABLE'
        WHEN 'r' THEN 'PARTITION'
        ELSE 'OTHER'
    END as table_type
FROM pg_class c
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE (c.relname LIKE 'mailer_route_masters%' OR c.relname LIKE 'mailer_plan_routes%' OR c.relname LIKE 'mailer_adjust_routes%'OR c.relname LIKE 'mailer_actual_routes%' OR c.relname LIKE 'route_aggregations%')
    AND c.relkind IN ('p', 'r')   -- p = partitioned table, r = regular table/partition
    AND n.nspname = current_schema()
ORDER BY c.relname;

-- Show partition key information (PostgreSQL 10+ compatible)
SELECT
    n.nspname as schema_name,
    c.relname as table_name,
    pg_get_partkeydef(c.oid) as partition_key
FROM pg_class c
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE c.relkind = 'p'   -- partitioned tables only
    AND (c.relname = 'mailer_route_masters' OR c.relname = 'mailer_plan_routes' OR c.relname = 'mailer_adjust_routes' OR c.relname = 'mailer_actual_routes' OR c.relname = 'route_aggregations')
    AND n.nspname = current_schema();

-- ================================================================================
-- PARTITION ALIGNMENT NOTES FOR POSTGRESQL
-- ================================================================================
-- 1. All tables use RANGE partitioning with identical boundaries
-- 2. Child tables include master_created_date field for partition alignment
-- 3. Foreign key constraints maintained across partitions
-- 4. PostgreSQL automatically routes data to correct partitions
-- 5. Default partitions handle data outside defined ranges
-- 6. Constraint exclusion enables partition pruning for performance
-- 7. Partitioning based on created_date for time-based data management
-- 8. Monthly partitions provide optimal balance between performance and maintenance
-- 9. Snake_case naming follows PostgreSQL conventions
-- ================================================================================