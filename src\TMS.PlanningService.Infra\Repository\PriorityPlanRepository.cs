﻿using System.Collections.Generic;
using Microsoft.AspNetCore.Routing.Template;
using Microsoft.EntityFrameworkCore;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.IRepository;
using TMS.PlanningService.Infra.Data;
using TMS.SharedKernel.EntityFrameworkCore;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace TMS.PlanningService.Infra.Repository;
public class PriorityPlanRepository : BaseRepository<PriorityPlan>, IPriorityPlanRepository
{
    public PriorityPlanRepository(ApplicationDbContext context): base(context)
    {
    }

    public Task<List<PriorityPlan>> GetActivePriorityPlanByCompanyIdAsync(Guid CompanyId, CancellationToken cancellationToken = default(CancellationToken))
    {
        return _dbSet.Where(x => x.CompanyId == CompanyId
                            && x.IsActive == true 
                            && x.IsDeleted != true).AsNoTracking()
                    .Include(x => x.PriorityPlanGroups)
                    .ThenInclude(x => x.PriorityAttributes)
                    .ToListAsync(cancellationToken);
    }

    public Task<List<PriorityPlan>> GetActivePriorityPlanAsync(CancellationToken cancellationToken = default(CancellationToken))
    {
        return _dbSet.Where(x => x.IsActive == true
                              && x.IsDeleted != true)
                    .AsNoTracking()
                    .Include(x => x.PriorityPlanGroups)
                    .ThenInclude(x => x.PriorityAttributes)
                    .ToListAsync(cancellationToken);
    }
}
