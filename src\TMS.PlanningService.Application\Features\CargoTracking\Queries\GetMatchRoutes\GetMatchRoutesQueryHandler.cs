﻿using System.Linq.Expressions;
using MediatR;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Features.CargoTracking.Queries.GetCargoTrackingByMailerId;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Features.CargoTracking.Queries.GetCargoTrackingRoutes;

public class GetMatchRoutesQueryHandler : IRequestHandler<GetMatchRoutesQuery, IEnumerable<GetMatchRouteResponse>>
{
    private readonly ILogger<GetCargoTrackingByMailerIdQueryHandler> _logger;
    private readonly IBaseRepository<MailerRouteMasterEntity> _routeMasterRepository;
    
    public GetMatchRoutesQueryHandler(
        IBaseRepository<MailerRouteMasterEntity> routeMasterRepository,
        ILogger<GetCargoTrackingByMailerIdQueryHandler> logger)
    {
        _routeMasterRepository = routeMasterRepository;
        _logger = logger;
    }

    public async Task<IEnumerable<GetMatchRouteResponse>> Handle(GetMatchRoutesQuery request, CancellationToken cancellationToken)
    { 
        // 1.Fetch data from repository with includes
        var mailerRouteMasters = await _routeMasterRepository.FindWithIncludeAsync(
            predicate: x => request.MailerIds.Contains(x.MailerId),
            includes: new Expression<Func<MailerRouteMasterEntity, object>>[]
            {
            o => o.PlanRoutes,
            o => o.ActualRoutes
            });

        if (mailerRouteMasters == null || !mailerRouteMasters.Any())
        {
            _logger.LogWarning("No MailerRouteMaster records found for given MailerIds: {@MailerIds}", request.MailerIds);
            return Enumerable.Empty<GetMatchRouteResponse>();
        }

        _logger.LogInformation("Fetched {Count} MailerRouteMaster records from repository", mailerRouteMasters.Count());

        // 2️.Group by MailerId and compute match route result
        var results = new List<GetMatchRouteResponse>();

        foreach (var group in mailerRouteMasters.GroupBy(x => x.MailerId))
        {
            var main = group.FirstOrDefault();
            if (main == null)
            {
                _logger.LogWarning("Empty group found for MailerId {MailerId}", group.Key);
                continue;
            }

            bool isCorrect = !group.Any(g => g.IsCorrectRoute == false);

            var actualRoutes = main.ActualRoutes?
                .Where(p =>p.MailerId == main.MailerId)
                .OrderBy(p => p.Step)
                .ToList() ?? new List<MailerActualRouteEntity>();

            var planRoutes = main.PlanRoutes?
                .Where(p => p.MailerId == main.MailerId)
                .OrderBy(p => p.Step)
                .ToList() ?? new List<MailerPlanRouteEntity>();

            var actualFirst = actualRoutes.FirstOrDefault(p => p.FromPostOfficeId == main.FromPostOfficeId);
            var planFirst = planRoutes.FirstOrDefault(p => p.FromPostOfficeId == main.FromPostOfficeId);

            // ✅ Only retrieve the data rows of the destination post office from the master data.
            var actualSecondLast = actualRoutes.LastOrDefault(p => p.ToPostOfficeId == main.ToPostOfficeId);
            var planSecondLast = planRoutes.LastOrDefault(p => p.ToPostOfficeId == main.ToPostOfficeId);

            // ✅ Set into response
            var response = new GetMatchRouteResponse
            {
                MailerId = main.MailerId,
                IsCorrectRoute = isCorrect,

                // Get the time from the first element
                ActualStartTime = actualFirst?.FromTime,
                PlanStartTime = planFirst?.FromTime,

                // Get the time from the second-to-last element
                ActualEndTime = actualSecondLast?.ToTime,
                PlanEndTime = planSecondLast?.ToTime
            };

            _logger.LogInformation(
                "Processed MailerId {MailerId}: IsCorrectRoute={IsCorrectRoute}, PlanStart={PlanStart}, PlanEnd={PlanEnd}, ActualStart={ActualStart}, ActualEnd={ActualEnd}",
                response.MailerId,
                response.IsCorrectRoute,
                response.PlanStartTime,
                response.PlanEndTime,
                response.ActualStartTime,
                response.ActualEndTime
            );

            results.Add(response);
        }

        _logger.LogInformation("Completed processing {Count} mailer groups for GetMatchRoutesQuery", results.Count);
        return results;
    }

}
