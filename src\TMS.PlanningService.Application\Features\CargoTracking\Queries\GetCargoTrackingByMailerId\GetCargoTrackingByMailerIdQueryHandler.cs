﻿using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Services.Orders;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Infra.Data;
using TMS.SharedKernal.Caching;

namespace TMS.PlanningService.Application.Features.CargoTracking.Queries.GetCargoTrackingByMailerId;

public class GetCargoTrackingByMailerIdQueryHandler : IRequestHandler<GetCargoTrackingByMailerIdQuery, CargoTrackingResponseDto>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<GetCargoTrackingByMailerIdQueryHandler> _logger;
    private readonly IOrderDataService _orderDataService;
    private readonly IRouteServiceApi _routeServiceApi;
    private readonly IMetadataCacheService _metadataCacheService;
     
    public GetCargoTrackingByMailerIdQueryHandler(
        ApplicationDbContext dbContext,
        ILogger<GetCargoTrackingByMailerIdQueryHandler> logger,
        IOrderDataService orderDataService,
        IRouteServiceApi routeServiceApi, 
        IMetadataCacheService metadataCacheService)
    {
        _dbContext = dbContext;
        _logger = logger;
        _orderDataService = orderDataService;
        _routeServiceApi = routeServiceApi;
        _metadataCacheService = metadataCacheService;
    }

    public async Task<CargoTrackingResponseDto> Handle(
        GetCargoTrackingByMailerIdQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Fetching all route types for MailerId: {MailerId} and ChildMailerId: {ChildMailerId}",
            request.MailerId, request.ChildMailerId);

        // Fetch master route
        var master = await _dbContext.MailerRouteMasters
                     .Where(x =>
                            x.MailerId == request.MailerId &&
                            (
                                string.IsNullOrEmpty(request.ChildMailerId)
                                || (request.MailerId == request.ChildMailerId && x.ChildMailerId == string.Empty)
                                || x.ChildMailerId == request.ChildMailerId
                            )
                     )
                     .FirstOrDefaultAsync(cancellationToken);

        // Fetch plan routes
        var planRoutes = await _dbContext.MailerPlanRoutes
            .Where(x =>
                        x.MailerId == request.MailerId &&
                        (
                            string.IsNullOrEmpty(request.ChildMailerId)
                            || (request.MailerId == request.ChildMailerId && x.ChildMailerId == string.Empty)
                            || x.ChildMailerId == request.ChildMailerId
                        )
                   )
            .OrderBy(x => x.Step)
            .ToListAsync(cancellationToken);

        // Fetch adjust routes
        var adjustRoutes = await _dbContext.MailerAdjustRoutes
             .Where(x =>
                        x.MailerId == request.MailerId &&
                        (
                            string.IsNullOrEmpty(request.ChildMailerId)
                            || (request.MailerId == request.ChildMailerId && x.ChildMailerId == string.Empty)
                            || x.ChildMailerId == request.ChildMailerId
                        )
                   )
            .OrderBy(x => x.Step)
            .ToListAsync(cancellationToken);

        // Fetch actual routes
        var actualRoutes = await _dbContext.MailerActualRoutes
            .Where(x =>
                        x.MailerId == request.MailerId &&
                        (
                            string.IsNullOrEmpty(request.ChildMailerId)
                            || (request.MailerId == request.ChildMailerId && x.ChildMailerId == string.Empty)
                            || x.ChildMailerId == request.ChildMailerId
                        )
                   )
            .OrderBy(x => x.Step)
            .ToListAsync(cancellationToken);

        // Order data
        var metrics = await _orderDataService.GetOrderMetricsByOrderIdsAsync(
            new List<string> { request.MailerId },
            cancellationToken);

        _logger.LogInformation(
            "Found routes for MailerId: {MailerId} and ChildMailerId: {ChildMailerId} - Master: {HasMaster}, Plan: {PlanCount}, Adjust: {AdjustCount}, Actual: {ActualCount}",
            request.MailerId,
            request.ChildMailerId,
            master != null,
            planRoutes.Count,
            adjustRoutes.Count,
            actualRoutes.Count);


        // Map to response DTO
        var planRoutesMaps = planRoutes.Adapt<List<CargoTrackingRouteDto>>();
        var adjustRoutesMaps = adjustRoutes.Adapt<List<CargoTrackingRouteDto>>();
        var actualRoutesMaps = actualRoutes.Adapt<List<CargoTrackingRouteDto>>();
        
        var response = new CargoTrackingResponseDto
        {
            Master = master != null ? new MailerRouteMasterDto
            {
                MailerId = master.MailerId,
                ChildMailerId = master.ChildMailerId,
                CurrentPostOffice = master.CurrentPostOffice,
                IsHaveMailerPlanRoute = master.IsHaveMailerPlanRoute,
                Status = master.Status,
                FromPostOfficeId = master.FromPostOfficeId,
                ToPostOfficeId = master.ToPostOfficeId,
                SenderAddress = master.SenderAddress,
                ReceiverAddress = master.ReceiverAddress,
                PlanStartTime = master.PlanStartTime,
                PlanEndTime = master.PlanEndTime,
                PlanDurationMinutes = master.PlanDurationMinutes,
                AdjustStartTime = master.AdjustStartTime,
                AdjustEndTime = master.AdjustEndTime,
                AdjustDurationMinutes = master.AdjustDurationMinutes,
                IsForwarded = master.IsForwarded,
                CreatedDate = master.CreatedDate,
                CreatedUserId = master.CreatedUserId,
                LastUpdateDate = master.LastUpdateDate,
                LastUpdateUser = master.LastUpdateUser,
                OrderMetrics = metrics.FirstOrDefault(m => m.Key == master.MailerId).Value
            } : null,

            PlanRoutes = planRoutesMaps.ToList(),
            AdjustRoutes = adjustRoutesMaps.ToList(),
            ActualRoutes = actualRoutesMaps.ToList()
        };

        // 1. Combine all PostOfficeIds from the 3 route lists (plan, adjust, actual)
        var postOfficeCodes = planRoutesMaps
            .Concat(adjustRoutesMaps)
            .Concat(actualRoutesMaps)
            .SelectMany(x => new[] { x.FromPostOfficeId, x.ToPostOfficeId }) 
            .Where(id => !string.IsNullOrEmpty(id))                           
            .Distinct()                                                       
            .ToList();

        // 2. Fetch post office data for all IDs in a single API call
        var postOffices = await FetchPostOfficeDataAsync(postOfficeCodes);

        // 3. Only continue if post office data was successfully retrieved
        if (postOffices is { Count: > 0 })
        {
            // Create a lookup dictionary for fast PostOfficeName access by PostOfficeCode
            var postOfficeDict = postOffices.ToDictionary(
                p => p.PostOfficeCode,
                p => p.PostOfficeName
            );

            // 4. Local function to map post office names to a list of routes
            void MapPostOfficeNames(IEnumerable<CargoTrackingRouteDto> routes)
            {
                Parallel.ForEach(routes, route =>
                {
                    if (route is null) return;

                    // Lookup and set the FromPostOffice name
                    if (postOfficeDict.TryGetValue(route.FromPostOfficeId ?? string.Empty, out var fromName))
                        route.FromPostOfficeName = fromName;

                    // Lookup and set the ToPostOffice name
                    if (postOfficeDict.TryGetValue(route.ToPostOfficeId ?? string.Empty, out var toName))
                        route.ToPostOfficeName = toName;
                });
            }

            // 5. Execute mapping in parallel for each route list
            MapPostOfficeNames(planRoutesMaps);
            MapPostOfficeNames(adjustRoutesMaps);
            MapPostOfficeNames(actualRoutesMaps);
        }
         
        return response;
    }

    /// <summary>
    /// Fetches post office data for the given aggregations
    /// Common helper used by MapToDtoAsync implementations
    /// </summary>
    protected async Task<List<PostOfficeDto>> FetchPostOfficeDataAsync(
        List<string> postOfficeCodes)
    {
        try
        {
            if (!postOfficeCodes.Any())
            {
                return new List<PostOfficeDto>();
            }

            // First, try to get post offices from cache
            try
            {
                var cachedPostOffices = await _metadataCacheService.GetPostOfficesAsync<PostOfficeDto>(postOfficeCodes);

                if (cachedPostOffices != null && cachedPostOffices.Any())
                {
                    _logger.LogInformation("Retrieved {Count} post offices from cache", cachedPostOffices.Count);

                    // Check if all required codes are in cache
                    var cachedCodes = cachedPostOffices.Keys.ToHashSet();
                    var missingCodes = postOfficeCodes.Where(code => !cachedCodes.Contains(code)).ToList();

                    if (!missingCodes.Any())
                    {
                        // All post offices found in cache
                        return cachedPostOffices.Values.ToList();
                    }

                    // Some codes are missing, fetch from API and merge
                    _logger.LogInformation("Found {MissingCount} post offices not in cache, fetching from API", missingCodes.Count);
                    var missingPostOffices = await _routeServiceApi.GetPostOfficesByCodesAsync(missingCodes);

                    // Merge cached and API results
                    var allPostOffices = cachedPostOffices.Values.ToList();
                    allPostOffices.AddRange(missingPostOffices);

                    return allPostOffices;
                }
            }
            catch (Exception cacheEx)
            {
                _logger.LogWarning(cacheEx, "Error fetching post office data from cache, falling back to API");
            }

            // Fallback: Get all post offices from API if cache failed or was empty
            _logger.LogInformation("Fetching all {Count} post offices from API", postOfficeCodes.Count);
            var postOffices = await _routeServiceApi.GetPostOfficesByCodesAsync(postOfficeCodes);
            return postOffices.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching post office data from RouteServiceApi");
            return new List<PostOfficeDto>();
        }
    }
}


