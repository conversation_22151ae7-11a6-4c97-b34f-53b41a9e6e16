﻿using System.Linq.Expressions;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Services.Orders;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Orders;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Infra.Data;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Features.CargoTracking.Queries.GetCargoTrackingByMailerId;

public class GetCargoTrackingByMailerIdQueryHandler : IRequestHandler<GetCargoTrackingByMailerIdQuery, Dictionary<string,CargoTrackingResponseDto>>
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<GetCargoTrackingByMailerIdQueryHandler> _logger;
    private readonly IOrderDataService _orderDataService;
    private readonly IRouteServiceApi _routeServiceApi;
    private readonly IMetadataCacheService _metadataCacheService;
    private readonly IBaseRepository<MailerRouteMasterEntity> _routeMasterRepository;
    private readonly IOrderServiceApi _orderServiceApi;

    public GetCargoTrackingByMailerIdQueryHandler(
        ApplicationDbContext dbContext,
        ILogger<GetCargoTrackingByMailerIdQueryHandler> logger,
        IOrderDataService orderDataService,
        IRouteServiceApi routeServiceApi, 
        IMetadataCacheService metadataCacheService,
        IBaseRepository<MailerRouteMasterEntity> routeMasterRepository,
        IOrderServiceApi orderServiceApi)
    {
        _dbContext = dbContext;
        _logger = logger;
        _orderDataService = orderDataService;
        _routeServiceApi = routeServiceApi;
        _metadataCacheService = metadataCacheService;
        _routeMasterRepository = routeMasterRepository;
        _orderServiceApi = orderServiceApi; 
    }

    public async Task<Dictionary<string, CargoTrackingResponseDto>> Handle(
        GetCargoTrackingByMailerIdQuery request,
        CancellationToken cancellationToken)
    {
        // Initialize result dictionary
        var results = new Dictionary<string, CargoTrackingResponseDto>();

        // Fetch all MailerRouteMaster records for the given MailerId
        var mailerRouteMasters = await _dbContext.MailerRouteMasters
            .Where(
                    x =>
                    x.MailerId == request.MailerId &&
                    (
                        string.IsNullOrEmpty(request.ChildMailerId)
                        || (request.MailerId == request.ChildMailerId && x.ChildMailerId == string.Empty)
                        || x.ChildMailerId == request.ChildMailerId
                    )
            )
            .AsNoTracking()
            .ToListAsync(cancellationToken);
         
        // ===================== Fetch order metrics for the MailerId =====================
        var orderMetric = await _orderDataService.GetOrderMetricsByOrderIdsAsync(
            new List<string> { request.MailerId },
            cancellationToken);

        // Initialize dictionary if null
        orderMetric ??= new Dictionary<string, OrderMetrics>();

        if (!orderMetric.ContainsKey(request.MailerId))
        {
            var orders = await _orderServiceApi.GetOrdersByIdsAsync(new List<string> { request.MailerId });

            if (orders?.Any() == true)
            {
                var order = orders.First();
                var metric = order.Adapt<OrderMetrics>();
                // Try to add to dictionary (safe if key already exists)
                orderMetric.TryAdd(order.OrderId, metric);
            }
        }
         
        // Check if any records exist
        if (mailerRouteMasters == null || !mailerRouteMasters.Any())
        {
            _logger.LogWarning(
                "No MailerRouteMaster records found for MailerId: {MailerId}",
                request.MailerId);

            results.TryAdd(
                request.MailerId,
                new CargoTrackingResponseDto
                {
                    Master = new MailerRouteMasterDto
                    {
                        OrderMetrics = orderMetric.TryGetValue(request.MailerId, out var metric) ? metric : null
                    }
                }
            );
            return results; // Return empty dictionary
        }

        // Find the first master route where ChildMailerId is null/empty or matches MailerId
        var firstMasterRoute = mailerRouteMasters.Count() == 1 ? 
            mailerRouteMasters.FirstOrDefault() : //In this case, the lookup will definitely be based on either the ChildMailerId or the order without a ChildMailerId 
            mailerRouteMasters.FirstOrDefault(x => x.ChildMailerId == x.MailerId);
         
        if (firstMasterRoute != null)
        {
            var mailerId = firstMasterRoute.MailerId;
            var childMailerId = string.IsNullOrEmpty(firstMasterRoute.ChildMailerId)
                ? mailerId
                : firstMasterRoute.ChildMailerId;

            _logger.LogInformation(
                "Processing main route for MailerId: {MailerId}",
                mailerId);

             var mailerPairs = new List<(string MailerId, string ChildMailerId)>
            {
                (mailerId, childMailerId)
            };

            var batchResult = await ProcessDataInBatchAsync(
                mailerPairs,
                orderMetric,
                cancellationToken);

            var key = (mailerId, childMailerId);

            if (batchResult.TryGetValue(key, out var dto))
            {
                results.TryAdd(childMailerId, dto);
            }
            else
            {
                _logger.LogWarning(
                    "No data returned for MailerId pair ({MailerId}, {ChildMailerId})",
                    mailerId, childMailerId);
            }
        }
        else
        {
            _logger.LogWarning(
                "No main MailerRouteMaster found for MailerId: {MailerId}",
                request.MailerId);
        }

         
        var incorrectRoutes = mailerRouteMasters
                            .Where(mt => mt.IsCorrectRoute == false)
                            .ToList();

        // Build batch request list
        var batchRequests = incorrectRoutes
            .Where(r => !string.IsNullOrEmpty(r.ChildMailerId))
            .Select(r => (r.MailerId, r.ChildMailerId))
            .ToList();

        var batchResults = await ProcessDataInBatchAsync(batchRequests, orderMetric, cancellationToken);

        // Insert into results using ChildMailerId as key (your business logic)
        foreach (var route in incorrectRoutes)
        {
            if (string.IsNullOrEmpty(route.ChildMailerId))
            {
                _logger.LogWarning(
                    "Skipped route with empty ChildMailerId for MailerId: {MailerId}",
                    route.MailerId);
                continue;
            }

            if (batchResults.TryGetValue((route.MailerId, route.ChildMailerId), out var dto))
            {
                results.TryAdd(route.ChildMailerId, dto);
            }
            else
            {
                _logger.LogWarning(
                    "No batch result found for MailerId={MailerId}, ChildMailerId={ChildMailerId}",
                    route.MailerId, route.ChildMailerId);
            }
        }
         
        return results;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="requests"></param>
    /// <param name="orderMetric"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    protected async Task<Dictionary<(string MailerId, string ChildMailerId), CargoTrackingResponseDto>>
    ProcessDataInBatchAsync(
        List<(string MailerId, string ChildMailerId)> requests,
        Dictionary<string, OrderMetrics> orderMetric,
        CancellationToken cancellationToken)
    {
        var results = new Dictionary<(string, string), CargoTrackingResponseDto>();

        if (requests == null || requests.Count == 0)
            return results;

        // 1️.Extract all parent MailerIds
        var parentMailerIds = requests
            .Select(r => r.MailerId)
            .Distinct()
            .ToList();

        // 2️.Fetch all mailer routes for all MailerIds at once
        var allRouteMasters = await _routeMasterRepository.FindWithIncludeAsync(
            predicate: x => parentMailerIds.Contains(x.MailerId),
            includes: new Expression<Func<MailerRouteMasterEntity, object>>[]
            {
                o => o.PlanRoutes,
                o => o.ActualRoutes,
                o => o.AdjustRoutes
            });

        // Build lookup: (MailerId, ChildMailerId) → Master entity
        var masterLookup = allRouteMasters.ToDictionary(
            x => (x.MailerId, !string.IsNullOrEmpty(x.ChildMailerId) ? x.ChildMailerId : x.MailerId)
        );
           
        // Build a list of all post office IDs from every route
        var allPostOfficeIds = allRouteMasters
             .SelectMany(master =>
                 master.PlanRoutes.Select(p => new { p.FromPostOfficeId, p.ToPostOfficeId })
                .Concat(master.AdjustRoutes.Select(a => new { a.FromPostOfficeId, a.ToPostOfficeId }))
                .Concat(master.ActualRoutes.Select(a => new { a.FromPostOfficeId, a.ToPostOfficeId }))
             )
            .SelectMany(x => new[] { x.FromPostOfficeId, x.ToPostOfficeId })
            .Where(id => !string.IsNullOrEmpty(id))
            .Distinct()
            .ToList();


        // 5️.Fetch all required postoffices in batch
        var postOffices = await FetchPostOfficeDataAsync(allPostOfficeIds);

        var poLookup = postOffices.ToDictionary(
            p => p.PostOfficeCode,
            p => p.PostOfficeName
        );

        // -----------------------------------------------
        // 6️.Process each request
        // -----------------------------------------------
        foreach (var (mailerId, childMailerId) in requests)
        {
            var lookupKey = (mailerId, childMailerId ?? string.Empty);

            if (!masterLookup.TryGetValue(lookupKey, out var master))
            {
                _logger.LogWarning(
                    "No MailerRouteMaster found for MailerId={MailerId} ChildMailerId={ChildMailerId}",
                    mailerId, childMailerId);

                continue;
            }

            // Map routes
            var plan = MapRoutes(master.PlanRoutes.OrderBy(p => p.Step)).ToList();
            var adjust = MapRoutes(master.AdjustRoutes.OrderBy(p => p.Step)).ToList();
            var actual = MapRoutes(master.ActualRoutes.OrderBy(p => p.Step)).ToList();

            // Attach PostOfficeName
            void MapNames(List<CargoTrackingRouteDto> list)
            {
                foreach (var r in list)
                {
                    if (r == null) continue;

                    if (poLookup.TryGetValue(r.FromPostOfficeId ?? "", out var fromName))
                        r.FromPostOfficeName = fromName;

                    if (poLookup.TryGetValue(r.ToPostOfficeId ?? "", out var toName))
                        r.ToPostOfficeName = toName;
                }
            }

            MapNames(plan);
            MapNames(adjust);
            MapNames(actual);

            // Build response
            var dto = new CargoTrackingResponseDto
            {
                Master = master.Adapt<MailerRouteMasterDto>(),
                PlanRoutes = plan,
                AdjustRoutes = adjust,
                ActualRoutes = actual
            };

            // Attach metrics
            if (orderMetric.TryGetValue(mailerId, out var metricValue))
                dto.Master.OrderMetrics = metricValue;

            // Store in dictionary
            results[(mailerId, childMailerId)] = dto;
        }

        return results;
    }
     
    /// <summary>
    /// Fetches post office data for the given aggregations
    /// Common helper used by MapToDtoAsync implementations
    /// </summary>
    protected async Task<List<PostOfficeDto>> FetchPostOfficeDataAsync(
        List<string> postOfficeCodes)
    {
        try
        {
            if (!postOfficeCodes.Any()) return new List<PostOfficeDto>();

            try
            {
                var cachedPostOffices = await _metadataCacheService.GetPostOfficesAsync<PostOfficeDto>(postOfficeCodes);

                if (cachedPostOffices != null && cachedPostOffices.Any())
                {
                    _logger.LogInformation("Retrieved {Count} post offices from cache", cachedPostOffices.Count);

                    // Check if all required codes are in cache
                    var cachedCodes = cachedPostOffices.Keys.ToHashSet();
                    var missingCodes = postOfficeCodes.Where(code => !cachedCodes.Contains(code)).ToList();

                    if (!missingCodes.Any())
                    {
                        // All post offices found in cache
                        return cachedPostOffices.Values.ToList();
                    }

                    // Some codes are missing, fetch from API and merge
                    _logger.LogInformation("Found {MissingCount} post offices not in cache, fetching from API", missingCodes.Count);
                    var missingPostOffices = await _routeServiceApi.GetPostOfficesByCodesAsync(missingCodes);

                    // Merge cached and API results
                    var allPostOffices = cachedPostOffices.Values.ToList();
                    allPostOffices.AddRange(missingPostOffices);

                    return allPostOffices;
                }
            }
            catch (Exception cacheEx)
            {
                _logger.LogWarning(cacheEx, "Error fetching post office data from cache, falling back to API");
            }

            // Fallback: Get all post offices from API if cache failed or was empty
            _logger.LogInformation("Fetching all {Count} post offices from API", postOfficeCodes.Count);
            var postOffices = await _routeServiceApi.GetPostOfficesByCodesAsync(postOfficeCodes);
            return postOffices.ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching post office data from RouteServiceApi");
            return new List<PostOfficeDto>();
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="TSource"></typeparam>
    /// <param name="routes"></param>
    /// <returns></returns>
    private static List<CargoTrackingRouteDto> MapRoutes<TSource>(IEnumerable<TSource> routes)
    {
        return routes?.Adapt<List<CargoTrackingRouteDto>>() ?? new List<CargoTrackingRouteDto>();
    }
     
}


