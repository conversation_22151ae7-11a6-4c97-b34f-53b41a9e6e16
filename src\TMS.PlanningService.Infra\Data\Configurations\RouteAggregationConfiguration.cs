﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Infra.Data.Configurations;

public class RouteAggregationConfiguration : IEntityTypeConfiguration<RouteAggregationEntity>
{
    public void Configure(EntityTypeBuilder<RouteAggregationEntity> builder)
    {
        builder.ToTable("route_aggregations");

        // Composite primary key matching partitioned table schema
        // PostgreSQL partitioned tables require partition key (CreatedAt) in PK
        builder.HasKey(x => new { x.Id, x.CreatedAt });

        builder.Property(x => x.Id)
            .HasColumnName("id")
            .IsRequired();

        builder.Property(x => x.RouteKey)
            .HasColumnName("route_key")
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.AggregationType)
            .HasColumnName("aggregation_type")
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.SnapshotAt)
            .HasColumnName("snapshot_at")
            .IsRequired();

        builder.Property(x => x.DailyPlanningId)
            .HasColumnName("daily_planning_id")
            .IsRequired(false); // Nullable - NULL for normal/priority, actual ID for daily

        builder.Property(x => x.FromOfficeId)
            .HasColumnName("from_office_id")
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.ToOfficeId)
            .HasColumnName("to_office_id")
            .IsRequired()
            .HasMaxLength(20);

        builder.Property(x => x.FromTime)
            .HasColumnName("from_time");

        builder.Property(x => x.ToTime)
            .HasColumnName("to_time");

        builder.Property(x => x.ActualFromTime)
            .HasColumnName("actual_from_time");

        builder.Property(x => x.ActualToTime)
            .HasColumnName("actual_to_time");

        builder.Property(x => x.TotalDurationMinutes)
            .HasColumnName("total_duration_minutes");

        builder.Property(x => x.AverageDurationMinutes)
            .HasColumnName("average_duration_minutes");

        builder.Property(x => x.EarliestStartTime)
            .HasColumnName("earliest_start_time");

        builder.Property(x => x.LatestEndTime)
            .HasColumnName("latest_end_time");

        builder.Property(x => x.TotalOrders)
            .HasColumnName("total_orders");

        builder.Property(x => x.TotalItems)
            .HasColumnName("total_items");

        builder.Property(x => x.TotalWeight)
            .HasColumnName("total_weight")
            .HasPrecision(18, 2);

        builder.Property(x => x.TotalRealWeight)
            .HasColumnName("total_real_weight")
            .HasPrecision(18, 2);

        builder.Property(x => x.TotalCalWeight)
            .HasColumnName("total_cal_weight")
            .HasPrecision(18, 2);

        builder.Property(x => x.TotalDiffWeight)
            .HasColumnName("total_diff_weight")
            .HasPrecision(18, 2);

        builder.Property(x => x.PriorityScore)
            .HasColumnName("priority_score");

        builder.Property(x => x.NeedsOptimization)
            .HasColumnName("needs_optimization");

        builder.Property(x => x.TransportProviderBreakdownJson)
            .HasColumnName("transport_provider_breakdown_json")
            .HasColumnType("jsonb");

        builder.Property(x => x.VehicleTypeBreakdownJson)
            .HasColumnName("vehicle_type_breakdown_json")
            .HasColumnType("jsonb");

        builder.Property(x => x.TransportMethodBreakdownJson)
            .HasColumnName("transport_method_breakdown_json")
            .HasColumnType("jsonb");

        builder.Property(x => x.AggregatedAt)
            .HasColumnName("aggregated_at");

        builder.Property(x => x.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(x => x.PriorityPlanId)
               .HasColumnName("priority_plan_id");

        // Navigation relationship to DailyPlanning (NO database FK constraint)
        // Optional - only set for 'daily' aggregation type
        // NULL for 'normal' and 'priority' aggregations
        //
        // IMPORTANT: The database FK constraint has been manually dropped.
        // This configuration only tells EF Core about the relationship for navigation.
        // Reasons for no DB FK:
        // 1. Allows NULL values to work properly with unique index for ON CONFLICT
        // 2. No FK validation overhead for normal/priority aggregations (99% of records)
        // 3. Application enforces referential integrity through queries
        // 4. EF Core navigation still works for eager/lazy loading when DailyPlanningId has value
        //
        // The FK was dropped using: migrations/drop_daily_planning_fk.sql
        // Future migrations should NOT re-create this FK constraint
        builder.HasOne(x => x.DailyPlanning)
            .WithMany(x => x.RouteAggregations)
            .HasForeignKey(x => x.DailyPlanningId)
            .OnDelete(DeleteBehavior.NoAction)
            .IsRequired(false);

        // Navigation to Orders collection - composite FK relationship
        // RouteAggregationOrderEntity has composite FK (RouteAggregationId, RouteAggregationCreatedAt)
        // matching this entity's composite PK (Id, CreatedAt) for proper referential integrity
        builder.HasMany(x => x.Orders)
            .WithOne(x => x.RouteAggregation)
            .HasForeignKey(x => new { x.RouteAggregationId, x.RouteAggregationCreatedAt })
            .HasPrincipalKey(x => new { x.Id, x.CreatedAt })
            .OnDelete(DeleteBehavior.Cascade);

        // Indexes
        // NOTE: Unique constraint is created via raw SQL with COALESCE to handle NULL daily_planning_id
        // See migrations/fix_route_aggregation_unique_constraint.sql
        // EF Core doesn't support COALESCE in indexes, so we manage it via SQL
        // The constraint: (route_key, aggregation_type, COALESCE(daily_planning_id, '00000000-0000-0000-0000-000000000000'), created_at)

        builder.HasIndex(x => x.AggregationType)
            .HasDatabaseName("ix_route_aggregations_aggregation_type");

        builder.HasIndex(x => x.DailyPlanningId)
            .HasDatabaseName("ix_route_aggregations_daily_planning")
            .HasFilter("daily_planning_id != '00000000-0000-0000-0000-000000000000'");

        builder.HasIndex(x => new { x.FromOfficeId, x.ToOfficeId })
            .HasDatabaseName("ix_route_aggregations_office_pair");

        builder.HasIndex(x => x.SnapshotAt)
            .HasDatabaseName("ix_route_aggregations_snapshot_at");

        builder.Property(x => x.FromBusinessOperation).HasColumnName("from_business_operation");
        builder.Property(x => x.ToBusinessOperation).HasColumnName("to_business_operation");
    }
}
