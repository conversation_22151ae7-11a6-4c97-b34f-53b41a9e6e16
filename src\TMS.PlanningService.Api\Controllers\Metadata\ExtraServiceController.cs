﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.ExtraService.Queries.GetExtraServices;
using TMS.PlanningService.Contracts.Dto;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/extra-services")]
[Produces("application/json")]
public class ExtraServiceController : ControllerBase
{
    private readonly IMediator _mediator;

    public ExtraServiceController(IMediator mediator)
    {
        _mediator = mediator;
    }

    ///// <summary>
    ///// Get all Serviece Types
    ///// </summary> 
    ///// <returns>List of Serviece Types</returns>
    [HttpGet("")]
    [ProducesResponseType(typeof(List<ExtraServiceDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResult<ExtraServiceDto>>> GetExtraServies()
    {
        var query = new GetExtraServicesQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }



}
