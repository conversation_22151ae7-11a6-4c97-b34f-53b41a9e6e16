﻿using Mapster;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Contracts.Orders;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernal.RabbitMq.Abstractions;
using TMS.SharedKernal.SmoothRedis;

namespace TMS.PlanningService.Application.Services.Orders;

/// <summary>
/// Service for fetching order data from Redis to enrich route aggregations
/// Reads order aggregations cached by OrderService
/// </summary>
public class OrderDataService : IOrderDataService
{
    private readonly ILogger<OrderDataService> _logger;
    private readonly ISmoothRedis _redis;
    private readonly IEventPublisher? _eventPublisher;
    private const string ORDER_AGGREGATION_KEY_PREFIX = "order-aggregation:";
    private const int MAX_RETRY_ATTEMPTS = 3;
    private const int RETRY_DELAY_MS = 500;

    public OrderDataService(
        ILogger<OrderDataService> logger,
        ISmoothRedis _redis,
        [FromKeyedServices(Constants.RabbitMqOrderEvents)] IEventPublisher? eventPublisher = null)
    {
        _logger = logger;
        this._redis = _redis;
        _eventPublisher = eventPublisher;
    }

    public async Task<Dictionary<string, OrderMetrics>> GetOrderMetricsByOrderIdsAsync(
        List<string> orderIds,
        CancellationToken cancellationToken = default,
        object? originalPlanningEvent = null)
    {
        var result = new Dictionary<string, OrderMetrics>();

        if (orderIds == null || !orderIds.Any())
        {
            return result;
        }

        try
        {
            _logger.LogWarning("Fetching order metrics for {Count} order IDs", orderIds.Count);

            var distinctOrderIds = orderIds.Distinct().ToList();

            // First attempt: Fetch all order data in a single batch operation (fast path)
            var cacheKeys = distinctOrderIds.Select(orderId => $"{ORDER_AGGREGATION_KEY_PREFIX}{orderId}").ToArray();
            var batchResults = await _redis.Batch.Cache.GetManyAsync<OrderMetrics>(cacheKeys);

            // Process successful fetches
            var notFoundOrderIds = new List<string>();
            foreach (var orderId in distinctOrderIds)
            {
                var cacheKey = $"{ORDER_AGGREGATION_KEY_PREFIX}{orderId}";
                if (batchResults.TryGetValue(cacheKey, out var orderMetrics) && orderMetrics != null)
                {
                    result[orderId] = orderMetrics.Adapt<OrderMetrics>();
                }
                else
                {
                    // Track orders not found for retry
                    notFoundOrderIds.Add(orderId);
                }
            }

            _logger.LogWarning(
                "Batch fetch completed - Found: {Found}/{Total}, NotFound: {NotFound}",
                result.Count,
                distinctOrderIds.Count,
                notFoundOrderIds.Count);

            // Retry logic for orders not found in first attempt
            // This handles eventual consistency scenarios where orders may appear after a delay
            if (notFoundOrderIds.Any())
            {
                _logger.LogWarning("Retrying {Count} orders not found in initial batch", notFoundOrderIds.Count);

                var retryTasks = notFoundOrderIds.Select(async orderId =>
                {
                    var metrics = await FetchOrderMetricsWithRetryAsync(orderId, cancellationToken);
                    return (OrderId: orderId, Metrics: metrics);
                });

                var retryResults = await Task.WhenAll(retryTasks);

                foreach (var (orderId, metrics) in retryResults)
                {
                    if (metrics != null)
                    {
                        result[orderId] = metrics;
                    }
                }
            }

            var successCount = result.Count;
            var notFoundCount = distinctOrderIds.Count - successCount;

            if (notFoundCount > 0)
            {
                var missingOrderIds = distinctOrderIds.Where(id => !result.ContainsKey(id)).ToList();

                _logger.LogWarning(
                    "Order metrics not found for {NotFoundCount}/{Total} order IDs - Orders may not be processed yet",
                    notFoundCount,
                    distinctOrderIds.Count);

                // If we have the original planning event and missing order IDs, send request to OrderService
                if (originalPlanningEvent != null && _eventPublisher != null && missingOrderIds.Any())
                {
                    await HandleMissingOrderMetricsAsync(
                        originalPlanningEvent,
                        missingOrderIds,
                        cancellationToken);
                }
            }

            _logger.LogWarning(
                "Successfully fetched order metrics - Requested: {Requested}, Found: {Found}, NotFound: {NotFound}",
                distinctOrderIds.Count,
                successCount,
                notFoundCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to fetch order metrics for order IDs");
        }

        return result;
    }

    private async Task<OrderMetrics?> FetchOrderMetricsWithRetryAsync(
        string OrderId,
        CancellationToken cancellationToken)
    {
        for (var attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++)
        {
            try
            {
                var cacheKey = $"{ORDER_AGGREGATION_KEY_PREFIX}{OrderId}";
                var orderSummary = await _redis.Cache.GetAsync<OrderMetrics>(cacheKey);

                if (orderSummary != null)
                {
                    return orderSummary.Adapt<OrderMetrics>(); 
                }

                // Order not found - may not be processed yet
                if (attempt < MAX_RETRY_ATTEMPTS)
                {
                    _logger.LogWarning(
                        "Order data not found for MailerId: {MailerId}, attempt {Attempt}/{MaxAttempts}, retrying...",
                        OrderId,
                        attempt,
                        MAX_RETRY_ATTEMPTS);

                    await Task.Delay(RETRY_DELAY_MS * attempt, cancellationToken);
                    continue;
                }

                _logger.LogWarning(
                    "Order data not found for MailerId: {MailerId} after {Attempts} attempts",
                    OrderId,
                    MAX_RETRY_ATTEMPTS);

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex,
                    "Error fetching order metrics for OrderId: {OrderId}, attempt {Attempt}/{MaxAttempts}",
                    OrderId,
                    attempt,
                    MAX_RETRY_ATTEMPTS);

                if (attempt == MAX_RETRY_ATTEMPTS)
                {
                    return null;
                }

                await Task.Delay(RETRY_DELAY_MS * attempt, cancellationToken);
            }
        }

        return null;
    }

    /// <summary>
    /// Handles missing order metrics by sending recalculation request to OrderService
    /// Includes retry limit checking and processing history tracking
    /// OrderService will only send back if caching succeeds, preventing infinite loops
    /// </summary>
    private async Task HandleMissingOrderMetricsAsync(
        object originalPlanningEvent,
        List<string> missingOrderIds,
        CancellationToken cancellationToken)
    {
        try
        {
            // Extract retry information from the original planning event
            int currentAttempts = 0;
            int maxAttempts = 3;
            string eventId = "";
            RabbitMqPlanningEvent? planningEvent = null;

            if (originalPlanningEvent is RabbitMqPlanningEvent pe)
            {
                planningEvent = pe;
                currentAttempts = pe.ProcessingAttempts;
                maxAttempts = pe.MaxProcessingAttempts;
                eventId = pe.Id;

                // CHECK MAX RETRY LIMIT
                if (currentAttempts >= maxAttempts)
                {
                    _logger.LogError(
                        "MAX RETRIES EXCEEDED: Giving up on event {EventId} after {Attempts} attempts. " +
                        "Missing {Count} orders: [{MissingIds}]. " +
                        "Processing history: {History}",
                        eventId,
                        currentAttempts,
                        missingOrderIds.Count,
                        string.Join(", ", missingOrderIds.Take(10)) + (missingOrderIds.Count > 10 ? "..." : ""),
                        string.Join(" | ", pe.ProcessingHistory));

                    // TODO: Send to dead letter queue or monitoring/alerting system
                    // await SendToDeadLetterQueueAsync(pe, missingOrderIds, cancellationToken);

                    return; // Don't retry anymore
                }

                //  INCREMENT RETRY COUNTER AND ADD HISTORY
                planningEvent.ProcessingAttempts++;
                var historyEntry = $"Attempt {planningEvent.ProcessingAttempts}/{maxAttempts} at {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}: " +
                                   $"Missing {missingOrderIds.Count} orders";
                planningEvent.ProcessingHistory.Add(historyEntry);
            }

            // Create and send the recalculation event
            var recalculationEvent = new RequestOrderRecalculationEvent
            {
                OriginalPlanningEvent = originalPlanningEvent,
                MissingOrderIds = missingOrderIds,
                Reason = $"OrderMetrics not found in cache for {missingOrderIds.Count} orders after {MAX_RETRY_ATTEMPTS} retry attempts per order",
                RequestTimestamp = DateTime.UtcNow,
                RetryCount = currentAttempts,
                MaxRetries = maxAttempts,
                OriginalEventId = eventId,
                FirstAttemptTime = currentAttempts == 0 ? DateTime.UtcNow : null
            };

            await _eventPublisher!.PublishAsync(recalculationEvent, cancellationToken: cancellationToken);

            _logger.LogWarning(
                "RETRY {Attempt}/{Max}: Sent RequestOrderRecalculationEvent to OrderService. " +
                "EventId: {RecalcEventId}, OriginalEventId: {OriginalEventId}, " +
                "Missing {Count} orders: [{OrderIds}]",
                currentAttempts + 1,
                maxAttempts,
                recalculationEvent.EventId,
                eventId,
                missingOrderIds.Count,
                string.Join(", ", missingOrderIds.Take(5)) + (missingOrderIds.Count > 5 ? "..." : ""));

            if (planningEvent != null && planningEvent.ProcessingHistory.Any())
            {
                _logger.LogInformation(
                    "Processing history for event {EventId}: {History}",
                    eventId,
                    string.Join(" → ", planningEvent.ProcessingHistory));
            }
        }
        catch (Exception publishEx)
        {
            _logger.LogError(publishEx,
                "Failed to publish RequestOrderRecalculationEvent for {Count} missing orders",
                missingOrderIds.Count);
        }
    }
}
