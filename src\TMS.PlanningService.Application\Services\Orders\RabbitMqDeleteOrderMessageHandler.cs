﻿using Microsoft.Extensions.Logging;
using TMS.PlanningService.Contracts.Orders;
using TMS.SharedKernal.RabbitMq.Abstractions;

namespace TMS.PlanningService.Application.Services.Orders;

public class RabbitMqDeleteOrderMessageHandler : IEventHandler<RabbitMqDeleteOrderEvent>
{
    private readonly ILogger<RabbitMqDeleteOrderMessageHandler> _logger;
    private readonly IRemoveOrderOnRoute _removeOrderOnRoute;

    public RabbitMqDeleteOrderMessageHandler(
        ILogger<RabbitMqDeleteOrderMessageHandler> logger,
        IRemoveOrderOnRoute removeOrderOnRoute)
    {
        _logger = logger;
        _removeOrderOnRoute = removeOrderOnRoute;
    }

    public async Task<bool> HandleAsync(
    RabbitMqDeleteOrderEvent message,
    CancellationToken cancellationToken = default)
    {
        try
        {
            // Validate message input
            if (message?.OrderIds == null || !message.OrderIds.Any())
            {
                _logger.LogWarning("Received RabbitMqDeleteOrderEvent with no OrderIds to process.");
                return true; // Nothing to process, but not a failure
            }

            // Process each OrderId in the message
            foreach (var mailerId in message.OrderIds)
            {
                // Delegate to RemoveOrderOnRoute service for reusable logic
                await _removeOrderOnRoute.RemoveOrderAsync(mailerId, cancellationToken);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to process delete order from RabbitMq - OrderIds: {OrderIds}, CreatedAt: {CreatedAt}, EventType: {EventType}",
                message?.OrderIds,
                message?.CreatedAt,
                message?.EventType);

            return false;
        }
         
        return true;
    }

}
