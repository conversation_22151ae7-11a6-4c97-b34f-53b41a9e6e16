﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;
namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class PriorityPlanGroupConfiguration : IEntityTypeConfiguration<PriorityPlanGroup>
{
    public void Configure(EntityTypeBuilder<PriorityPlanGroup> builder)
    {
        builder.ToTable("priority_plan_group");

        builder.HasKey(o => new { o.Id });

        builder.Property(x => x.Id)
            .HasColumnName("id");

        builder.Property(o => o.PriorityPlanId)
            .IsRequired()
            .HasColumnName("priority_plan_id");

        builder.Property(x => x.LogicOperator)
               .HasColumnName("logic_operator");

        //builder.HasMany(x => x.PriorityProperties)
        //       .WithOne() 
        //       .HasForeign<PERSON>ey(p => p.PriorityGroupId) 
        //       .OnDelete(DeleteBehavior.Cascade);
    }
}
