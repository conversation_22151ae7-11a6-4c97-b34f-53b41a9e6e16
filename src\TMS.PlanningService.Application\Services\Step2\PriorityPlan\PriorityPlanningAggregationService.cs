﻿using Microsoft.Extensions.Logging;
using OpenTelemetry.Resources;
using TMS.PlanningService.Application.Services.Orders;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Orders;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernal.Caching;
using TMS.SharedKernal.SmoothRedis;
using Entities = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Services.Step2.PriorityPlan;

/// <summary>
/// Service for calculating priority planning aggregations filtered by DE (Express) service type
/// Inherits from PlanningAggregationService and overrides filtering and scoring logic
/// All bug fixes from the base class are automatically inherited
/// </summary>
public class PriorityPlanningAggregationService : PlanningAggregationService, IPriorityPlanningAggregationService
{
    private const string DE_SERVICE_TYPE_ID = "DE"; // Express delivery service type
    private readonly IMetadataCacheService _metadataCacheService;

    public PriorityPlanningAggregationService(
        ILogger<PriorityPlanningAggregationService> logger,
        ISmoothRedis redis,
        IOrderDataService orderDataService,
        IMetadataCacheService metadataCacheService)
        : base(logger, redis, orderDataService)
    {
        _metadataCacheService = metadataCacheService;
    }

    // Override Redis keys for priority aggregations
    protected override string RouteAggregationKeyPrefix => "planning:priority-route-aggregation:";
    protected override string AllRoutesKey => "planning:all-priority-route-keys";
    protected override string LockPrefix => "priority-route:";

    // Express deliveries need faster optimization thresholds
    protected override int OptimizationOrderThreshold => 5; // Lower threshold for express
    protected override int OptimizationDurationMinutes => 720; // 12 hours for express

    /// <summary>
    /// Filter routes to include only DE (Express) service type
    /// </summary>
    protected override bool ShouldIncludeRoute(MailerPlanRoute route)
    {
        return base.ShouldIncludeRoute(route);
    }

    /// <summary>
    /// Filter orders to include based on priority configurations
    /// Multiple priority configs with OR logic between groups, AND logic within groups
    /// </summary>
    protected override bool ShouldIncludeOrderMetric(OrderMetrics orderMetric)
    {
        // Get cached priority configurations from metadata cache
        var priorityConfigs = _metadataCacheService.GetAsync<Entities.PriorityPlan>()
                                                   .ConfigureAwait(false)
                                                   .GetAwaiter()
                                                   .GetResult();

        if (!priorityConfigs.Any())
            return false;

        /**
         ** Có nhiều priority config
         ** Thoả 1 nhóm ưu tiên => return đây là đơn ưu tiên
         ** (Multiple priority configs - satisfying 1 group means it's a priority order)
         **/
        foreach (var config in priorityConfigs)
        {
            // Không có nhóm ưu tiên thì bỏ qua
            if (config.PriorityPlanGroups == null || !config.PriorityPlanGroups.Any())
                continue;

            // OR giữa các group (OR between groups)
            // AND giữa các attribute trong cùng group (AND within group attributes)
            foreach (var group in config.PriorityPlanGroups)
            {
                if (group.PriorityAttributes == null || !group.PriorityAttributes.Any())
                    continue;

                // Check if ALL attributes in this group pass (AND logic)
                bool allAttributesPass = true;
                foreach (var attribute in group.PriorityAttributes)
                {
                    bool isMatch = ConditionCheckProcess(attribute, orderMetric);

                    // Chỉ cần 1 cái fail là cả group fail
                    // Ko check nữa => check group tiếp theo
                    if (!isMatch)
                    {
                        allAttributesPass = false;
                        break;
                    }
                }

                // Nếu duyệt tất cả attribute trong group mà đều đúng => thoả group này
                // => return đây là đơn ưu tiên
                if (allAttributesPass)
                {
                    return true; // Early exit - order is priority
                }
            }
        }

        return false;
    }
     
    protected override async Task PrepareRouteAggregationAsync(string routeKey,
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        bool isUsingAdjustRoutes,
        CancellationToken cancellationToken,
        object? originalPlanningEvent = null)
    {
        var priorityConfigs = _metadataCacheService.GetAsync<Entities.PriorityPlan>()
                                                   .ConfigureAwait(false)
                                                   .GetAwaiter()
                                                   .GetResult();

        if (priorityConfigs != null && priorityConfigs.Any())
        {
            foreach (var priorityConfig in priorityConfigs)
            {
                var priorityConfigId = priorityConfig.Id;
                var routeKeyWithConfig = $"{routeKey}:{priorityConfigId}";

                await UpdateSingleRouteAggregationAsync(routeKeyWithConfig, routes, actualRoutes, isUsingAdjustRoutes, cancellationToken, originalPlanningEvent);
            }
        }
        else
        {
            _logger.LogWarning("No priority configurations found. Skipping priority route aggregation for routeKey: {RouteKey}", routeKey);
        }
    }

    public async Task<List<RouteAggregationSummary>> GetCurrentPriorityAggregationsAsync(
        CancellationToken cancellationToken = default)
    {
        return await GetCurrentAggregationsAsync(cancellationToken);
    }

    public async Task<RouteAggregationSummary?> GetPriorityRouteAggregationAsync(
        string routeKey,
        CancellationToken cancellationToken = default)
    {
        return await GetRouteAggregationAsync(routeKey, cancellationToken);
    }

    /// <summary>
    /// Gets lightweight metadata for all priority route aggregations (exposed from base)
    /// Used by snapshot job for efficient change detection
    /// </summary>
    public async Task<List<RouteMetadata>> GetAllRouteMetadataAsync()
    {
        return await GetRouteMetadataListAsync();
    }

    /// <summary>
    /// Override CreateEntityFromAggregation to set AggregationType='priority'
    /// Priority aggregations are for DE (Express) service type only
    /// </summary>
    protected override TMS.PlanningService.Domain.Entities.RouteAggregationEntity CreateEntityFromAggregation(
        RouteAggregationSummary agg,
        DateTime snapshotTime)
    {
        var entity = base.CreateEntityFromAggregation(agg, snapshotTime);

        // Override AggregationType to 'priority'
        entity.AggregationType = "priority";

        return entity;
    }


    private bool ConditionCheckProcess(Entities.PriorityPlanGroupAttr attribute, OrderMetrics orderMetric)
    {
        switch ((int)attribute.PropertyType)
        {
            case (int)PriorityType.BasicService:
                return EvaluateBasicService((int)attribute.PropertyOperator, 
                                            attribute.Values ?? string.Empty, 
                                            orderMetric.ServiceTypeId ?? string.Empty);

            case (int)PriorityType.ExtraService:
                return EvaluateExtraService((int)attribute.PropertyOperator, 
                                             attribute.Values ?? string.Empty, 
                                             orderMetric.ExtraServices?.Select(x => x.ExtraServiceId).ToList());

            case (int)PriorityType.ChargeableWeight:
                return EvaluateChargeableWeight((int)attribute.PropertyOperator, Convert.ToDouble(attribute.Values), Convert.ToDouble(orderMetric.Weight));

            case (int)PriorityType.Origin:
                return EvaluateOrigin((int)(attribute.LocationType ?? 0), (int)attribute.PropertyOperator, attribute.Values ?? string.Empty, orderMetric);

            case (int)PriorityType.Destination:
                return EvaluateDestination((int)(attribute.LocationType ?? 0), (int)attribute.PropertyOperator, attribute.Values ?? string.Empty, orderMetric);

            default:
                return false;
        }
    }

    #region Condition check process
    private bool EvaluateBasicService(int attrOperator, string values, string orderServiceTypeId)
    {
        // Tách danh sách value, loại bỏ khoảng trắng, rỗng
        var valueList = values.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList();

        bool contains = valueList.Any(value => string.Equals(value, orderServiceTypeId, StringComparison.OrdinalIgnoreCase));

        return attrOperator == (int)PriorityTypeOperator.Include ? contains : !contains;
    }

    private bool EvaluateExtraService(int attrOperator, string values, List<string>? orderExtraServiceIds)
    {
        if (orderExtraServiceIds == null || !orderExtraServiceIds.Any())
            return false;

        // Tách danh sách value, loại bỏ khoảng trắng, rỗng
        var valueList = values.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList();
 
        bool hasMatch = valueList.All(x => orderExtraServiceIds.Contains(x));
        return attrOperator == (int)PriorityTypeOperator.Include ? hasMatch : !hasMatch;
    }

    private bool EvaluateChargeableWeight(int attrOperator, double configChargeableWeight, double orderChargeableWeight)
    {
        switch (attrOperator)
        {
            case (int)PriorityTypeOperator.GreaterThan:
                return Convert.ToDouble(orderChargeableWeight) > Convert.ToDouble(configChargeableWeight);

            case (int)PriorityTypeOperator.LessThan:
                return Convert.ToDouble(orderChargeableWeight) < Convert.ToDouble(configChargeableWeight);

            case (int)PriorityTypeOperator.GreaterThanOrEqual:
                return Convert.ToDouble(orderChargeableWeight) >= Convert.ToDouble(configChargeableWeight);

            case (int)PriorityTypeOperator.LessThanOrEqual:
                return Convert.ToDouble(orderChargeableWeight) <= Convert.ToDouble(configChargeableWeight);
            default:
                return false;
        }
    }

    private bool EvaluateOrigin(int locationType, int attrOperator, string values, OrderMetrics orderMetric)
    {
        var configOrigins = values.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList();
        var orderOriginValue = string.Empty;
        switch (locationType)
        {
            case (int)LocationType.PostOffice:
                orderOriginValue = orderMetric.SenderPostOfficeId ?? string.Empty;
                break;

            case (int)LocationType.Province:
                orderOriginValue = orderMetric.SenderProvinceId ?? string.Empty;
                break;

            case (int)LocationType.Ward:
                orderOriginValue = orderMetric.SenderWardId ?? string.Empty;
                break;
            default:
                return false;
        }

        bool contains = configOrigins.Any(value => string.Equals(value, orderOriginValue, StringComparison.OrdinalIgnoreCase));
        return attrOperator == (int)PriorityTypeOperator.Include ? contains : !contains;

    }

    private bool EvaluateDestination(int locationType, int attrOperator, string values, OrderMetrics orderMetric)
    {
        var configDestinations = values.Split(',', StringSplitOptions.RemoveEmptyEntries | StringSplitOptions.TrimEntries).ToList();
        var orderDestinationValue = string.Empty;
        switch (locationType)
        {
            case (int)LocationType.PostOffice:
                orderDestinationValue = orderMetric.ReceiverPostOfficeId ?? string.Empty;
                break;

            case (int)LocationType.Province:
                orderDestinationValue = orderMetric.ReceiverProvinceId ?? string.Empty;
                break;

            case (int)LocationType.Ward:
                orderDestinationValue = orderMetric.ReceiverWardId ?? string.Empty;
                break;
            default:
                return false;
        }

        bool contains = configDestinations.Any(value => string.Equals(value, orderDestinationValue, StringComparison.OrdinalIgnoreCase));
        return attrOperator == (int)PriorityTypeOperator.Include ? contains : !contains;
    }
    #endregion

}
