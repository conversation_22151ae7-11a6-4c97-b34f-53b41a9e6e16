﻿using Microsoft.Extensions.Logging;
using Quartz;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Application.Services.Step2.PriorityPlan;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Jobs;

/// <summary>
/// Unified snapshot job that syncs normal, priority, and daily route aggregations from Redis to database every 10 minutes
/// Also persists daily plan totals from Redis to database
/// Delegates persistence logic to aggregation services following inheritance pattern
/// Uses LastPersistedAt for change detection to only persist modified aggregations
/// </summary>
[DisallowConcurrentExecution]
public class RouteAggregationSnapshotJob : IJob
{
    private readonly IPlanningAggregationService _planningAggregationService;
    private readonly IPriorityPlanningAggregationService _priorityPlanningAggregationService;
    private readonly IDailyPlanningAggregationService _dailyPlanningAggregationService;
    private readonly IBaseRepository<RouteAggregationEntity> _routeAggregationRepository;
    private readonly IBaseRepository<RouteAggregationOrderEntity> _routeOrderRepository;
    private readonly IBaseRepository<DailyPlanningEntity> _dailyPlanRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<RouteAggregationSnapshotJob> _logger;

    public RouteAggregationSnapshotJob(
        IPlanningAggregationService planningAggregationService,
        IPriorityPlanningAggregationService priorityPlanningAggregationService,
        IDailyPlanningAggregationService dailyPlanningAggregationService,
        IBaseRepository<RouteAggregationEntity> routeAggregationRepository,
        IBaseRepository<RouteAggregationOrderEntity> routeOrderRepository,
        IBaseRepository<DailyPlanningEntity> dailyPlanRepository,
        IUnitOfWork unitOfWork,
        ILogger<RouteAggregationSnapshotJob> logger)
    {
        _planningAggregationService = planningAggregationService;
        _priorityPlanningAggregationService = priorityPlanningAggregationService;
        _dailyPlanningAggregationService = dailyPlanningAggregationService;
        _routeAggregationRepository = routeAggregationRepository;
        _routeOrderRepository = routeOrderRepository;
        _dailyPlanRepository = dailyPlanRepository;
        _unitOfWork = unitOfWork;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var snapshotTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("Starting unified route aggregation snapshot job at {SnapshotTime}", snapshotTime);

            // PART 1: Persist normal route aggregations (handled by service)
            var normalCount = await _planningAggregationService.PersistRouteAggregationsAsync(
                _routeAggregationRepository,
                _routeOrderRepository,
                _unitOfWork,
                snapshotTime,
                context.CancellationToken);

            _logger.LogInformation("PART 1 completed - Persisted {Count} normal route aggregations", normalCount);

            // PART 2: Persist priority route aggregations (DE service type only - handled by service)
            var priorityCount = await _priorityPlanningAggregationService.PersistRouteAggregationsAsync(
                _routeAggregationRepository,
                _routeOrderRepository,
                _unitOfWork,
                snapshotTime,
                context.CancellationToken);

            _logger.LogInformation("PART 2 completed - Persisted {Count} priority route aggregations", priorityCount);

            // PART 3: Persist daily plan route aggregations (handled by service)
            var dailyCount = await _dailyPlanningAggregationService.PersistRouteAggregationsAsync(
                _routeAggregationRepository,
                _routeOrderRepository,
                _unitOfWork,
                snapshotTime,
                context.CancellationToken);

            _logger.LogInformation("PART 3 completed - Persisted {Count} daily plan route aggregations", dailyCount);

            // PART 4: Persist daily plan totals from Redis to database
            await PersistDailyPlanTotalsAsync(context.CancellationToken);

            stopwatch.Stop();
            _logger.LogInformation(
                "Unified snapshot job completed in {ElapsedMs}ms - Normal: {NormalCount}, Priority: {PriorityCount}, Daily: {DailyCount}",
                stopwatch.ElapsedMilliseconds,
                normalCount,
                priorityCount,
                dailyCount);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Unified route aggregation snapshot job failed after {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds);
            // Don't throw - allow job to retry on next schedule
        }
    }

    /// <summary>
    /// Persists daily plan totals from Redis to database
    /// No DB queries for reading - only updates from Redis cache
    /// </summary>
    private async Task PersistDailyPlanTotalsAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Persisting daily plan totals from Redis");

            // Get all active daily plan IDs from Redis
            var activePlanIds = await _dailyPlanningAggregationService.GetActiveDailyPlanIdsAsync(cancellationToken);

            if (activePlanIds == null || !activePlanIds.Any())
            {
                _logger.LogInformation("No active daily plans to update totals");
                return;
            }

            var updatedCount = 0;
            foreach (var planId in activePlanIds)
            {
                try
                {
                    await _dailyPlanningAggregationService.UpdateDailyPlanTotalsAsync(
                        planId,
                        _dailyPlanRepository,
                        _unitOfWork,
                        cancellationToken);
                    updatedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to update totals for daily plan {PlanId}", planId);
                    // Continue with next plan
                }
            }

            _logger.LogInformation(
                "Updated totals for {UpdatedCount} out of {TotalCount} daily plans",
                updatedCount,
                activePlanIds.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to persist daily plan totals");
            // Don't throw - allow job to continue
        }
    }
}
