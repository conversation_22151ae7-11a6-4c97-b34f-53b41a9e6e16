using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Quartz;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Application.Services.Step2.PriorityPlan;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Jobs;

/// <summary>
/// Unified snapshot job that syncs normal, priority, and daily route aggregations from Redis to database every 10 minutes
/// Also persists daily plan totals from Redis to database
/// Delegates persistence logic to aggregation services following inheritance pattern
/// Uses LastPersistedAt for change detection to only persist modified aggregations
/// OPTIMIZED: Uses parallel processing with isolated scopes to avoid DbContext sharing issues
/// </summary>
[DisallowConcurrentExecution]
public class RouteAggregationSnapshotJob : IJob
{
    private readonly IServiceScopeFactory _scopeFactory;
    private readonly ILogger<RouteAggregationSnapshotJob> _logger;

    public RouteAggregationSnapshotJob(
        IServiceScopeFactory scopeFactory,
        ILogger<RouteAggregationSnapshotJob> logger)
    {
        _scopeFactory = scopeFactory;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var snapshotTime = DateTime.UtcNow;

        try
        {
            _logger.LogInformation("Starting unified route aggregation snapshot job at {SnapshotTime}", snapshotTime);

            // OPTIMIZATION: Process all aggregation types in parallel with isolated scopes
            // Each parallel task gets its own DbContext/UnitOfWork to avoid threading issues
            var normalTask = PersistNormalAggregationsAsync(snapshotTime, context.CancellationToken);
            var priorityTask = PersistPriorityAggregationsAsync(snapshotTime, context.CancellationToken);
            var dailyTask = PersistDailyAggregationsAsync(snapshotTime, context.CancellationToken);

            // Wait for all three to complete
            var counts = await Task.WhenAll(normalTask, priorityTask, dailyTask);

            var normalCount = counts[0];
            var priorityCount = counts[1];
            var dailyCount = counts[2];

            _logger.LogInformation(
                "Parts 1-3 completed in parallel - Normal: {NormalCount}, Priority: {PriorityCount}, Daily: {DailyCount}",
                normalCount,
                priorityCount,
                dailyCount);

            // PART 4: Persist daily plan totals from Redis to database
            // Uses separate scope for isolation
            await PersistDailyPlanTotalsAsync(context.CancellationToken);

            stopwatch.Stop();
            _logger.LogInformation(
                "Unified snapshot job completed in {ElapsedMs}ms - Normal: {NormalCount}, Priority: {PriorityCount}, Daily: {DailyCount}",
                stopwatch.ElapsedMilliseconds,
                normalCount,
                priorityCount,
                dailyCount);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "Unified route aggregation snapshot job failed after {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds);
            // Don't throw - allow job to retry on next schedule
        }
    }

    /// <summary>
    /// Persists normal route aggregations using an isolated scope
    /// Creates its own DbContext/UnitOfWork to avoid threading issues
    /// </summary>
    private async Task<int> PersistNormalAggregationsAsync(DateTime snapshotTime, CancellationToken cancellationToken)
    {
        try
        {
            // Create isolated scope with its own DbContext
            using var scope = _scopeFactory.CreateScope();
            var service = scope.ServiceProvider.GetRequiredService<IPlanningAggregationService>();
            var routeRepo = scope.ServiceProvider.GetRequiredService<IBaseRepository<RouteAggregationEntity>>();
            var orderRepo = scope.ServiceProvider.GetRequiredService<IBaseRepository<RouteAggregationOrderEntity>>();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

            var count = await service.PersistRouteAggregationsAsync(
                routeRepo,
                orderRepo,
                unitOfWork,
                snapshotTime,
                cancellationToken);

            _logger.LogInformation("PART 1 completed - Persisted {Count} normal route aggregations", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to persist normal route aggregations");
            return 0;
        }
    }

    /// <summary>
    /// Persists priority route aggregations using an isolated scope
    /// Creates its own DbContext/UnitOfWork to avoid threading issues
    /// </summary>
    private async Task<int> PersistPriorityAggregationsAsync(DateTime snapshotTime, CancellationToken cancellationToken)
    {
        try
        {
            // Create isolated scope with its own DbContext
            using var scope = _scopeFactory.CreateScope();
            var service = scope.ServiceProvider.GetRequiredService<IPriorityPlanningAggregationService>();
            var routeRepo = scope.ServiceProvider.GetRequiredService<IBaseRepository<RouteAggregationEntity>>();
            var orderRepo = scope.ServiceProvider.GetRequiredService<IBaseRepository<RouteAggregationOrderEntity>>();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

            var count = await service.PersistRouteAggregationsAsync(
                routeRepo,
                orderRepo,
                unitOfWork,
                snapshotTime,
                cancellationToken);

            _logger.LogInformation("PART 2 completed - Persisted {Count} priority route aggregations", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to persist priority route aggregations");
            return 0;
        }
    }

    /// <summary>
    /// Persists daily route aggregations using an isolated scope
    /// Creates its own DbContext/UnitOfWork to avoid threading issues
    /// </summary>
    private async Task<int> PersistDailyAggregationsAsync(DateTime snapshotTime, CancellationToken cancellationToken)
    {
        try
        {
            // Create isolated scope with its own DbContext
            using var scope = _scopeFactory.CreateScope();
            var service = scope.ServiceProvider.GetRequiredService<IDailyPlanningAggregationService>();
            var routeRepo = scope.ServiceProvider.GetRequiredService<IBaseRepository<RouteAggregationEntity>>();
            var orderRepo = scope.ServiceProvider.GetRequiredService<IBaseRepository<RouteAggregationOrderEntity>>();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

            var count = await service.PersistRouteAggregationsAsync(
                routeRepo,
                orderRepo,
                unitOfWork,
                snapshotTime,
                cancellationToken);

            _logger.LogInformation("PART 3 completed - Persisted {Count} daily plan route aggregations", count);
            return count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to persist daily route aggregations");
            return 0;
        }
    }

    /// <summary>
    /// Persists daily plan totals from Redis to database
    /// No DB queries for reading - only updates from Redis cache
    /// Uses batched parallel processing to avoid overwhelming database connection pool
    /// </summary>
    private async Task PersistDailyPlanTotalsAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Create isolated scope for reading plan IDs
            using var scope = _scopeFactory.CreateScope();
            var service = scope.ServiceProvider.GetRequiredService<IDailyPlanningAggregationService>();

            _logger.LogInformation("Persisting daily plan totals from Redis");

            // Get all active daily plan IDs from Redis
            var activePlanIds = await service.GetActiveDailyPlanIdsAsync(cancellationToken);

            if (activePlanIds == null || !activePlanIds.Any())
            {
                _logger.LogInformation("No active daily plans to update totals");
                return;
            }

            // OPTIMIZATION: Batched parallel processing to limit concurrent DB connections
            // Process 10 plans at a time to avoid exhausting connection pool
            const int BATCH_SIZE = 10;
            var planIdList = activePlanIds.ToList();
            var totalBatches = (int)Math.Ceiling((double)planIdList.Count / BATCH_SIZE);
            var totalUpdated = 0;

            _logger.LogInformation(
                "Processing {TotalPlans} daily plans in {TotalBatches} batches of {BatchSize}",
                planIdList.Count,
                totalBatches,
                BATCH_SIZE);

            for (int i = 0; i < planIdList.Count; i += BATCH_SIZE)
            {
                var batchNumber = (i / BATCH_SIZE) + 1;
                var batch = planIdList.Skip(i).Take(BATCH_SIZE).ToList();

                // Process this batch in parallel (max 10 concurrent connections)
                var updateTasks = batch.Select(planId =>
                    UpdateDailyPlanTotalWithScopeAsync(planId, cancellationToken));

                var results = await Task.WhenAll(updateTasks);
                var batchUpdatedCount = results.Count(success => success);
                totalUpdated += batchUpdatedCount;

                _logger.LogInformation(
                    "Batch {BatchNumber}/{TotalBatches}: Updated {UpdatedCount}/{BatchCount} plans",
                    batchNumber,
                    totalBatches,
                    batchUpdatedCount,
                    batch.Count);
            }

            _logger.LogInformation(
                "Updated totals for {UpdatedCount} out of {TotalCount} daily plans",
                totalUpdated,
                planIdList.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to persist daily plan totals");
            // Don't throw - allow job to continue
        }
    }

    /// <summary>
    /// Updates a single daily plan total using an isolated scope
    /// Each plan update gets its own DbContext to avoid concurrency issues
    /// </summary>
    private async Task<bool> UpdateDailyPlanTotalWithScopeAsync(Guid planId, CancellationToken cancellationToken)
    {
        try
        {
            // Create isolated scope for this plan update
            using var scope = _scopeFactory.CreateScope();
            var service = scope.ServiceProvider.GetRequiredService<IDailyPlanningAggregationService>();
            var dailyPlanRepo = scope.ServiceProvider.GetRequiredService<IBaseRepository<DailyPlanningEntity>>();
            var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

            await service.UpdateDailyPlanTotalsAsync(
                planId,
                dailyPlanRepo,
                unitOfWork,
                cancellationToken);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update totals for daily plan {PlanId}", planId);
            return false;
        }
    }
}
