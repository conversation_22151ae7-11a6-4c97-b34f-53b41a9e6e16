﻿using System.ComponentModel.DataAnnotations.Schema;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Domain.Entities.Metadata;
public class PriorityPlanGroup : EntityBase
{
    [Column("priority_plan_id")]
    public Guid PriorityPlanId { get; set; }

    [Column("logic_operator")]
    public LogicalOperator? LogicOperator { get;set; }

    [Column("step_number")]
    public int StepNumber { get; set; }

    public ICollection<PriorityPlanGroupAttr> PriorityAttributes { get; set; } = new List<PriorityPlanGroupAttr>();

}
