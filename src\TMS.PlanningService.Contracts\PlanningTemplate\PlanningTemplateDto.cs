﻿using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.PlanningTemplate;

public class PlanningTemplateDto
{
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public Guid RouteId { get; set; }
    public string RouteName { get; set; } = string.Empty;
    public string StartedOfficeName { get; set; } = string.Empty;
    public string StartedOfficeCode { get; set; } = string.Empty;
    public string EndedOfficeName { get; set; } = string.Empty;
    public string EndedOfficeCode { get; set; } = string.Empty;
    public double TotalDistance { get; set; }
    public double TotalDuration { get; set; }
    public int StopCount { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public int PriorityNumber { get; set; }
    public Guid VehicleTypeId { get; set; }
    public string VehicleTypeName { get; set; } = string.Empty;
    public bool IsActive { get; set; }
    public Guid CreatedBy { get; set; }
    public DateTime CreatedAt { get; set; }
    public string? CreatedByName { get; set; }
    public string? CreatedByCode { get; set; }
    public string? CreatedByAvatar { get; set; }
    public Guid UpdatedBy { get; set; }
    public DateTime UpdatedAt { get; set; }
    public string? UpdatedByName { get; set; }
    public string? UpdatedByCode { get; set; }
    public string? UpdatedByAvatar { get; set; }
    public List<PlanningTemplateDetailDto> Details { get; set; } = new();
}

public class PlanningTemplateDetailDto
{
    public Guid Id { get; set; }
    public Guid PlanningTemplateId { get; set; }
    public Guid PostOfficeId { get; set; }
    public string PostOfficeName { get; set; } = string.Empty;
    public TimeSpan FromTime { get; set; }
    public int FromAddDays { get; set; }
    public TimeSpan ToTime { get; set; }
    public int ToTimeAddDays { get; set; }

    public string PostOfficeTypeName { get; set; } = string.Empty;
    public BusinessOperation BusinessOperation { get; set; }
    public string BusinessOperationName { get; set; } = string.Empty;
    public float DistanceBetweenPoints { get; set; }
    public int StepNumber { get; set; }
    public string PostOfficeCode { get; set; } = string.Empty;
    public double? Latitude { get; set; }
    public double? Longitude { get; set; }
}

public class CreatePlanningTemplateRequest
{
    public Guid RouteId { get; set; }
    public string Name { get; set; } = string.Empty;
    public int PriorityNumber { get; set; } = 0;
    public Guid VehicleTypeId { get; set; }
    public bool IsActive { get; set; } = true;
    public string RouteCode { get; set; } = string.Empty;
    public string PostOfficeCodes { get; set; } = string.Empty;
    public List<CreatePlanningTemplateDetailRequest> Details { get; set; } = new();
}

public class CreatePlanningTemplateDetailRequest
{
    public Guid PostOfficeId { get; set; }
    public TimeSpan FromTime { get; set; }
    public int FromAddDays { get; set; } = 0;
    public TimeSpan ToTime { get; set; }
    public int ToTimeAddDays { get; set; } = 0;
    public BusinessOperation BusinessOperation { get; set; }
    public float DistanceBetweenPoints { get; set; }
    public int StepNumber { get; set; }
    public string PostOfficeCode { get; set; }
}

public class UpdatePlanningTemplateRequest
{
    public Guid Id { get; set; }
    public Guid? RouteId { get; set; }
    public string? Name { get; set; } = string.Empty;
    public int? PriorityNumber { get; set; }
    public Guid? VehicleTypeId { get; set; }
    public bool? IsActive { get; set; }
    public string RouteCode { get; set; } = string.Empty;
    public string PostOfficeCodes { get; set; } = string.Empty;
    public List<UpdatePlanningTemplateDetailRequest> Details { get; set; } = new();
}

public class UpdatePlanningTemplateDetailRequest
{
    public Guid? Id { get; set; }
    public Guid PostOfficeId { get; set; }
    public TimeSpan FromTime { get; set; }
    public int FromAddDays { get; set; }
    public TimeSpan ToTime { get; set; }
    public int ToTimeAddDays { get; set; }
    public BusinessOperation BusinessOperation { get; set; }
    public float DistanceBetweenPoints { get; set; }
    public int StepNumber { get; set; }
    public string PostOfficeCode { get; set; }
}


public record GetPlanningTemplatesRequest(
    string SearchTerm,
    int Page,
    int PageSize,
    List<Guid>? VehicleTypeIds,
    List<Guid>? CreatedByIds,
    bool? IsActive,
    SortOrderBase? SortOrder
);
