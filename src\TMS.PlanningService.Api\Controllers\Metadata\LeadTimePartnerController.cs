﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.LeadTimePartner.CreateLeadTimePartner;
using TMS.PlanningService.Application.Features.LeadTimePartner.UpdateLeadTimePartner;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.CreatePriorityPlan;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdatePriorityPlan;
using TMS.PlanningService.Contracts.LeadTimePartner;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/lead-time-partners")]
[Produces("application/json")]
public class LeadTimePartnerController : ControllerBase
{
    private readonly IMediator _mediator;

    public LeadTimePartnerController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Create a lead time partner
    /// </summary>
    /// <param name="request">Lead time partner data</param>
    /// <returns>Created Lead time partner Id</returns>
    [HttpPost]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Guid>> CreateLeadTimePartner([FromBody] CreateLeadTimePartnerRequest request)
    {
        var command = new CreateLeadTimePartnerCommand(request);
        var result = await _mediator.Send(command);

        return CreatedAtAction(
            nameof(CreateLeadTimePartner),
            new { id = result },
            result);
    }

    /// <summary>
    /// Update an existing lead time partner
    /// </summary>
    /// <param name="id">lead time partner Id</param>
    /// <param name="request">Updated lead time partner data</param>
    /// <returns>No content</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdatePriorityRoute(Guid id, [FromBody] UpdateLeadTimePartnerRequest request)
    {
        if (id != request.Id)
            return BadRequest("ID mismatch between route and body");

        var command = new UpdateLeadTimePartnerCommand(request);
        await _mediator.Send(command);
        return NoContent();
    }
}
