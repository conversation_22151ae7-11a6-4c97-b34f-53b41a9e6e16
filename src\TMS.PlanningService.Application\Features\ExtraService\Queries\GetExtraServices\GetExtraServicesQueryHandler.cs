﻿
using MediatR;
using TMS.PlanningService.Contracts.Dto;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using Entity = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Features.ExtraService.Queries.GetExtraServices;

public class GetExtraServicesQueryHandler : IRequestHandler<GetExtraServicesQuery, List<ExtraServiceDto>>
{
    private readonly IMetadataCacheService _metadataService;
    private readonly IBaseRepository<Entity.ExtraService> _extraServicesRepository;

    public GetExtraServicesQueryHandler(IMetadataCacheService metadataService,
        IBaseRepository<Entity.ExtraService> extraServicesRepository)
    {
        _metadataService = metadataService;
        _extraServicesRepository = extraServicesRepository;
    }

    public async Task<List<ExtraServiceDto>> Handle(GetExtraServicesQuery request, CancellationToken cancellationToken)
    {
        var extraServices = await _metadataService.GetExtraServicesAsync<Entity.ExtraService>();
        var data = extraServices.Select(x => new ExtraServiceDto
        {
            ServiceId = x.ServiceId,
            ServiceName = x.ServiceName,
            IsActive = x.IsActive ?? false
        }).ToList();

        if (!data.Any())
        {
            data = (await _extraServicesRepository.GetAllAsync(cancellationToken))
                  .Select(x => new ExtraServiceDto
                  {
                      ServiceId = x.ServiceId,
                      ServiceName = x.ServiceName,
                      IsActive = x.IsActive ?? false
                  }).ToList();
        }

        return data.OrderBy(x => x.ServiceId).ToList();
    }
}
