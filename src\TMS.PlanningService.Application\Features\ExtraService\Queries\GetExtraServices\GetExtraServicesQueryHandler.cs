﻿
using MediatR;
using TMS.PlanningService.Contracts.Dto;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using Entity = TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Application.Features.ExtraService.Queries.GetExtraServices;

public class GetExtraServicesQueryHandler : IRequestHandler<GetExtraServicesQuery, List<ExtraServiceDto>>
{
    private readonly IMetadataCacheService _metadataService;

    public GetExtraServicesQueryHandler(IMetadataCacheService metadataService)
    {
        _metadataService = metadataService;
    }

    public async Task<List<ExtraServiceDto>> Handle(GetExtraServicesQuery request, CancellationToken cancellationToken)
    {
        var extraServices = await _metadataService.GetExtraServicesAsync<Entity.Metadata.ExtraService>();
        var data = extraServices.Select(x => new ExtraServiceDto
        {
            ServiceId = x.ServiceId,
            ServiceName = x.ServiceName,
            IsActive = x.IsActive ?? false
        }).OrderBy(x => x.ServiceId).ToList();
        return data;
    }
}
