﻿using FluentValidation;

namespace TMS.PlanningService.Application.Features.Planning.Commands.ReceivePlanning;

public class ReceivePlanningCommandValidator : AbstractValidator<ReceivePlanningCommand>
{
    public ReceivePlanningCommandValidator()
    {
        RuleFor(x => x.MailerRouteMaster)
            .NotNull()
            .WithMessage("MailerRouteMaster is required");

        RuleFor(x => x.MailerRouteMaster.MailerId)
            .NotEmpty()
            .WithMessage("MailerId is required")
            .MaximumLength(50)
            .WithMessage("MailerId cannot exceed 50 characters");

        RuleForEach(x => x.MailerPlanRoutes)
            .NotNull()
            .NotEmpty()
            .WithMessage("Individual plan routes cannot be null or empty");

        RuleForEach(x => x.MailerPlanRoutes)
            .Must(route => !string.IsNullOrWhiteSpace(route.MailerId))
            .WithMessage("MailerId is required for each plan route");
         
        RuleForEach(x => x.MailerActualRoutes)
            .NotNull()
            .NotEmpty()
            .WithMessage("Individual actual routes cannot be null or empty");

        RuleForEach(x => x.MailerActualRoutes)
            .Must(route => !string.IsNullOrWhiteSpace(route.MailerId))
            .WithMessage("MailerId is required for each actual route");
         
    }
}
