﻿using MediatR;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.PlanningTemplate;
using TMS.PlanningService.Contracts.PmsSync;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Features.LeadTime.Queries.GetLeadTimes;

public record GetLeadTimesQuery(GetLeadTimeRequest ParamRequest) : IRequest<PagedResult<LeadTimeConfigDto>>;
