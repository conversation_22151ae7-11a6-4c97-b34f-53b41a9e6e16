﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Infra.Data;
using TMS.PlanningService.Domain.Entities;
using Mapster;

namespace TMS.PlanningService.Application.Features.Planning.Commands.ReceivePlanning;

public class ReceivePlanningCommandHandler : IRequestHandler<ReceivePlanningCommand, MailerRouteMasterEntity>
{
    private readonly ApplicationDbContext _context;
    private readonly ILogger<ReceivePlanningCommandHandler> _logger;

    public ReceivePlanningCommandHandler(
        ApplicationDbContext context,
        ILogger<ReceivePlanningCommandHandler> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task<MailerRouteMasterEntity> Handle(ReceivePlanningCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Processing planning data for MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
            request.MailerRouteMaster.MailerId, request.MailerRouteMaster.ChildMailerId);

        try
        {
            var mailerRouteMaster = request.MailerRouteMaster.Adapt<MailerRouteMasterEntity>();
             
            // Add plan routes
            foreach (var planRoute in request.MailerPlanRoutes)
            {
                var entity = planRoute.Adapt<MailerPlanRouteEntity>(); 
                mailerRouteMaster.PlanRoutes.Add(entity);
            }

            // Add adjust routes
            foreach (var adjustRoute in request.MailerAdjustRoutes)
            {
                var entity = adjustRoute.Adapt<MailerAdjustRouteEntity>(); 
                mailerRouteMaster.AdjustRoutes.Add(entity);
            }

            // Add actual routes
            foreach (var actualRoute in request.MailerActualRoutes)
            {
                var entity = actualRoute.Adapt<MailerActualRouteEntity>();
                mailerRouteMaster.ActualRoutes.Add(entity);
            }

            // Check if the record already exists (upsert logic)
            // For partitioned tables, we need to query by business key and date range
            var existingRecord = await _context.MailerRouteMasters
                .Where(x => x.MailerId == mailerRouteMaster.MailerId &&
                           x.ChildMailerId == mailerRouteMaster.ChildMailerId)
                .OrderByDescending(x => x.CreatedDate)
                .FirstOrDefaultAsync(cancellationToken);

            if (existingRecord != null)
            { 
                _logger.LogInformation("Marked existing planning data as superseded for MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                    mailerRouteMaster.MailerId, mailerRouteMaster.ChildMailerId);
                 
                // Remove existing items
                _context.MailerRouteMasters.RemoveRange(existingRecord);

                // Also remove related child entities
                var mailerPlanRoutes = await _context.MailerPlanRoutes
                    .Where(x => x.MailerId == mailerRouteMaster.MailerId && x.ChildMailerId == mailerRouteMaster.ChildMailerId)
                    .OrderByDescending(x => x.MasterCreatedDate)
                    .ToListAsync(cancellationToken);
                
                _context.MailerPlanRoutes.RemoveRange(mailerPlanRoutes);

                var mailerAdjustRoutes = await _context.MailerAdjustRoutes
                    .Where(x => x.MailerId == mailerRouteMaster.MailerId && x.ChildMailerId == mailerRouteMaster.ChildMailerId)
                    .OrderByDescending(x => x.MasterCreatedDate)
                    .ToListAsync(cancellationToken);

                _context.MailerAdjustRoutes.RemoveRange(mailerAdjustRoutes);

                var mailerActualRoutes = await _context.MailerActualRoutes
                    .Where(x => x.MailerId == mailerRouteMaster.MailerId && x.ChildMailerId == mailerRouteMaster.ChildMailerId)
                    .OrderByDescending(x => x.MasterCreatedDate)
                    .ToListAsync(cancellationToken);
                 
                _context.MailerActualRoutes.RemoveRange(mailerActualRoutes);

            }
             
            // Set MasterCreatedDate for child entities to match parent
            foreach (var planRoute in mailerRouteMaster.PlanRoutes)
                planRoute.MasterCreatedDate = mailerRouteMaster.CreatedDate;
            foreach (var adjustRoute in mailerRouteMaster.AdjustRoutes)
                adjustRoute.MasterCreatedDate = mailerRouteMaster.CreatedDate;
            foreach (var actualRoute in mailerRouteMaster.ActualRoutes)
                actualRoute.MasterCreatedDate = mailerRouteMaster.CreatedDate;
             
            // Always add new record for partitioned table
            await _context.MailerRouteMasters.AddAsync(mailerRouteMaster, cancellationToken);

            _logger.LogInformation("Created new planning data for MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                mailerRouteMaster.MailerId, mailerRouteMaster.ChildMailerId);

            await _context.SaveChangesAsync(cancellationToken);

            _logger.LogInformation("Successfully processed planning data for MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                mailerRouteMaster.MailerId, mailerRouteMaster.ChildMailerId);

            return mailerRouteMaster;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process planning data for MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                request.MailerRouteMaster.MailerId, request.MailerRouteMaster.ChildMailerId);
            throw;
        }
    }
}
