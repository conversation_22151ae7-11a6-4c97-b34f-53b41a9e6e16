﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Infra.Data;
using TMS.SharedKernel.Constants.Constants;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.PlanningService.Application.Services.Step2.DailyPlanning;

/// <summary>
/// Generates daily real execution plans from planning templates
/// Runs at midnight to create execution plans for today
/// </summary>
public class DailyPlanningGeneratorService : IDailyPlanningGeneratorService
{
    private readonly ApplicationDbContext _dbContext;
    private readonly ILogger<DailyPlanningGeneratorService> _logger;
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IDailyPlanningAggregationService _dailyPlanningAggregationService;
    private readonly IBaseRepository<DailyPlanningEntity> _dailyPlanRepository;

    public DailyPlanningGeneratorService(
        ApplicationDbContext dbContext,
        ILogger<DailyPlanningGeneratorService> logger,
        ICurrentFactorProvider currentFactorProvider,
        IDailyPlanningAggregationService dailyPlanningAggregationService,
        IBaseRepository<DailyPlanningEntity> dailyPlanRepository)
    {
        _dbContext = dbContext;
        _logger = logger;
        _currentFactorProvider = currentFactorProvider;
        _dailyPlanningAggregationService = dailyPlanningAggregationService;
        _dailyPlanRepository = dailyPlanRepository;
    }

    public async Task<int> GenerateDailyPlansAsync(
        DateOnly? executionDate = null,
        Guid? companyId = null,
        CancellationToken cancellationToken = default)
    {
        // Use local time instead of UTC to avoid timezone issues
        // When running at midnight local time, we want to generate plans for "today" in local timezone
        // Using UTC would cause plans to be generated for yesterday in timezones ahead of UTC (e.g., Asia)
        var targetDate = executionDate ?? DateOnly.FromDateTime(DateTime.Now);
        var generatedCount = 0;

        try
        {
            _logger.LogInformation("Starting daily plan generation for date: {Date}", targetDate);

            // Get all active planning templates
            var query = _dbContext.PlanningTemplates
                .Include(t => t.Details.OrderBy(d => d.StepNumber))
                .Where(t => t.IsActive && !t.IsDeleted);

            if (companyId.HasValue)
            {
                query = query.Where(t => t.CompanyId == companyId.Value);
            }

            var templates = await query.ToListAsync(cancellationToken);

            _logger.LogInformation("Found {Count} active planning templates", templates.Count);

            // Query max plan code counter once for this month (performance optimization)
            var monthPrefix = $"KHVC{targetDate:yyMM}";
            var lastPlanCode = await _dbContext.DailyPlannings
                .Where(p => p.Code.StartsWith(monthPrefix))
                .MaxAsync(p => (string?)p.Code, cancellationToken);

            int nextCounter = 1;
            if (!string.IsNullOrEmpty(lastPlanCode) && lastPlanCode.Length >= monthPrefix.Length + 5)
            {
                var counterStr = lastPlanCode.Substring(monthPrefix.Length, 5);
                if (int.TryParse(counterStr, out int currentCounter))
                {
                    nextCounter = currentCounter + 1;
                }
            }

            var routeAggregationSummaries = new List<RouteAggregationSummary>();
            foreach (var template in templates)
            {
                try
                {
                    // Check if plan already exists for this template and date
                    var existDailyPlans = (await _dailyPlanRepository.FindWithIncludeAsync(
                                            predicate: p => p.PlanningTemplateId == template.Id && p.ExecutionDate >= targetDate,
                                            includes: p => p.RouteAggregations));

                    if (existDailyPlans != null && existDailyPlans.Any())
                    {
                        foreach (var dailyPlan in existDailyPlans)
                        {
                            var existRouteAggregations = dailyPlan.RouteAggregations.Select(agg => new RouteAggregationSummary
                            {
                                DailyPlanningId = agg.DailyPlanningId,
                                FromOfficeId = agg.FromOfficeId,
                                ToOfficeId = agg.ToOfficeId,

                                // Convert from UTC to local machine time (e.g., UTC+7 for Vietnam)
                                FromTime = agg.FromTime?.ToLocalTime(),
                                ToTime = agg.ToTime?.ToLocalTime(),
                                AggregatedAt = agg.AggregatedAt.ToLocalTime(),
                                RouteKey = agg.RouteKey,
                                IsPlanningExist = true,
                            }).ToList();

                            routeAggregationSummaries.AddRange(existRouteAggregations);
                        }

                        _logger.LogWarning(
                            "Plan already exists for template {TemplateId} ({TemplateName}) on date {Date}, skipping",
                            template.Id, template.Name, targetDate);

                        continue;
                    }

                    var routeSumaries = await GeneratePlanFromTemplateAsync(template.Id, targetDate, nextCounter, cancellationToken);
                    routeAggregationSummaries.AddRange(routeSumaries);
                    nextCounter++; // Increment counter for next plan
                    generatedCount++;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex,
                        "Failed to generate plan from template {TemplateId} ({TemplateName})",
                        template.Id, template.Name);
                    // Continue with next template
                }
            }

            //Initializes route aggregations in Redis from generated daily plan
            await _dailyPlanningAggregationService.InitializeRouteAggregationsAsync(
                    routeAggregationSummaries,
                    _dailyPlanRepository,
                    cancellationToken);

            _logger.LogInformation(
                "Successfully generated {Count} real plans for date {Date}",
                generatedCount, targetDate);

            return generatedCount;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to generate daily plans for date {Date}", targetDate);
            throw;
        }
    }

    public async Task<List<RouteAggregationSummary>> GeneratePlanFromTemplateAsync(
        Guid templateId,
        DateOnly executionDate,
        int? planCounter = null,
        CancellationToken cancellationToken = default)
    {
        // Load template with details
        var template = await _dbContext.PlanningTemplates
            .Include(t => t.Details.OrderBy(d => d.StepNumber))
            .FirstOrDefaultAsync(t => t.Id == templateId, cancellationToken);

        if (template == null)
        {
            throw new InvalidOperationException($"Planning template {templateId} not found");
        }

        if (!template.IsActive || template.IsDeleted)
        {
            throw new InvalidOperationException($"Planning template {templateId} is not active");
        }

        // Generate code for the real plan (KHVCyyMM{D5})
        var monthPrefix = $"KHVC{executionDate:yyMM}";

        int nextCounter;
        if (planCounter.HasValue)
        {
            // Use the provided counter (passed from batch generation for performance)
            nextCounter = planCounter.Value;
        }
        else
        {
            // Query the database for the next counter (fallback for single plan generation)
            var lastPlanCode = await _dbContext.DailyPlannings
                .Where(p => p.Code.StartsWith(monthPrefix))
                .MaxAsync(p => (string?)p.Code, cancellationToken);

            nextCounter = 1;
            if (!string.IsNullOrEmpty(lastPlanCode) && lastPlanCode.Length >= monthPrefix.Length + 5)
            {
                var counterStr = lastPlanCode.Substring(monthPrefix.Length, 5);
                if (int.TryParse(counterStr, out int currentCounter))
                {
                    nextCounter = currentCounter + 1;
                }
            }
        }

        var planCode = $"{monthPrefix}{nextCounter:D5}";

        // Create daily execution plan from template
        var dailyPlan = new DailyPlanningEntity
        {
            CompanyId = template.CompanyId,
            PlanningTemplateId = template.Id,
            RouteId = template.RouteId,
            ExecutionDate = executionDate,
            Code = planCode,
            Name = $"{template.Name}",
            TotalDistance = template.TotalDistance,
            TotalDuration = template.TotalDuration,
            OfficeCount = template.OfficeCount,
            VehicleTypeId = template.VehicleTypeId,
            RouteCode = template.RouteCode,
            PostOfficeCodes = template.PostOfficeCodes,
            PriorityNumber = template.PriorityNumber,
            Status = String.Empty, // does not handle at this time
            IsActive = true,
            TotalWeight = 0, // Will be updated by RabbitMQ events
            TotalRealWeight = 0, // Will be updated by RabbitMQ events
            TotalDiffWeight = 0,
            CreatedBy = SystemConstants.System.Id,
            CreatedAt = DateTime.UtcNow,
            UpdatedBy = SystemConstants.System.Id,
            UpdatedAt = DateTime.UtcNow
        };

        // Create route aggregations from template details using unified RouteAggregationEntity
        // Group consecutive stops to create routes (Stop[i] -> Stop[i+1])
        var routeAggregations = new List<RouteAggregationEntity>();
        var orderedDetails = template.Details.OrderBy(d => d.StepNumber).ToList();
        var now = DateTime.UtcNow;

        for (int i = 0; i < orderedDetails.Count - 1; i++)
        {
            var fromStop1 = orderedDetails[i];
            var fromStop2 = orderedDetails[i + 1];

            // Calculate actual date-times for the route
            var baseDate = executionDate.ToDateTime(TimeOnly.MinValue);
            var fromTimeStop1 = baseDate.AddDays(fromStop1.FromAddDays).Add(fromStop1.FromTime);
            var fromTimeStop2 = baseDate.AddDays(fromStop2.FromAddDays).Add(fromStop2.FromTime);

            // Calculate duration in minutes
            var durationMinutes = (int)(fromTimeStop2 - fromTimeStop1).TotalMinutes;

            // Create route key: FromTime:ToTime:FromOfficeId:ToOfficeId
            var routeKey = $"{fromTimeStop1:yyyyMMddHHmm}:{fromTimeStop2:yyyyMMddHHmm}:{fromStop1.PostOfficeCode}:{fromStop2.PostOfficeCode}";

            _logger.LogInformation($"Generated daily plan with code: {planCode} and routeKey: {routeKey}");

            var routeAggregation = new RouteAggregationEntity
            {
                RouteKey = routeKey,
                AggregationType = "daily", // Set aggregation type to 'daily'
                SnapshotAt = now, // Initial snapshot time
                
                DailyPlanningId = dailyPlan.Id,

                FromOfficeId = fromStop1.PostOfficeCode,
                ToOfficeId = fromStop2.PostOfficeCode,
                FromTime = fromTimeStop1,
                ToTime = fromTimeStop2,
                TotalDurationMinutes = durationMinutes,
                AverageDurationMinutes = durationMinutes, // Same as total for single route segment
                EarliestStartTime = fromTimeStop1,
                LatestEndTime = fromTimeStop2,
                TotalOrders = 0,
                TotalItems = 0,
                TotalWeight = 0, // Will be updated by RabbitMQ events when orders arrive
                TotalRealWeight = 0, // Will be updated by RabbitMQ events
                TotalCalWeight = 0, // Will be updated by RabbitMQ events
                TotalDiffWeight = 0, // Will be updated by RabbitMQ events
                PriorityScore = 0, // Will be calculated when orders are aggregated
                NeedsOptimization = false,
                TransportProviderBreakdownJson = "[]",
                VehicleTypeBreakdownJson = "[]",
                TransportMethodBreakdownJson = "[]",
                AggregatedAt = now,
                FromBusinessOperation = fromStop1.BusinessOperation,
                ToBusinessOperation = fromStop2.BusinessOperation
            };

            routeAggregations.Add(routeAggregation);
        }

        // Add route aggregations to plan
        dailyPlan.RouteAggregations = routeAggregations;

        // Save to database
        await _dbContext.DailyPlannings.AddAsync(dailyPlan, cancellationToken);
        await _dbContext.SaveChangesAsync(cancellationToken);

        // Populate initial route aggregations to Redis (with 0 weights)
        // RabbitMQ events will update these aggregations in real-time
        var initialAggregations = routeAggregations.Select(ra => new RouteAggregationSummary
        {
            // should copy all relevant fields from ra
            // this one for consistency id, createdat from entity
            Id = ra.Id,
            CreatedAt = ra.CreatedAt,
            // 
            DailyPlanningId = dailyPlan.Id, // real-plan id
            RouteKey = ra.RouteKey,
            FromOfficeId = ra.FromOfficeId,
            ToOfficeId = ra.ToOfficeId,
            FromTime = ra.FromTime,
            ToTime = ra.ToTime,
            TotalDurationMinutes = ra.TotalDurationMinutes,
            AverageDurationMinutes = ra.AverageDurationMinutes,
            EarliestStartTime = ra.EarliestStartTime,
            LatestEndTime = ra.LatestEndTime,
            TotalWeight = 0,
            TotalRealWeight = 0,
            TotalCalWeight = 0,
            TotalDiffWeight = 0,
            PriorityScore = 0,
            FromBusinessOperation = ra.FromBusinessOperation,
            ToBusinessOperation = ra.ToBusinessOperation
        }).ToList();

        _logger.LogInformation(
            "Generated daily plan {PlanId} ({PlanCode}) from template {TemplateId} for date {Date} with {Routes} route segments and populated to Redis",
            dailyPlan.Id, dailyPlan.Code, template.Id, executionDate, routeAggregations.Count);

        return initialAggregations;
    }

    public async Task GenerateNewDateFromTemplateAsync(
    Guid templateId,
    DateOnly executionDate,
    int? planCounter = null,
    CancellationToken cancellationToken = default)
    {
        var routeAggregationSummaries = await GeneratePlanFromTemplateAsync(
            templateId,
            executionDate,
            planCounter: null, // Let the generator determine the counter
            cancellationToken);

        await _dailyPlanningAggregationService.InitializeRouteAggregationsAsync(
        routeAggregationSummaries,
        _dailyPlanRepository,
        cancellationToken, 
        false);
    }
}
