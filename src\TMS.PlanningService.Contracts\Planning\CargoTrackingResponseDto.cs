﻿namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// Response DTO containing all route types for a mailer
/// </summary>
public class CargoTrackingResponseDto
{
    /// <summary>
    /// Master route information
    /// </summary>
    public MailerRouteMasterDto? Master { get; set; }

    /// <summary>
    /// Plan routes (from mailer_plan_routes)
    /// </summary>
    public List<CargoTrackingRouteDto> PlanRoutes { get; set; } = new();

    /// <summary>
    /// Adjust routes (from mailer_adjust_routes)
    /// </summary>
    public List<CargoTrackingRouteDto> AdjustRoutes { get; set; } = new();

    /// <summary>
    /// Actual routes (from mailer_actual_routes)
    /// </summary>
    public List<CargoTrackingRouteDto> ActualRoutes { get; set; } = new();
}

public class CargoTrackingRouteDto
{
    /// <summary>
    /// Mailer ID (Order ID)
    /// </summary>
    public string MailerId { get; set; } = string.Empty;

    /// <summary>
    /// Child Mailer ID (Order Item ID)
    /// </summary>
    public string ChildMailerId { get; set; } = string.Empty;

    /// <summary>
    /// Source post office ID
    /// </summary>
    public string? FromPostOfficeId { get; set; }

    /// <summary>
    /// Origin post office name
    /// </summary>
    public string? FromPostOfficeName { get; set; }

    /// <summary>
    /// Destination post office ID
    /// </summary>
    public string? ToPostOfficeId { get; set; }

    /// <summary>
    /// Destination post office name
    /// </summary>
    public string? ToPostOfficeName { get; set; }

    /// <summary>
    /// Lead time ID reference
    /// </summary>
    public string? LeadTimeId { get; set; }

    /// <summary>
    /// Transport provider type (e.g., "VNP" for Vietnam Post)
    /// </summary>
    public string? TransportProviderType { get; set; }

    /// <summary>
    /// Transport provider type name
    /// </summary>
    public string? TransportProviderTypeName { get; set; }

    /// <summary>
    /// Transport vehicle type (e.g., "TRUCK", "MOTORCYCLE")
    /// </summary>
    public string? TransportVehicleType { get; set; }

    /// <summary>
    /// Transport vehicle type name
    /// </summary>
    public string? TransportVehicleTypeName { get; set; }

    /// <summary>
    /// Transport method ID
    /// </summary>
    public string? TransportMethodId { get; set; }

    /// <summary>
    /// Transport method name
    /// </summary>
    public string? TransportMethodName { get; set; }

    /// <summary>
    /// Departure time from source office
    /// </summary>
    public DateTime? FromTime { get; set; }

    /// <summary>
    /// Time delay from departure (minutes)
    /// </summary>
    public int FromTimeDelay { get; set; }

    /// <summary>
    /// Arrival time at destination office
    /// </summary>
    public DateTime? ToTime { get; set; }

    /// <summary>
    /// Time delay to arrival (minutes)
    /// </summary>
    public int ToTimeDelay { get; set; }

    /// <summary>
    /// Additional days for delivery
    /// </summary>
    public int AddDays { get; set; }

    /// <summary>
    /// Step number in the route sequence
    /// </summary>
    public int Step { get; set; }

    /// <summary>
    /// Route type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Route type name
    /// </summary>
    public string? TypeName { get; set; }

    /// <summary>
    /// History ID for tracking changes
    /// </summary>
    public int HistoryId { get; set; }

    /// <summary>
    /// Previous step number
    /// </summary>
    public int? PreviousStep { get; set; }

    /// <summary>
    /// Lead time type ID
    /// </summary>
    public string? LeadTimeTypeId { get; set; }

    /// <summary>
    /// Lead time type name
    /// </summary>
    public string? LeadTimeTypeName { get; set; }

    /// <summary>
    /// Service type ID (e.g., "DE" for Express)
    /// </summary>
    public string? ServiceTypeId { get; set; }

    /// <summary>
    /// Service type name
    /// </summary>
    public string? ServiceTypeName { get; set; }

    /// <summary>
    /// Extra service code
    /// </summary>
    public string? ExtraService { get; set; }

    /// <summary>
    /// Extra service name
    /// </summary>
    public string? ExtraServiceName { get; set; }

    /// <summary>
    /// Master created date (for partition alignment)
    /// </summary>
    public DateTime MasterCreatedDate { get; set; }
}
