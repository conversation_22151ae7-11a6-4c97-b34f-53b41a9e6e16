﻿-- Set client encoding to UTF8
SET client_encoding = 'UTF8';

DO $$
DECLARE
    v_data_exists boolean;
BEGIN
    -- ===================================================================
    -- CHECKPOINT: ensure migration runs only once
    -- ===================================================================
    SELECT EXISTS (SELECT
    1
FROM
    pg_indexes
WHERE
    tablename = 'route_aggregations'
    AND indexname = 'idx_route_vehicle_type_gin'
    AND schemaname = 'public' limit 1) INTO v_data_exists;

    IF v_data_exists THEN
        RAISE NOTICE 'Migration already applied. Skipping initialization...';
        RETURN;
    END IF;

----- vehicle_type_breakdown_json
-- 1. Remove the old default that PostgreSQL can't cast
ALTER TABLE route_aggregations
    ALTER COLUMN vehicle_type_breakdown_json
    DROP DEFAULT;

-- 2. Change the column type, casting existing data
ALTER TABLE route_aggregations
    ALTER COLUMN vehicle_type_breakdown_json
    TYPE jsonb
    USING vehicle_type_breakdown_json::jsonb;

-- 3. Add the new default, explicitly cast as jsonb
-- Replace '{}' with '[]' or your actual desired default string literal if needed.
ALTER TABLE route_aggregations
    ALTER COLUMN vehicle_type_breakdown_json
    SET DEFAULT '{}'::jsonb;

----- transport_provider_breakdown_json
	-- 1. Remove the old default that PostgreSQL can't cast
ALTER TABLE route_aggregations
    ALTER COLUMN transport_provider_breakdown_json
    DROP DEFAULT;

-- 2. Change the column type, casting existing data
ALTER TABLE route_aggregations
    ALTER COLUMN transport_provider_breakdown_json
    TYPE jsonb
    USING transport_provider_breakdown_json::jsonb;

-- 3. Add the new default, explicitly cast as jsonb
-- Replace '{}' with '[]' or your actual desired default string literal if needed.
ALTER TABLE route_aggregations
    ALTER COLUMN transport_provider_breakdown_json
    SET DEFAULT '{}'::jsonb;

----- transport_method_breakdown_json
	-- 1. Remove the old default that PostgreSQL can't cast
ALTER TABLE route_aggregations
    ALTER COLUMN transport_method_breakdown_json
    DROP DEFAULT;

-- 2. Change the column type, casting existing data
ALTER TABLE route_aggregations
    ALTER COLUMN transport_method_breakdown_json
    TYPE jsonb
    USING transport_method_breakdown_json::jsonb;

-- 3. Add the new default, explicitly cast as jsonb
-- Replace '{}' with '[]' or your actual desired default string literal if needed.
ALTER TABLE route_aggregations
    ALTER COLUMN transport_method_breakdown_json
    SET DEFAULT '{}'::jsonb;

CREATE INDEX idx_route_vehicle_type_gin
ON route_aggregations
USING GIN (vehicle_type_breakdown_json);

CREATE INDEX idx_route_transport_provider_gin
ON route_aggregations
USING GIN (transport_provider_breakdown_json);

CREATE INDEX idx_route_transport_method_gin
ON route_aggregations
USING GIN (transport_method_breakdown_json);

END $$;

