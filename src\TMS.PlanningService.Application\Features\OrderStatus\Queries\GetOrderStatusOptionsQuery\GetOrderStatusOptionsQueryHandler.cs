﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MapsterMapper;
using MediatR;
using TMS.PlanningService.Contracts.Orders;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using Entity = TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Application.Features.OrderStatus.Queries.GetOrderStatusOptionsQuery;

public class GetOrderStatusOptionsQueryHandler : IRequestHandler<GetOrderStatusOptionsQuery, PagedResult<OrderStatusOptionsResponse?>>
{
    private readonly IMetadataCacheService _metadataService;
    private IMapper _mapper;


    public GetOrderStatusOptionsQueryHandler(IMetadataCacheService metadataService, IMapper mapper, ICurrentFactorProvider currentFactorProvider)
    {
        _metadataService = metadataService;
        _mapper = mapper;
    }

    public async Task<PagedResult<OrderStatusOptionsResponse>?> Handle(GetOrderStatusOptionsQuery request, CancellationToken cancellationToken)
    {
        var orderStatus = await _metadataService.GetOrderStatusesAsync<Entity.Metadata.OrderStatus>();
        
        var orderStatusGroupsOptions = orderStatus
            .Where(p => p.StatusGroupId == Constants.OrderStatusGroup)
            .OrderBy(x => x.StatusId).ToList();

        if (!orderStatusGroupsOptions.Any())
            return null;

        return new PagedResult<OrderStatusOptionsResponse>(_mapper.Map<List<OrderStatusOptionsResponse>>(orderStatusGroupsOptions), orderStatusGroupsOptions.Count, 1, orderStatusGroupsOptions.Count);

    }
}
