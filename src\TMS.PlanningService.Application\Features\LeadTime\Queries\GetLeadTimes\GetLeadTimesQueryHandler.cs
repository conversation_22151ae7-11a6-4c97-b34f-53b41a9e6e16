﻿using System.Linq.Expressions;
using LinqKit;
using Mapster;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.PlanningTemplate;
using TMS.PlanningService.Contracts.PmsSync;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;
using TMS.SharedKernel.Utilities;
using Entity = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Features.LeadTime.Queries.GetLeadTimes;

public class GetLeadTimesQueryHandler : IRequestHandler<GetLeadTimesQuery, PagedResult<LeadTimeConfigDto>>
{
    private readonly IBaseRepository<Entity.LeadTime> _leadTimeRepository;
    private readonly ICostServiceApi _costServiceApi;
    private readonly IMetadataCacheService _metadataCacheService;
    public GetLeadTimesQueryHandler(
        IBaseRepository<Entity.LeadTime> leadTimeRepository,
        ICostServiceApi costServiceApi,
        IMetadataCacheService metadataCacheService)
    {
        _leadTimeRepository = leadTimeRepository;
        _costServiceApi = costServiceApi;
        _metadataCacheService = metadataCacheService;
    }

    public async Task<PagedResult<LeadTimeConfigDto>> Handle(GetLeadTimesQuery request, CancellationToken cancellationToken)
    {
        var predicate = BuildPredicate(request.ParamRequest);

        var pagedResult = await _leadTimeRepository.GetPagedAsync(
            page: request.ParamRequest.Page,
            pageSize: request.ParamRequest.PageSize,
            predicate: predicate,
            selector: x => new LeadTimeConfigDto
            {
                LeadTimeId = x.LeadTimeId,
                FromOfficeId = x.FromOfficeId,
                ToOfficeId = x.ToOfficeId,
                FromZoneId = x.FromZoneId,
                ToZoneId = x.ToZoneId,
                StartTime = x.StartTime,
                EndTime = x.EndTime,
                AddDays = x.AddDays,
                LeadTimeTypeName = x.LeadTimeTypeName,
                TransportMethodName = x.TransportMethodName,
                TransportVehicleTypeName = x.TransportVehicleTypeName,
                IsActive = x.IsActive,
                LeadTimePartnerDto = new LeadTimePartnerDto
                {
                    Id = x.LeadTimePartner.Id,
                    LeadTimeId = x.LeadTimePartner.LeadTimeId,
                    FromTime = x.LeadTimePartner.FromTime,
                    ToTime = x.LeadTimePartner.ToTime,
                    FromAddDays = x.LeadTimePartner.FromAddDays,
                    ToAddDays = x.LeadTimePartner.ToAddDays,
                    PartnerId = x.LeadTimePartner.PartnerId,
                    SenderPostOffice = x.LeadTimePartner.SenderPostOffice,
                    ReceivePostOffice = x.LeadTimePartner.ReceivePostOffice,
                    Description = x.LeadTimePartner.Description
                }                                
            }
            , sortOptions: null
            , cancellationToken: cancellationToken);

        var pagingItems = pagedResult.Items;
        var partnersDict = new Dictionary<Guid, PartnerDto>();
        var postOfficesDict = new Dictionary<string, PostOfficeDto>();

        var partnerIds = pagingItems.Select(x => x.LeadTimePartnerDto?.PartnerId ?? Guid.Empty).ToHashSet();
        var offices = pagingItems
            .SelectMany(x => new string[]
            {
                x.FromOfficeId ?? "",
                x.ToOfficeId ?? "",
                x.LeadTimePartnerDto?.SenderPostOffice ?? "",
                x.LeadTimePartnerDto ?.ReceivePostOffice ?? ""
            })
            .Where(x => !string.IsNullOrEmpty(x))
            .Select(id => id)
            .ToHashSet();


        if (partnerIds != null && partnerIds.Count > 0)
            partnersDict = (await _costServiceApi.GetPartnersByIdsAsync(partnerIds)).ToDictionary(x => x.Id);

        if (offices != null && offices.Count > 0)
            postOfficesDict = await _metadataCacheService.GetPostOfficesAsync<PostOfficeDto>(offices.ToList());

        await MappingData(pagingItems, partnersDict, postOfficesDict);
        return new PagedResult<LeadTimeConfigDto>(
            pagingItems,
            pagedResult.TotalCount,
            pagedResult.PageNumber,
            pagedResult.PageSize);
    }

    private Expression<Func<Entity.LeadTime, bool>> BuildPredicate(GetLeadTimeRequest paramRequest)
    {
        var predicate = PredicateBuilder.New<Entity.LeadTime>(true);

        var transportProviderType = nameof(TransportProviderTypeEnum.KETNOIBT3);
        predicate = predicate.And(x => x.TransportProviderType == transportProviderType);
        
        if (!string.IsNullOrEmpty(paramRequest.SearchTerm))
        {
            var normalizedSearchTerm = (paramRequest.SearchTerm ?? "").RemoveAccents().ToLowerInvariant();
            predicate = predicate.And(x => EF.Functions.Like(x.FromOfficeId, $"%{normalizedSearchTerm}%")
                                        || EF.Functions.Like(x.ToOfficeId, $"%{normalizedSearchTerm}%"));
        }

        if (paramRequest.FromPostOfficeIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.FromPostOfficeIds.Contains(x.FromOfficeId ?? ""));

        if (paramRequest.ToPostOfficeIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.ToPostOfficeIds.Contains(x.ToOfficeId ?? ""));

        if (paramRequest.LeadTimeTypeIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.LeadTimeTypeIds.Contains(x.LeadTimeTypeId ?? ""));

        if (paramRequest.TransportMethodIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.TransportMethodIds.Contains(x.TransportMethodId ?? ""));

        if (paramRequest.TransportVehicleIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.TransportVehicleIds.Contains(x.TransportVehicleType ?? ""));

        if (paramRequest.PartnerIds?.Any() == true)
            predicate = predicate.And(x => x.LeadTimePartner != null 
                                      && paramRequest.PartnerIds.Contains(x.LeadTimePartner.PartnerId ?? Guid.Empty));

        if (paramRequest.PartnerFromOfficeIds?.Any() == true)
            predicate = predicate.And(x => x.LeadTimePartner != null 
                                      && paramRequest.PartnerFromOfficeIds.Contains(x.LeadTimePartner.SenderPostOffice ?? ""));

        if (paramRequest.PartnerToOfficeIds?.Any() == true)
            predicate = predicate.And(x => x.LeadTimePartner != null  
                                      && paramRequest.PartnerToOfficeIds.Contains(x.LeadTimePartner.ReceivePostOffice ?? ""));

        if (paramRequest.FromZoneIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.FromZoneIds.Contains(x.FromZoneId ?? ""));

        if (paramRequest.ToZoneIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.ToZoneIds.Contains(x.ToZoneId ?? ""));

        if (paramRequest.IsActive.HasValue)
            predicate = predicate.And(x => paramRequest.IsActive == x.IsActive);

        return predicate;
    }

    private async Task MappingData(
        IEnumerable<LeadTimeConfigDto> items, 
        Dictionary<Guid, PartnerDto> partnersDict, 
        Dictionary<string, PostOfficeDto> postOfficesDict)
    {
        foreach (var item in items) 
        {
            postOfficesDict = postOfficesDict ?? new Dictionary<string, PostOfficeDto>();
            partnersDict = partnersDict ?? new Dictionary<Guid, PartnerDto>();
            var formPostOffice = postOfficesDict.GetValueOrDefault(item.FromOfficeId)?.PostOfficeName;
            var toPostOffice = postOfficesDict.GetValueOrDefault(item.ToOfficeId)?.PostOfficeName;
            item.FromPostOfficeName = formPostOffice;
            item.ToPostOfficeName = toPostOffice;

            if (item.LeadTimePartnerDto != null)
            {
                var partnerName = partnersDict.GetValueOrDefault(item.LeadTimePartnerDto.PartnerId ?? Guid.Empty)?.PartnerName;
                var senderOffice = postOfficesDict.GetValueOrDefault(item.LeadTimePartnerDto.SenderPostOffice ?? "");
                var receiveOffice = postOfficesDict.GetValueOrDefault(item.LeadTimePartnerDto.ReceivePostOffice ?? "");

                item.LeadTimePartnerDto.PartnerName = partnerName;
                item.LeadTimePartnerDto.SenderPostOffice = senderOffice?.PostOfficeName;
                item.LeadTimePartnerDto.SenderPostOfficeAddress = senderOffice?.StreetAddress;
                item.LeadTimePartnerDto.ReceivePostOffice = receiveOffice?.PostOfficeName;
                item.LeadTimePartnerDto.ReceivePostOfficeAddress = receiveOffice?.StreetAddress;

            }    
        }
    }
}
