using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NPOI.SS.Formula.Functions;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Common.Queries;

/// <summary>
/// Base class for query handlers that implement hybrid Redis + PostgreSQL pagination
///
/// PATTERN: Tiered Data Access
/// - Pages 1-N (configurable): Try Redis first (hot data - recent)
/// - Pages N+: Fall back to hybrid Redis + DB (cold/historical data)
/// - Always deduplicate and prefer Redis data (fresher)
///
/// USAGE:
/// 1. Inherit from this base class
/// 2. Implement abstract methods for Redis/DB fetching
/// 3. Implement filtering, sorting, DTO mapping
/// 4. Call ExecuteHybridPaginationAsync() in your Handle method
///
/// EXAMPLE:
/// <code>
/// public class GetMyDataQueryHandler
///     : HybridPaginationQueryHandlerBase&lt;GetMyDataQuery, MyDto, MySummary, MyEntity&gt;
/// {
///     protected override async Task&lt;List&lt;MySummary&gt;&gt; FetchFromRedisAsync(...)
///     protected override async Task&lt;List&lt;MySummary&gt;&gt; FetchFromDatabaseAsync(...)
///     protected override List&lt;MySummary&gt; ApplyFilters(...)
///     protected override List&lt;MySummary&gt; ApplySorting(...)
///     protected override Task&lt;MyDto&gt; MapToDtoAsync(...)
///     protected override string GetUniqueKey(MySummary summary) => summary.Id;
/// }
/// </code>
/// </summary>
/// <typeparam name="TRequest">MediatR request type (query)</typeparam>
/// <typeparam name="TDto">DTO returned to client</typeparam>
/// <typeparam name="TSummary">In-memory summary/cache type from Redis</typeparam>
/// <typeparam name="TEntity">Database entity type</typeparam>
public abstract class HybridPaginationQueryHandlerBase<TRequest, TDto, TSummary, TEntity>
    where TRequest : IRequest<PagedResult<TDto>>
    where TEntity : class
{
    /// <summary>
    /// Configuration: Number of pages expected to be fully covered by Redis
    /// Default: 3 pages (adjust based on your Redis retention policy)
    /// </summary>
    protected virtual int RedisExpectedPageCoverage => 3;

    /// <summary>
    /// Logger for tracking data source and performance
    /// </summary>
    protected abstract ILogger Logger { get; }

    /// <summary>
    /// Estimated total count from metadata-first filtering (set by derived classes)
    /// Used for accurate pagination when implementing metadata-first optimization
    /// If null, falls back to counting actual filtered data
    /// </summary>
    protected int? EstimatedTotalCount { get; set; }

    /// <summary>
    /// Flag indicating whether pagination has already been done in FetchFromRedisAsync
    /// When true, base class will skip the pagination step (metadata-first optimization)
    /// When false (default), base class will paginate the data normally
    /// </summary>
    protected bool IsPaginationAlreadyDone { get; set; }

    /// <summary>
    /// Set of active priority plan IDs for filtering (if applicable)
    /// </summary>
    protected Dictionary<Guid, PriorityPlan>? ActivePriorityPlans { get; set; } = null;

    /// <summary>
    /// Main execution method - call this from your Handle method
    /// </summary>
    protected async Task<PagedResult<TDto>> ExecuteHybridPaginationAsync(
        TRequest request,
        int pageNumber,
        int pageSize,
        CancellationToken cancellationToken)
    {
        // Reset flags for each request
        EstimatedTotalCount = null;
        IsPaginationAlreadyDone = false;

        // STEP 1: Try Redis first (hot data - recent aggregations)
        Logger.LogInformation("Fetching data - Page {Page}, Size {Size}", pageNumber, pageSize);

        var redisData = await FetchFromRedisAsync(request, cancellationToken);

        // STEP 2: Check if Redis has sufficient data
        // IMPORTANT: If pagination is already done (metadata-first optimization),
        // trust that Redis has the correct data for this page, even if count < pageSize
        // This handles cases where filtering results in fewer items than pageSize
        var isRedisDataSufficient = IsPaginationAlreadyDone || redisData.Count >= pageSize;

        List<TSummary> finalData;
        string dataSource;

        if (isRedisDataSufficient && redisData.Any())
        {
            // Redis has enough data - use it (fast path)
            finalData = redisData;
            dataSource = "Redis";
            Logger.LogInformation("Using Redis data only - {Count} items found", redisData.Count);
        }
        else
        {
            // STEP 3: Fallback to hybrid Redis + DB approach
            Logger.LogWarning(
                "Redis data insufficient ({RedisCount}/{PageSize}) - falling back to database for page {Page}",
                redisData.Count,
                pageSize,
                pageNumber);

            // IMPORTANT: Reset metadata-first optimization flags for hybrid mode
            // The merged Redis+DB data needs to be filtered, sorted, and paginated by base class
            // Metadata-first optimization (pre-pagination) only applies to Redis-only mode
            IsPaginationAlreadyDone = false;
            EstimatedTotalCount = null;
            Logger.LogInformation("Reset metadata-first optimization flags for hybrid mode");

            var dbData = await FetchFromDatabaseAsync(request, cancellationToken);

            // STEP 4: Merge Redis + DB data (deduplicate by unique key)
            finalData = MergeAndDeduplicate(redisData, dbData);
            dataSource = redisData.Any() ? "Redis+DB (Hybrid)" : "DB";

            Logger.LogInformation(
                "Hybrid fetch complete - Redis: {RedisCount}, DB: {DbCount}, Final: {FinalCount}",
                redisData.Count,
                dbData.Count,
                finalData.Count);
        }

        // STEP 5: Apply filters and sorting (skip if already done in FetchFromRedisAsync)
        List<TSummary> filteredData;
        List<TSummary> sortedData;

        if (IsPaginationAlreadyDone)
        {
            // Metadata-first optimization: filtering, sorting, and pagination already done
            // Skip these steps to avoid double-filtering which would reduce item count below pageSize
            // IMPORTANT: Derived classes MUST apply ALL filters at metadata level before setting this flag
            filteredData = finalData;
            sortedData = finalData;
            Logger.LogInformation("Filtering and sorting already done in FetchFromRedisAsync - skipping");
        }
        else
        {
            // Standard mode: apply filters and sorting in-memory
            filteredData = ApplyFilters(finalData, request);
            sortedData = ApplySorting(filteredData, request);
        }

        // STEP 6: Paginate the final results (skip if already done in FetchFromRedisAsync)
        List<TSummary> pagedData;
        if (IsPaginationAlreadyDone)
        {
            // Metadata-first optimization: data is already paginated for current page
            pagedData = sortedData;
            Logger.LogInformation("Pagination already done in FetchFromRedisAsync - using pre-paginated data");
        }
        else
        {
            // Standard pagination: skip previous pages and take current page
            pagedData = sortedData
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        // STEP 7: Map to DTOs (with enrichment if needed)
        var dtos = new List<TDto>();
        foreach (var item in pagedData)
        {
            var dto = await MapToDtoAsync(item, dataSource, cancellationToken);
            dtos.Add(dto);
        }

        // STEP 8: Calculate total count
        // IMPORTANT: For high-volume data (100K+ records), derived classes MUST use metadata-first mode:
        // - Set IsPaginationAlreadyDone = true
        // - Set EstimatedTotalCount from metadata/indexes
        // - Return only current page data from FetchFromRedisAsync
        //
        // EstimatedTotalCount should be used when:
        // - Metadata-first optimization is enabled (fast, accurate count from indexes/metadata)
        // - Dataset is too large to load entirely into memory
        //
        // filteredData.Count should be used when:
        // - Small dataset already loaded into memory (hybrid fallback mode)
        // - We have the complete filtered dataset available
        var totalCount = EstimatedTotalCount ?? filteredData.Count;

        Logger.LogInformation(
            "Retrieved {Count} items from {Source} (Total: {Total}{EstimatedFlag}, Page: {Page}/{TotalPages})",
            dtos.Count,
            dataSource,
            totalCount,
            EstimatedTotalCount.HasValue ? " (estimated from metadata)" : "",
            pageNumber,
            (totalCount + pageSize - 1) / pageSize);

        return new PagedResult<TDto>(
            dtos,
            totalCount,
            pageNumber,
            pageSize);
    }

    /// <summary>
    /// Fetch data from Redis cache (hot data - typically last 7 days)
    /// </summary>
    protected abstract Task<List<TSummary>> FetchFromRedisAsync(
        TRequest request,
        CancellationToken cancellationToken);

    /// <summary>
    /// Fetch data from PostgreSQL database (cold/historical data)
    /// Apply filters at DB level for performance
    /// Fetch extra records (e.g., pageSize * 3) to account for in-memory filtering
    /// </summary>
    protected abstract Task<List<TSummary>> FetchFromDatabaseAsync(
        TRequest request,
        CancellationToken cancellationToken);

    /// <summary>
    /// Apply request filters to the data (in-memory)
    /// This runs after data is fetched from Redis/DB
    /// </summary>
    protected abstract List<TSummary> ApplyFilters(
        List<TSummary> data,
        TRequest request);

    /// <summary>
    /// Apply sorting to the filtered data (in-memory)
    /// </summary>
    protected abstract List<TSummary> ApplySorting(
        List<TSummary> data,
        TRequest request);

    /// <summary>
    /// Map summary to DTO with optional enrichment (e.g., fetch related data)
    /// The dataSource parameter can be used for debugging/monitoring
    /// </summary>
    protected abstract Task<TDto> MapToDtoAsync(
        TSummary summary,
        string dataSource,
        CancellationToken cancellationToken);

    /// <summary>
    /// Get unique key for deduplication (e.g., ID, composite key)
    /// Used to prevent duplicates when merging Redis + DB data
    /// </summary>
    protected abstract string GetUniqueKey(TSummary summary);

    /// <summary>
    /// Merges Redis and DB data, preferring Redis data (fresher)
    /// Deduplicates by unique key
    /// </summary>
    private List<TSummary> MergeAndDeduplicate(
        List<TSummary> redisData,
        List<TSummary> dbData)
    {
        // Use unique key as identifier
        var redisKeys = redisData.Select(GetUniqueKey).ToHashSet();

        // Take all Redis data + DB data that doesn't exist in Redis
        var merged = redisData
            .Concat(dbData.Where(db => !redisKeys.Contains(GetUniqueKey(db))))
            .ToList();

        return merged;
    }

    /// <summary>
    /// Calculates the estimated total count when exact count is not available.
    /// Sums items already skipped (previous pages) plus items in current batch.
    ///
    /// IMPORTANT: This is an ESTIMATE, not exact count.
    /// For exact count, derived classes should set EstimatedTotalCount from metadata filtering.
    /// </summary>
    /// <param name="totalItems">
    ///     Number of items in the current batch (after merging Redis + DB data).
    ///     Represents items available from current page onwards.
    /// </param>
    /// <param name="pageSize">Number of items per page.</param>
    /// <param name="pageNumber">The current page number (1-based index).</param>
    /// <returns>
    /// Estimated total count = (items already skipped) + (items in current batch)
    /// </returns>
    private int CalculateTotalItems(int totalItems, int pageSize, int pageNumber)
    {
        // Calculate how many items were skipped to reach current page
        int itemsAlreadySkipped = (pageNumber - 1) * pageSize;

        // Total = skipped items + items in current batch
        int estimatedTotal = itemsAlreadySkipped + totalItems;

        return estimatedTotal;
    }

}
