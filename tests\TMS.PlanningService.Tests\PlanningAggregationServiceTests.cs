using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using TMS.PlanningService.Application.Services.Orders;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Orders;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernal.SmoothRedis;
using Xunit;

namespace TMS.PlanningService.Tests;

// Test wrapper to expose protected methods for unit testing
public class TestablePlanningAggregationService : PlanningAggregationService
{
    public TestablePlanningAggregationService(
        ILogger<PlanningAggregationService> logger,
        ISmoothRedis redis,
        IOrderDataService orderDataService)
        : base(logger, redis, orderDataService)
    {
    }

    // Expose protected methods as public for testing
    public Task PublicUpdateSingleRouteAggregationAsync(
        string routeKey,
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        bool isUsingAdjustRoutes,
        CancellationToken cancellationToken,
        object? originalPlanningEvent = null)
    {
        return UpdateSingleRouteAggregationAsync(routeKey, routes, actualRoutes, isUsingAdjustRoutes, cancellationToken, originalPlanningEvent);
    }

    public Task PublicRemoveOrderOnRouteAsync(
        List<MailerPlanRoute> adjustRoutes,
        bool isUsingAdjustRoutes,
        List<MailerPlanRoute> planRoutes,
        CancellationToken cancellationToken)
    {
        return RemoveOrderOnRouteAsync(adjustRoutes, isUsingAdjustRoutes, planRoutes, cancellationToken);
    }
}

public class PlanningAggregationServiceTests
{
    private readonly Mock<ILogger<PlanningAggregationService>> _loggerMock;
    private readonly Mock<ISmoothRedis> _redisMock;
    private readonly Mock<IOrderDataService> _orderDataServiceMock;
    private readonly TestablePlanningAggregationService _service;

    public PlanningAggregationServiceTests()
    {
        _loggerMock = new Mock<ILogger<PlanningAggregationService>>();
        _redisMock = new Mock<ISmoothRedis>();
        _orderDataServiceMock = new Mock<IOrderDataService>();

        _service = new TestablePlanningAggregationService(
            _loggerMock.Object,
            _redisMock.Object,
            _orderDataServiceMock.Object);
    }

    #region UpdateSingleRouteAggregationAsync Tests

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldThrowException_WhenLockAcquisitionFails()
    {
        // Arrange
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var routes = CreateTestRoutes();
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;

        // Setup lock to return null (acquisition failed)
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.IsAny<string>(),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<InvalidOperationException>(
            () => _service.PublicUpdateSingleRouteAggregationAsync(
                routeKey,
                routes,
                actualRoutes,
                isUsingAdjustRoutes: false,
                cancellationToken));

        exception.Message.Should().Contain("Failed to acquire lock");
        exception.Message.Should().Contain(routeKey);
    }

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldCreateNewAggregation_WhenNotExistsInCache()
    {
        // Arrange
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var routes = CreateTestRoutes();
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;
        var mockLock = new Mock<IRedisLock>();

        // Setup lock acquisition success
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.IsAny<string>(),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        // Setup cache to return null (no existing aggregation)
        _redisMock.Setup(r => r.Cache.GetAsync<RouteAggregationSummary>(It.IsAny<string>()))
            .ReturnsAsync((RouteAggregationSummary?)null);

        // Setup order data service to return empty metrics
        _orderDataServiceMock.Setup(o => o.GetOrderMetricsByOrderIdsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>(),
                It.IsAny<object?>()))
            .ReturnsAsync(new Dictionary<string, OrderMetrics>());

        // Setup cache set
        _redisMock.Setup(r => r.Cache.SetAsync(
                It.IsAny<string>(),
                It.IsAny<RouteAggregationSummary>(),
                It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        // Setup metadata update - return null lock to skip metadata update
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                "all-routes",
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act
        await _service.PublicUpdateSingleRouteAggregationAsync(
            routeKey,
            routes,
            actualRoutes,
            isUsingAdjustRoutes: false,
            cancellationToken);

        // Assert
        _redisMock.Verify(r => r.Cache.GetAsync<RouteAggregationSummary>(
            It.Is<string>(key => key.Contains(routeKey))), Times.Once);

        mockLock.Verify(l => l.DisposeAsync(), Times.Once);
    }

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldUpdateExistingAggregation_WithOrderMetrics()
    {
        // Arrange
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var mailerId = "MAILER001";
        var childMailerId = "CHILD001";
        var routes = CreateTestRoutesWithMailer(mailerId, childMailerId);
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;
        var mockLock = new Mock<IRedisLock>();

        // Create existing aggregation
        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 0,
            TotalItems = 0,
            TotalWeight = 0,
            OrderDetails = new Dictionary<string, OrderOnRoute>(),
            CreatedAt = DateTime.UtcNow.AddHours(-1),
            AggregatedAt = DateTime.UtcNow.AddHours(-1)
        };

        // Setup lock acquisition success
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        // Setup cache to return existing aggregation
        _redisMock.Setup(r => r.Cache.GetAsync<RouteAggregationSummary>(It.IsAny<string>()))
            .ReturnsAsync(existingAggregation);

        // Setup order metrics
        var orderMetrics = new Dictionary<string, OrderMetrics>
        {
            {
                mailerId, new OrderMetrics
                {
                    OrderId = mailerId,
                    CurrentStatusId = "1",
                    Weight = 10.5m,
                    RealWeight = 12.0m,
                    CalWeight = 11.0m,
                    IsDeleted = false,
                    Items = new List<OrderItemMetric>
                    {
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = childMailerId,
                            Weight = 10.5m,
                            RealWeight = 12.0m,
                            CalWeight = 11.0m,
                            CurrentStatusId = "1"
                        }
                    }
                }
            }
        };

        _orderDataServiceMock.Setup(o => o.GetOrderMetricsByOrderIdsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>(),
                It.IsAny<object?>()))
            .ReturnsAsync(orderMetrics);

        // Setup cache set
        RouteAggregationSummary? capturedSummary = null;
        _redisMock.Setup(r => r.Cache.SetAsync(
                It.IsAny<string>(),
                It.IsAny<RouteAggregationSummary>(),
                It.IsAny<TimeSpan>()))
            .Callback<string, RouteAggregationSummary, TimeSpan?>((key, summary, expiry) =>
            {
                capturedSummary = summary;
            })
            .Returns(Task.CompletedTask);

        // Setup metadata update - return null lock to skip metadata update
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                "all-routes",
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act
        await _service.PublicUpdateSingleRouteAggregationAsync(
            routeKey,
            routes,
            actualRoutes,
            isUsingAdjustRoutes: false,
            cancellationToken);

        // Assert
        capturedSummary.Should().NotBeNull();
        capturedSummary!.TotalOrders.Should().Be(1);
        capturedSummary.TotalItems.Should().Be(1);
        capturedSummary.TotalWeight.Should().Be(10.5m);
        capturedSummary.TotalRealWeight.Should().Be(12.0m);
        capturedSummary.TotalCalWeight.Should().Be(11.0m);
        capturedSummary.OrderDetails.Should().ContainKey(mailerId);
        capturedSummary.OrderDetails[mailerId].Items.Should().HaveCount(1);

        mockLock.Verify(l => l.DisposeAsync(), Times.Once);
    }

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldSkipDeletedOrders()
    {
        // Arrange
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var mailerId = "MAILER001";
        var childMailerId = "CHILD001";
        var routes = CreateTestRoutesWithMailer(mailerId, childMailerId);
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;
        var mockLock = new Mock<IRedisLock>();

        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            OrderDetails = new Dictionary<string, OrderOnRoute>(),
            CreatedAt = DateTime.UtcNow
        };

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Cache.GetAsync<RouteAggregationSummary>(It.IsAny<string>()))
            .ReturnsAsync(existingAggregation);

        // Setup deleted order metrics
        var orderMetrics = new Dictionary<string, OrderMetrics>
        {
            {
                mailerId, new OrderMetrics
                {
                    OrderId = mailerId,
                    CurrentStatusId = "1",
                    Weight = 10.5m,
                    IsDeleted = true, // Deleted order
                    Items = new List<OrderItemMetric>
                    {
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = childMailerId,
                            Weight = 10.5m,
                            CurrentStatusId = "1"
                        }
                    }
                }
            }
        };

        _orderDataServiceMock.Setup(o => o.GetOrderMetricsByOrderIdsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>(),
                It.IsAny<object?>()))
            .ReturnsAsync(orderMetrics);

        _redisMock.Setup(r => r.Cache.SetAsync(
                It.IsAny<string>(),
                It.IsAny<RouteAggregationSummary>(),
                It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                "all-routes",
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act
        await _service.PublicUpdateSingleRouteAggregationAsync(
            routeKey,
            routes,
            actualRoutes,
            isUsingAdjustRoutes: false,
            cancellationToken);

        // Assert - deleted orders should not be counted
        _redisMock.Verify(r => r.Cache.SetAsync(
            It.IsAny<string>(),
            It.IsAny<RouteAggregationSummary>(),
            It.IsAny<TimeSpan>()), Times.Never); // Should not save if no valid orders
    }

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldSkipDeliveredOrders()
    {
        // Arrange
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var mailerId = "MAILER001";
        var childMailerId = "CHILD001";
        var routes = CreateTestRoutesWithMailer(mailerId, childMailerId);
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;
        var mockLock = new Mock<IRedisLock>();

        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            OrderDetails = new Dictionary<string, OrderOnRoute>(),
            CreatedAt = DateTime.UtcNow
        };

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Cache.GetAsync<RouteAggregationSummary>(It.IsAny<string>()))
            .ReturnsAsync(existingAggregation);

        // Setup delivered order metrics
        var orderMetrics = new Dictionary<string, OrderMetrics>
        {
            {
                mailerId, new OrderMetrics
                {
                    OrderId = mailerId,
                    CurrentStatusId = "6", // DELIVERED status
                    Weight = 10.5m,
                    IsDeleted = false,
                    Items = new List<OrderItemMetric>
                    {
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = childMailerId,
                            Weight = 10.5m,
                            CurrentStatusId = "6"
                        }
                    }
                }
            }
        };

        _orderDataServiceMock.Setup(o => o.GetOrderMetricsByOrderIdsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>(),
                It.IsAny<object?>()))
            .ReturnsAsync(orderMetrics);

        _redisMock.Setup(r => r.Cache.SetAsync(
                It.IsAny<string>(),
                It.IsAny<RouteAggregationSummary>(),
                It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                "all-routes",
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act
        await _service.PublicUpdateSingleRouteAggregationAsync(
            routeKey,
            routes,
            actualRoutes,
            isUsingAdjustRoutes: false,
            cancellationToken);

        // Assert - delivered orders should not be counted
        _redisMock.Verify(r => r.Cache.SetAsync(
            It.IsAny<string>(),
            It.IsAny<RouteAggregationSummary>(),
            It.IsAny<TimeSpan>()), Times.Never);
    }

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldUpdateExistingOrderItem_WhenAlreadyExists()
    {
        // Arrange
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var mailerId = "MAILER001";
        var childMailerId = "CHILD001";
        var routes = CreateTestRoutesWithMailer(mailerId, childMailerId);
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;
        var mockLock = new Mock<IRedisLock>();

        // Create existing aggregation with order already present
        var existingOrder = new OrderOnRoute
        {
            MailerId = mailerId,
            Status = "1",
            Weight = 5.0m,
            RealWeight = 6.0m,
            CalWeight = 5.5m,
            Items = new List<OrderItemDetail>
            {
                new OrderItemDetail
                {
                    MailerId = mailerId,
                    ChildMailerId = childMailerId,
                    Weight = 5.0m,
                    RealWeight = 6.0m,
                    CalWeight = 5.5m,
                    Status = "1",
                    IsDeleted = false
                }
            }
        };

        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 1,
            TotalItems = 1,
            TotalWeight = 5.0m,
            TotalRealWeight = 6.0m,
            TotalCalWeight = 5.5m,
            OrderDetails = new Dictionary<string, OrderOnRoute>
            {
                { mailerId, existingOrder }
            },
            CreatedAt = DateTime.UtcNow.AddHours(-1),
            AggregatedAt = DateTime.UtcNow.AddHours(-1)
        };

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Cache.GetAsync<RouteAggregationSummary>(It.IsAny<string>()))
            .ReturnsAsync(existingAggregation);

        // Setup updated order metrics
        var orderMetrics = new Dictionary<string, OrderMetrics>
        {
            {
                mailerId, new OrderMetrics
                {
                    OrderId = mailerId,
                    CurrentStatusId = "2", // Updated status
                    Weight = 10.5m, // Updated weight
                    RealWeight = 12.0m,
                    CalWeight = 11.0m,
                    IsDeleted = false,
                    Items = new List<OrderItemMetric>
                    {
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = childMailerId,
                            Weight = 10.5m,
                            RealWeight = 12.0m,
                            CalWeight = 11.0m,
                            CurrentStatusId = "2"
                        }
                    }
                }
            }
        };

        _orderDataServiceMock.Setup(o => o.GetOrderMetricsByOrderIdsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>(),
                It.IsAny<object?>()))
            .ReturnsAsync(orderMetrics);

        RouteAggregationSummary? capturedSummary = null;
        _redisMock.Setup(r => r.Cache.SetAsync(
                It.IsAny<string>(),
                It.IsAny<RouteAggregationSummary>(),
                It.IsAny<TimeSpan>()))
            .Callback<string, RouteAggregationSummary, TimeSpan?>((key, summary, expiry) =>
            {
                capturedSummary = summary;
            })
            .Returns(Task.CompletedTask);

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                "all-routes",
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act
        await _service.PublicUpdateSingleRouteAggregationAsync(
            routeKey,
            routes,
            actualRoutes,
            isUsingAdjustRoutes: false,
            cancellationToken);

        // Assert - existing item should be updated
        capturedSummary.Should().NotBeNull();
        capturedSummary!.TotalOrders.Should().Be(1); // Still 1 order
        capturedSummary.OrderDetails[mailerId].Items[0].Weight.Should().Be(10.5m);
        capturedSummary.OrderDetails[mailerId].Items[0].Status.Should().Be("2");
        capturedSummary.OrderDetails[mailerId].Items[0].IsDeleted.Should().BeFalse();
    }

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldProcessMultipleChildMailers_ForSameOrder()
    {
        // Arrange - This tests the fix for Issue #3: Wrong childMailerId selection
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var mailerId = "MAILER001";
        var routes = new List<MailerPlanRoute>
        {
            CreateTestRoute(mailerId, "CHILD001", "OFFICE1", "OFFICE2"),
            CreateTestRoute(mailerId, "CHILD002", "OFFICE1", "OFFICE2"),
            CreateTestRoute(mailerId, "CHILD003", "OFFICE1", "OFFICE2")
        };
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;
        var mockLock = new Mock<IRedisLock>();

        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 0,
            TotalItems = 0,
            TotalWeight = 0,
            OrderDetails = new Dictionary<string, OrderOnRoute>(),
            CreatedAt = DateTime.UtcNow
        };

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Cache.GetAsync<RouteAggregationSummary>(It.IsAny<string>()))
            .ReturnsAsync(existingAggregation);

        // Setup order metrics with 3 child items
        var orderMetrics = new Dictionary<string, OrderMetrics>
        {
            {
                mailerId, new OrderMetrics
                {
                    OrderId = mailerId,
                    CurrentStatusId = "1",
                    Weight = 30.0m,
                    RealWeight = 33.0m,
                    CalWeight = 31.5m,
                    IsDeleted = false,
                    Items = new List<OrderItemMetric>
                    {
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = "CHILD001",
                            Weight = 10.0m,
                            RealWeight = 11.0m,
                            CalWeight = 10.5m,
                            CurrentStatusId = "1"
                        },
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = "CHILD002",
                            Weight = 10.0m,
                            RealWeight = 11.0m,
                            CalWeight = 10.5m,
                            CurrentStatusId = "1"
                        },
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = "CHILD003",
                            Weight = 10.0m,
                            RealWeight = 11.0m,
                            CalWeight = 10.5m,
                            CurrentStatusId = "1"
                        }
                    }
                }
            }
        };

        _orderDataServiceMock.Setup(o => o.GetOrderMetricsByOrderIdsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>(),
                It.IsAny<object?>()))
            .ReturnsAsync(orderMetrics);

        RouteAggregationSummary? capturedSummary = null;
        _redisMock.Setup(r => r.Cache.SetAsync(
                It.IsAny<string>(),
                It.IsAny<RouteAggregationSummary>(),
                It.IsAny<TimeSpan>()))
            .Callback<string, RouteAggregationSummary, TimeSpan?>((key, summary, expiry) =>
            {
                capturedSummary = summary;
            })
            .Returns(Task.CompletedTask);

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                "all-routes",
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act
        await _service.PublicUpdateSingleRouteAggregationAsync(
            routeKey,
            routes,
            actualRoutes,
            isUsingAdjustRoutes: false,
            cancellationToken);

        // Assert - All 3 child mailers should be processed
        capturedSummary.Should().NotBeNull();
        capturedSummary!.TotalOrders.Should().Be(1, "there's only 1 order");
        capturedSummary.TotalItems.Should().Be(3, "there are 3 child items");
        capturedSummary.TotalWeight.Should().Be(30.0m, "sum of all 3 items");
        capturedSummary.TotalRealWeight.Should().Be(33.0m);
        capturedSummary.TotalCalWeight.Should().Be(31.5m);
        capturedSummary.OrderDetails.Should().ContainKey(mailerId);
        capturedSummary.OrderDetails[mailerId].Items.Should().HaveCount(3, "all 3 child mailers should be added");
        capturedSummary.OrderDetails[mailerId].Items.Should().Contain(i => i.ChildMailerId == "CHILD001");
        capturedSummary.OrderDetails[mailerId].Items.Should().Contain(i => i.ChildMailerId == "CHILD002");
        capturedSummary.OrderDetails[mailerId].Items.Should().Contain(i => i.ChildMailerId == "CHILD003");
    }

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldUpdateWeights_WhenActiveItemWeightChanges()
    {
        // Arrange - This tests the fix for Issue #2: Missing weight updates for active items
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var mailerId = "MAILER001";
        var childMailerId = "CHILD001";
        var routes = CreateTestRoutesWithMailer(mailerId, childMailerId);
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;
        var mockLock = new Mock<IRedisLock>();

        // Existing aggregation with item weight = 5kg
        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 1,
            TotalItems = 1,
            TotalWeight = 5.0m,
            TotalRealWeight = 5.5m,
            TotalCalWeight = 5.2m,
            TotalDiffWeight = 0.3m,
            OrderDetails = new Dictionary<string, OrderOnRoute>
            {
                {
                    mailerId, new OrderOnRoute
                    {
                        MailerId = mailerId,
                        TotalItems = 1,
                        Weight = 5.0m,
                        RealWeight = 5.5m,
                        CalWeight = 5.2m,
                        Items = new List<OrderItemDetail>
                        {
                            new OrderItemDetail
                            {
                                MailerId = mailerId,
                                ChildMailerId = childMailerId,
                                Weight = 5.0m,
                                RealWeight = 5.5m,
                                CalWeight = 5.2m,
                                Status = "1",
                                IsDeleted = false
                            }
                        }
                    }
                }
            },
            CreatedAt = DateTime.UtcNow
        };

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Cache.GetAsync<RouteAggregationSummary>(It.IsAny<string>()))
            .ReturnsAsync(existingAggregation);

        // Order metrics with UPDATED weight = 10kg (was 5kg)
        var orderMetrics = new Dictionary<string, OrderMetrics>
        {
            {
                mailerId, new OrderMetrics
                {
                    OrderId = mailerId,
                    CurrentStatusId = "1",
                    Weight = 10.0m,
                    RealWeight = 11.0m,
                    CalWeight = 10.5m,
                    IsDeleted = false,
                    Items = new List<OrderItemMetric>
                    {
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = childMailerId,
                            Weight = 10.0m,  // Changed from 5.0 to 10.0
                            RealWeight = 11.0m,
                            CalWeight = 10.5m,
                            CurrentStatusId = "1"
                        }
                    }
                }
            }
        };

        _orderDataServiceMock.Setup(o => o.GetOrderMetricsByOrderIdsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>(),
                It.IsAny<object?>()))
            .ReturnsAsync(orderMetrics);

        RouteAggregationSummary? capturedSummary = null;
        _redisMock.Setup(r => r.Cache.SetAsync(
                It.IsAny<string>(),
                It.IsAny<RouteAggregationSummary>(),
                It.IsAny<TimeSpan>()))
            .Callback<string, RouteAggregationSummary, TimeSpan?>((key, summary, expiry) =>
            {
                capturedSummary = summary;
            })
            .Returns(Task.CompletedTask);

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                "all-routes",
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act
        await _service.PublicUpdateSingleRouteAggregationAsync(
            routeKey,
            routes,
            actualRoutes,
            isUsingAdjustRoutes: false,
            cancellationToken);

        // Assert - Weights should be updated to new values
        capturedSummary.Should().NotBeNull();
        capturedSummary!.TotalOrders.Should().Be(1, "order count unchanged");
        capturedSummary.TotalItems.Should().Be(1, "item count unchanged");
        capturedSummary.TotalWeight.Should().Be(10.0m, "weight updated from 5kg to 10kg");
        capturedSummary.TotalRealWeight.Should().Be(11.0m, "real weight updated");
        capturedSummary.TotalCalWeight.Should().Be(10.5m, "cal weight updated");
        capturedSummary.TotalDiffWeight.Should().Be(0.5m, "diff weight recalculated");
        capturedSummary.OrderDetails[mailerId].Weight.Should().Be(10.0m);
        capturedSummary.OrderDetails[mailerId].Items[0].Weight.Should().Be(10.0m);
    }

    [Fact]
    public async Task UpdateSingleRouteAggregationAsync_ShouldHandleNullOrderItem_Gracefully()
    {
        // Arrange - This tests the fix for Issue #5: Null orderItem risk
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var mailerId = "MAILER001";
        var childMailerId = "CHILD_NOT_FOUND";  // This child ID doesn't exist in metrics
        var routes = CreateTestRoutesWithMailer(mailerId, childMailerId);
        var actualRoutes = new List<MailerActualRoute>();
        var cancellationToken = CancellationToken.None;
        var mockLock = new Mock<IRedisLock>();

        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 0,
            TotalItems = 0,
            TotalWeight = 0,
            OrderDetails = new Dictionary<string, OrderOnRoute>(),
            CreatedAt = DateTime.UtcNow
        };

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Cache.GetAsync<RouteAggregationSummary>(It.IsAny<string>()))
            .ReturnsAsync(existingAggregation);

        // Order metrics WITHOUT the requested child item
        var orderMetrics = new Dictionary<string, OrderMetrics>
        {
            {
                mailerId, new OrderMetrics
                {
                    OrderId = mailerId,
                    CurrentStatusId = "1",
                    Weight = 10.0m,
                    RealWeight = 11.0m,
                    CalWeight = 10.5m,
                    IsDeleted = false,
                    Items = new List<OrderItemMetric>
                    {
                        new OrderItemMetric
                        {
                            OrderId = mailerId,
                            OrderItemId = "DIFFERENT_CHILD",  // Different from requested
                            Weight = 10.0m,
                            RealWeight = 11.0m,
                            CalWeight = 10.5m,
                            CurrentStatusId = "1"
                        }
                    }
                }
            }
        };

        _orderDataServiceMock.Setup(o => o.GetOrderMetricsByOrderIdsAsync(
                It.IsAny<List<string>>(),
                It.IsAny<CancellationToken>(),
                It.IsAny<object?>()))
            .ReturnsAsync(orderMetrics);

        _redisMock.Setup(r => r.Cache.SetAsync(
                It.IsAny<string>(),
                It.IsAny<RouteAggregationSummary>(),
                It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                "all-routes",
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null);

        // Act - Should NOT throw NullReferenceException
        await _service.PublicUpdateSingleRouteAggregationAsync(
            routeKey,
            routes,
            actualRoutes,
            isUsingAdjustRoutes: false,
            cancellationToken);

        // Assert - Cache should NOT be updated (no valid items found)
        // The method gracefully handles missing order item by logging warning and continuing
        _redisMock.Verify(r => r.Cache.SetAsync(
            It.IsAny<string>(),
            It.IsAny<RouteAggregationSummary>(),
            It.IsAny<TimeSpan>()), Times.Never, "aggregation should not be cached when no valid items found");
    }

    #endregion

    #region RemoveOrderOnRouteAsync Tests

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldReturnEarly_WhenNotUsingAdjustRoutes()
    {
        // Arrange
        var adjustRoutes = CreateTestRoutes();
        var planRoutes = CreateTestRoutes();
        var cancellationToken = CancellationToken.None;

        // Act
        await _service.PublicRemoveOrderOnRouteAsync(
            adjustRoutes,
            isUsingAdjustRoutes: false,
            planRoutes,
            cancellationToken);

        // Assert - no Redis operations should be performed
        _redisMock.Verify(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(It.IsAny<string>()), Times.Never);
        _redisMock.Verify(r => r.Locks.AcquireAsync(It.IsAny<string>(), It.IsAny<TimeSpan>(), It.IsAny<TimeSpan>()), Times.Never);
    }

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldReturnEarly_WhenNoMetadataFound()
    {
        // Arrange
        var adjustRoutes = CreateTestRoutes();
        var planRoutes = CreateTestRoutes();
        var cancellationToken = CancellationToken.None;

        // Setup metadata to return empty
        _redisMock.Setup(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(It.IsAny<string>()))
            .ReturnsAsync((Dictionary<string, RouteMetadata>?)null);

        // Act
        await _service.PublicRemoveOrderOnRouteAsync(
            adjustRoutes,
            isUsingAdjustRoutes: true,
            planRoutes,
            cancellationToken);

        // Assert - no lock acquisitions should happen
        _redisMock.Verify(r => r.Locks.AcquireAsync(
            It.Is<string>(s => s.StartsWith("route:")),
            It.IsAny<TimeSpan>(),
            It.IsAny<TimeSpan>()), Times.Never);
    }

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldRemoveOrders_WhenMatchingRoutesFound()
    {
        // Arrange
        var mailerId = "MAILER001";
        var childMailerId = "CHILD001";
        var adjustRoutes = CreateTestRoutes();
        var planRoutes = CreateTestRoutesWithMailer(mailerId, childMailerId);
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var cancellationToken = CancellationToken.None;

        // Setup metadata
        var metadata = new Dictionary<string, RouteMetadata>
        {
            {
                routeKey, new RouteMetadata
                {
                    RouteKey = routeKey,
                    AggregatedAt = DateTime.UtcNow
                }
            }
        };

        _redisMock.Setup(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(It.IsAny<string>()))
            .ReturnsAsync(metadata);

        // Setup existing aggregation with order
        var existingOrder = new OrderOnRoute
        {
            MailerId = mailerId,
            Status = "1",
            Weight = 10.5m,
            RealWeight = 12.0m,
            CalWeight = 11.0m,
            TotalItems = 1,
            Items = new List<OrderItemDetail>
            {
                new OrderItemDetail
                {
                    MailerId = mailerId,
                    ChildMailerId = childMailerId,
                    Weight = 10.5m,
                    RealWeight = 12.0m,
                    CalWeight = 11.0m,
                    Status = "1",
                    IsDeleted = false
                }
            }
        };

        var existingSummary = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 1,
            TotalItems = 1,
            TotalWeight = 10.5m,
            TotalRealWeight = 12.0m,
            TotalCalWeight = 11.0m,
            TotalDiffWeight = 1.0m,
            OrderDetails = new Dictionary<string, OrderOnRoute>
            {
                { mailerId, existingOrder }
            },
            CreatedAt = DateTime.UtcNow
        };

        var mockLock = new Mock<IRedisLock>();
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        // Setup batch get
        var cacheKey = $"planning:route-aggregation:{routeKey}";
        var batchResults = new Dictionary<string, RouteAggregationSummary?>
        {
            { cacheKey, existingSummary }
        };

        _redisMock.Setup(r => r.Batch.Cache.GetManyAsync<RouteAggregationSummary>(It.IsAny<string[]>()))
            .ReturnsAsync(batchResults);

        // Setup batch set
        Dictionary<string, RouteAggregationSummary>? capturedSummaries = null;
        _redisMock.Setup(r => r.Batch.Cache.SetManyAsync(
                It.IsAny<Dictionary<string, RouteAggregationSummary>>(),
                It.IsAny<TimeSpan>()))
            .Callback<Dictionary<string, RouteAggregationSummary>, TimeSpan?>((summaries, expiry) =>
            {
                capturedSummaries = summaries;
            })
            .Returns(Task.CompletedTask);

        // Act
        await _service.PublicRemoveOrderOnRouteAsync(
            adjustRoutes,
            isUsingAdjustRoutes: true,
            planRoutes,
            cancellationToken);

        // Assert
        capturedSummaries.Should().NotBeNull();
        capturedSummaries!.Should().ContainKey(cacheKey);

        var updatedSummary = capturedSummaries[cacheKey];
        updatedSummary.TotalOrders.Should().Be(0); // Order should be removed
        updatedSummary.TotalItems.Should().Be(0);
        updatedSummary.TotalWeight.Should().Be(0);
        updatedSummary.OrderDetails[mailerId].Items[0].IsDeleted.Should().BeTrue();

        mockLock.Verify(l => l.DisposeAsync(), Times.Once);
    }

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldHandlePartialLockFailures_Gracefully()
    {
        // Arrange
        var mailerId1 = "MAILER001";
        var mailerId2 = "MAILER002";
        var childMailerId1 = "CHILD001";
        var childMailerId2 = "CHILD002";
        var adjustRoutes = CreateTestRoutes();

        var planRoutes = new List<MailerPlanRoute>
        {
            CreateTestRoute(mailerId1, childMailerId1, "OFFICE1", "OFFICE2"),
            CreateTestRoute(mailerId2, childMailerId2, "OFFICE3", "OFFICE4")
        };

        var routeKey1 = "202510280800:202510281000:OFFICE1:OFFICE2";
        var routeKey2 = "202510280900:202510281100:OFFICE3:OFFICE4";
        var cancellationToken = CancellationToken.None;

        // Setup metadata with two routes
        var metadata = new Dictionary<string, RouteMetadata>
        {
            { routeKey1, new RouteMetadata { RouteKey = routeKey1, AggregatedAt = DateTime.UtcNow } },
            { routeKey2, new RouteMetadata { RouteKey = routeKey2, AggregatedAt = DateTime.UtcNow } }
        };

        _redisMock.Setup(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(It.IsAny<string>()))
            .ReturnsAsync(metadata);

        var mockLock1 = new Mock<IRedisLock>();

        // Setup lock acquisition - first succeeds, second fails
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey1)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock1.Object);

        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey2)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync((IRedisLock?)null); // Lock fails for second route

        // Setup batch operations for only the successfully locked route
        var existingSummary = new RouteAggregationSummary
        {
            RouteKey = routeKey1,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            OrderDetails = new Dictionary<string, OrderOnRoute>(),
            CreatedAt = DateTime.UtcNow
        };

        var cacheKey1 = $"planning:route-aggregation:{routeKey1}";
        var batchResults = new Dictionary<string, RouteAggregationSummary?>
        {
            { cacheKey1, existingSummary }
        };

        _redisMock.Setup(r => r.Batch.Cache.GetManyAsync<RouteAggregationSummary>(It.IsAny<string[]>()))
            .ReturnsAsync(batchResults);

        _redisMock.Setup(r => r.Batch.Cache.SetManyAsync(
                It.IsAny<Dictionary<string, RouteAggregationSummary>>(),
                It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        // Act - should not throw exception
        await _service.PublicRemoveOrderOnRouteAsync(
            adjustRoutes,
            isUsingAdjustRoutes: true,
            planRoutes,
            cancellationToken);

        // Assert - first lock should be released, processing should continue
        mockLock1.Verify(l => l.DisposeAsync(), Times.Once);
    }

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldRecalculateTotals_AfterRemoval()
    {
        // Arrange
        var mailerId = "MAILER001";
        var childMailerId1 = "CHILD001";
        var childMailerId2 = "CHILD002";
        var adjustRoutes = CreateTestRoutes();
        var planRoutes = new List<MailerPlanRoute>
        {
            CreateTestRoute(mailerId, childMailerId1, "OFFICE1", "OFFICE2")
        };
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var cancellationToken = CancellationToken.None;

        // Setup metadata
        var metadata = new Dictionary<string, RouteMetadata>
        {
            { routeKey, new RouteMetadata { RouteKey = routeKey, AggregatedAt = DateTime.UtcNow } }
        };

        _redisMock.Setup(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(It.IsAny<string>()))
            .ReturnsAsync(metadata);

        // Setup existing aggregation with two items
        var existingOrder = new OrderOnRoute
        {
            MailerId = mailerId,
            Status = "1",
            Weight = 20.0m,
            RealWeight = 24.0m,
            CalWeight = 22.0m,
            TotalItems = 2,
            Items = new List<OrderItemDetail>
            {
                new OrderItemDetail
                {
                    MailerId = mailerId,
                    ChildMailerId = childMailerId1,
                    Weight = 10.0m,
                    RealWeight = 12.0m,
                    CalWeight = 11.0m,
                    Status = "1",
                    IsDeleted = false
                },
                new OrderItemDetail
                {
                    MailerId = mailerId,
                    ChildMailerId = childMailerId2,
                    Weight = 10.0m,
                    RealWeight = 12.0m,
                    CalWeight = 11.0m,
                    Status = "1",
                    IsDeleted = false
                }
            }
        };

        var existingSummary = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 1,
            TotalItems = 2,
            TotalWeight = 20.0m,
            TotalRealWeight = 24.0m,
            TotalCalWeight = 22.0m,
            TotalDiffWeight = 2.0m,
            OrderDetails = new Dictionary<string, OrderOnRoute>
            {
                { mailerId, existingOrder }
            },
            CreatedAt = DateTime.UtcNow
        };

        var mockLock = new Mock<IRedisLock>();
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        var cacheKey = $"planning:route-aggregation:{routeKey}";
        var batchResults = new Dictionary<string, RouteAggregationSummary?>
        {
            { cacheKey, existingSummary }
        };

        _redisMock.Setup(r => r.Batch.Cache.GetManyAsync<RouteAggregationSummary>(It.IsAny<string[]>()))
            .ReturnsAsync(batchResults);

        Dictionary<string, RouteAggregationSummary>? capturedSummaries = null;
        _redisMock.Setup(r => r.Batch.Cache.SetManyAsync(
                It.IsAny<Dictionary<string, RouteAggregationSummary>>(),
                It.IsAny<TimeSpan>()))
            .Callback<Dictionary<string, RouteAggregationSummary>, TimeSpan?>((summaries, expiry) =>
            {
                capturedSummaries = summaries;
            })
            .Returns(Task.CompletedTask);

        // Act - remove one child mailer
        await _service.PublicRemoveOrderOnRouteAsync(
            adjustRoutes,
            isUsingAdjustRoutes: true,
            planRoutes,
            cancellationToken);

        // Assert - totals should be recalculated
        capturedSummaries.Should().NotBeNull();
        var updatedSummary = capturedSummaries![cacheKey];

        // One item removed, one remaining
        updatedSummary.TotalOrders.Should().Be(1); // Order still exists with remaining items
        updatedSummary.TotalItems.Should().Be(1); // One item left
        updatedSummary.TotalWeight.Should().Be(10.0m);
        updatedSummary.TotalRealWeight.Should().Be(12.0m);
        updatedSummary.TotalCalWeight.Should().Be(11.0m);
        updatedSummary.TotalDiffWeight.Should().Be(1.0m);

        // Check item deletion status
        var item1 = updatedSummary.OrderDetails[mailerId].Items.First(i => i.ChildMailerId == childMailerId1);
        var item2 = updatedSummary.OrderDetails[mailerId].Items.First(i => i.ChildMailerId == childMailerId2);

        item1.IsDeleted.Should().BeTrue(); // Removed item
        item2.IsDeleted.Should().BeFalse(); // Remaining item
    }

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldNotModify_WhenOrderNotFound()
    {
        // Arrange
        var mailerId = "NONEXISTENT";
        var childMailerId = "CHILD001";
        var adjustRoutes = CreateTestRoutes();
        var planRoutes = CreateTestRoutesWithMailer(mailerId, childMailerId);
        var routeKey = "202510280800:202510281000:OFFICE1:OFFICE2";
        var cancellationToken = CancellationToken.None;

        // Setup metadata
        var metadata = new Dictionary<string, RouteMetadata>
        {
            { routeKey, new RouteMetadata { RouteKey = routeKey, AggregatedAt = DateTime.UtcNow } }
        };

        _redisMock.Setup(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(It.IsAny<string>()))
            .ReturnsAsync(metadata);

        // Setup existing aggregation WITHOUT the order
        var existingSummary = new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 0,
            TotalItems = 0,
            TotalWeight = 0,
            OrderDetails = new Dictionary<string, OrderOnRoute>(), // Empty
            CreatedAt = DateTime.UtcNow
        };

        var mockLock = new Mock<IRedisLock>();
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.Is<string>(s => s.Contains(routeKey)),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        var cacheKey = $"planning:route-aggregation:{routeKey}";
        var batchResults = new Dictionary<string, RouteAggregationSummary?>
        {
            { cacheKey, existingSummary }
        };

        _redisMock.Setup(r => r.Batch.Cache.GetManyAsync<RouteAggregationSummary>(It.IsAny<string[]>()))
            .ReturnsAsync(batchResults);

        _redisMock.Setup(r => r.Batch.Cache.SetManyAsync(
                It.IsAny<Dictionary<string, RouteAggregationSummary>>(),
                It.IsAny<TimeSpan>()))
            .Returns(Task.CompletedTask);

        // Act
        await _service.PublicRemoveOrderOnRouteAsync(
            adjustRoutes,
            isUsingAdjustRoutes: true,
            planRoutes,
            cancellationToken);

        // Assert - SetManyAsync should not be called when no modifications made
        _redisMock.Verify(r => r.Batch.Cache.SetManyAsync(
            It.IsAny<Dictionary<string, RouteAggregationSummary>>(),
            It.IsAny<TimeSpan>()), Times.Never);
    }

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldHandleMultipleRoutesWithSameFromTo_WithoutCrashing()
    {
        // Arrange - This tests the fix for Issue #1: Dictionary duplicate key crash
        var planRoutes = new List<MailerPlanRoute>
        {
            CreateTestRoute("MAILER001", "CHILD001", "OFFICE1", "OFFICE2"),
            CreateTestRoute("MAILER001", "CHILD002", "OFFICE1", "OFFICE2"), // Same From:To
            CreateTestRoute("MAILER002", "CHILD003", "OFFICE1", "OFFICE2"), // Same From:To
        };
        var cancellationToken = CancellationToken.None;

        // Setup route metadata
        var routeMetadata = new List<RouteMetadata>
        {
            new RouteMetadata
            {
                RouteKey = "202510280800:202510281000:OFFICE1:OFFICE2",
                AggregatedAt = DateTime.UtcNow
            }
        };

        _redisMock.Setup(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(
                It.IsAny<string>()))
            .ReturnsAsync(routeMetadata.ToDictionary(m => m.RouteKey, m => m));

        // Setup existing aggregation with all items
        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = "202510280800:202510281000:OFFICE1:OFFICE2",
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 2,
            TotalItems = 3,
            TotalWeight = 30.0m,
            TotalRealWeight = 33.0m,
            TotalCalWeight = 31.5m,
            TotalDiffWeight = 1.5m,
            OrderDetails = new Dictionary<string, OrderOnRoute>
            {
                {
                    "MAILER001", new OrderOnRoute
                    {
                        MailerId = "MAILER001",
                        TotalItems = 2,
                        Weight = 20.0m,
                        RealWeight = 22.0m,
                        CalWeight = 21.0m,
                        Items = new List<OrderItemDetail>
                        {
                            new OrderItemDetail { MailerId = "MAILER001", ChildMailerId = "CHILD001", Weight = 10.0m, RealWeight = 11.0m, CalWeight = 10.5m, IsDeleted = false },
                            new OrderItemDetail { MailerId = "MAILER001", ChildMailerId = "CHILD002", Weight = 10.0m, RealWeight = 11.0m, CalWeight = 10.5m, IsDeleted = false }
                        }
                    }
                },
                {
                    "MAILER002", new OrderOnRoute
                    {
                        MailerId = "MAILER002",
                        TotalItems = 1,
                        Weight = 10.0m,
                        RealWeight = 11.0m,
                        CalWeight = 10.5m,
                        Items = new List<OrderItemDetail>
                        {
                            new OrderItemDetail { MailerId = "MAILER002", ChildMailerId = "CHILD003", Weight = 10.0m, RealWeight = 11.0m, CalWeight = 10.5m, IsDeleted = false }
                        }
                    }
                }
            },
            CreatedAt = DateTime.UtcNow
        };

        var mockLock = new Mock<IRedisLock>();
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.IsAny<string>(),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Batch.Cache.GetManyAsync<RouteAggregationSummary>(
                It.IsAny<string[]>()))
            .ReturnsAsync(new Dictionary<string, RouteAggregationSummary>
            {
                { "planning:route-aggregation:202510280800:202510281000:OFFICE1:OFFICE2", existingAggregation }
            });

        RouteAggregationSummary? capturedSummary = null;
        _redisMock.Setup(r => r.Batch.Cache.SetManyAsync(
                It.IsAny<Dictionary<string, RouteAggregationSummary>>(),
                It.IsAny<TimeSpan>()))
            .Callback<Dictionary<string, RouteAggregationSummary>, TimeSpan?>((summaries, expiry) =>
            {
                capturedSummary = summaries.Values.First();
            })
            .Returns(Task.CompletedTask);

        // Act - Should NOT crash with "duplicate key" exception
        await _service.PublicRemoveOrderOnRouteAsync(
            new List<MailerPlanRoute>(),
            isUsingAdjustRoutes: true,
            planRoutes,
            cancellationToken);

        // Assert - All 3 items should be removed
        capturedSummary.Should().NotBeNull();
        capturedSummary!.TotalOrders.Should().Be(0, "all orders removed");
        capturedSummary.TotalItems.Should().Be(0, "all items removed");
        capturedSummary.TotalWeight.Should().Be(0, "total weight reset");
        capturedSummary.OrderDetails["MAILER001"].Items.Should().OnlyContain(i => i.IsDeleted == true);
        capturedSummary.OrderDetails["MAILER002"].Items.Should().OnlyContain(i => i.IsDeleted == true);
    }

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldDecrementTotalOrders_WhenLastItemRemoved()
    {
        // Arrange - This tests the fix for Issue #4: TotalOrders logic
        var planRoutes = new List<MailerPlanRoute>
        {
            CreateTestRoute("MAILER001", "CHILD001", "OFFICE1", "OFFICE2")
        };
        var cancellationToken = CancellationToken.None;

        var routeMetadata = new List<RouteMetadata>
        {
            new RouteMetadata
            {
                RouteKey = "202510280800:202510281000:OFFICE1:OFFICE2",
                AggregatedAt = DateTime.UtcNow
            }
        };

        _redisMock.Setup(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(
                It.IsAny<string>()))
            .ReturnsAsync(routeMetadata.ToDictionary(m => m.RouteKey, m => m));

        // Order with ONE item (last item)
        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = "202510280800:202510281000:OFFICE1:OFFICE2",
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 1,
            TotalItems = 1,
            TotalWeight = 10.0m,
            TotalRealWeight = 11.0m,
            TotalCalWeight = 10.5m,
            TotalDiffWeight = 0.5m,
            OrderDetails = new Dictionary<string, OrderOnRoute>
            {
                {
                    "MAILER001", new OrderOnRoute
                    {
                        MailerId = "MAILER001",
                        TotalItems = 1,
                        Weight = 10.0m,
                        RealWeight = 11.0m,
                        CalWeight = 10.5m,
                        Items = new List<OrderItemDetail>
                        {
                            new OrderItemDetail
                            {
                                MailerId = "MAILER001",
                                ChildMailerId = "CHILD001",
                                Weight = 10.0m,
                                RealWeight = 11.0m,
                                CalWeight = 10.5m,
                                IsDeleted = false
                            }
                        }
                    }
                }
            },
            CreatedAt = DateTime.UtcNow
        };

        var mockLock = new Mock<IRedisLock>();
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.IsAny<string>(),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Batch.Cache.GetManyAsync<RouteAggregationSummary>(
                It.IsAny<string[]>()))
            .ReturnsAsync(new Dictionary<string, RouteAggregationSummary>
            {
                { "planning:route-aggregation:202510280800:202510281000:OFFICE1:OFFICE2", existingAggregation }
            });

        RouteAggregationSummary? capturedSummary = null;
        _redisMock.Setup(r => r.Batch.Cache.SetManyAsync(
                It.IsAny<Dictionary<string, RouteAggregationSummary>>(),
                It.IsAny<TimeSpan>()))
            .Callback<Dictionary<string, RouteAggregationSummary>, TimeSpan?>((summaries, expiry) =>
            {
                capturedSummary = summaries.Values.First();
            })
            .Returns(Task.CompletedTask);

        // Act
        await _service.PublicRemoveOrderOnRouteAsync(
            new List<MailerPlanRoute>(),
            isUsingAdjustRoutes: true,
            planRoutes,
            cancellationToken);

        // Assert - TotalOrders should be decremented when last item removed
        capturedSummary.Should().NotBeNull();
        capturedSummary!.TotalOrders.Should().Be(0, "order count decremented when last item removed");
        capturedSummary.TotalItems.Should().Be(0);
        capturedSummary.TotalWeight.Should().Be(0);
        capturedSummary.OrderDetails["MAILER001"].Items[0].IsDeleted.Should().BeTrue();
    }

    [Fact]
    public async Task RemoveOrderOnRouteAsync_ShouldNotDecrementTotalOrders_WhenOtherItemsRemain()
    {
        // Arrange - Test that TotalOrders stays same when other items exist
        var planRoutes = new List<MailerPlanRoute>
        {
            CreateTestRoute("MAILER001", "CHILD001", "OFFICE1", "OFFICE2") // Remove CHILD001, but CHILD002 remains
        };
        var cancellationToken = CancellationToken.None;

        var routeMetadata = new List<RouteMetadata>
        {
            new RouteMetadata
            {
                RouteKey = "202510280800:202510281000:OFFICE1:OFFICE2",
                AggregatedAt = DateTime.UtcNow
            }
        };

        _redisMock.Setup(r => r.Cache.GetAsync<Dictionary<string, RouteMetadata>>(
                It.IsAny<string>()))
            .ReturnsAsync(routeMetadata.ToDictionary(m => m.RouteKey, m => m));

        // Order with TWO items (removing one should NOT decrement TotalOrders)
        var existingAggregation = new RouteAggregationSummary
        {
            RouteKey = "202510280800:202510281000:OFFICE1:OFFICE2",
            FromOfficeId = "OFFICE1",
            ToOfficeId = "OFFICE2",
            TotalOrders = 1,
            TotalItems = 2,
            TotalWeight = 20.0m,
            TotalRealWeight = 22.0m,
            TotalCalWeight = 21.0m,
            TotalDiffWeight = 1.0m,
            OrderDetails = new Dictionary<string, OrderOnRoute>
            {
                {
                    "MAILER001", new OrderOnRoute
                    {
                        MailerId = "MAILER001",
                        TotalItems = 2,
                        Weight = 20.0m,
                        RealWeight = 22.0m,
                        CalWeight = 21.0m,
                        Items = new List<OrderItemDetail>
                        {
                            new OrderItemDetail
                            {
                                MailerId = "MAILER001",
                                ChildMailerId = "CHILD001",
                                Weight = 10.0m,
                                RealWeight = 11.0m,
                                CalWeight = 10.5m,
                                IsDeleted = false
                            },
                            new OrderItemDetail
                            {
                                MailerId = "MAILER001",
                                ChildMailerId = "CHILD002",
                                Weight = 10.0m,
                                RealWeight = 11.0m,
                                CalWeight = 10.5m,
                                IsDeleted = false
                            }
                        }
                    }
                }
            },
            CreatedAt = DateTime.UtcNow
        };

        var mockLock = new Mock<IRedisLock>();
        _redisMock.Setup(r => r.Locks.AcquireAsync(
                It.IsAny<string>(),
                It.IsAny<TimeSpan>(),
                It.IsAny<TimeSpan>()))
            .ReturnsAsync(mockLock.Object);

        _redisMock.Setup(r => r.Batch.Cache.GetManyAsync<RouteAggregationSummary>(
                It.IsAny<string[]>()))
            .ReturnsAsync(new Dictionary<string, RouteAggregationSummary>
            {
                { "planning:route-aggregation:202510280800:202510281000:OFFICE1:OFFICE2", existingAggregation }
            });

        RouteAggregationSummary? capturedSummary = null;
        _redisMock.Setup(r => r.Batch.Cache.SetManyAsync(
                It.IsAny<Dictionary<string, RouteAggregationSummary>>(),
                It.IsAny<TimeSpan>()))
            .Callback<Dictionary<string, RouteAggregationSummary>, TimeSpan?>((summaries, expiry) =>
            {
                capturedSummary = summaries.Values.First();
            })
            .Returns(Task.CompletedTask);

        // Act
        await _service.PublicRemoveOrderOnRouteAsync(
            new List<MailerPlanRoute>(),
            isUsingAdjustRoutes: true,
            planRoutes,
            cancellationToken);

        // Assert - TotalOrders should NOT be decremented (other item remains)
        capturedSummary.Should().NotBeNull();
        capturedSummary!.TotalOrders.Should().Be(1, "order still exists because CHILD002 remains");
        capturedSummary.TotalItems.Should().Be(1, "only one item removed");
        capturedSummary.TotalWeight.Should().Be(10.0m, "weight of removed item subtracted");
        capturedSummary.OrderDetails["MAILER001"].Items.Should().HaveCount(2);
        capturedSummary.OrderDetails["MAILER001"].Items[0].IsDeleted.Should().BeTrue("CHILD001 removed");
        capturedSummary.OrderDetails["MAILER001"].Items[1].IsDeleted.Should().BeFalse("CHILD002 remains");
    }

    #endregion

    #region Helper Methods

    private List<MailerPlanRoute> CreateTestRoutes()
    {
        return new List<MailerPlanRoute>
        {
            new MailerPlanRoute
            {
                FromPostOfficeId = "OFFICE1",
                ToPostOfficeId = "OFFICE2",
                FromTime = new DateTime(2025, 10, 28, 8, 0, 0),
                ToTime = new DateTime(2025, 10, 28, 10, 0, 0),
                MailerId = "MAILER001",
                FromTimeDelay = 0,
                ToTimeDelay = 120,
                AddDays = 0
            }
        };
    }

    private List<MailerPlanRoute> CreateTestRoutesWithMailer(string mailerId, string childMailerId)
    {
        return new List<MailerPlanRoute>
        {
            new MailerPlanRoute
            {
                FromPostOfficeId = "OFFICE1",
                ToPostOfficeId = "OFFICE2",
                FromTime = new DateTime(2025, 10, 28, 8, 0, 0),
                ToTime = new DateTime(2025, 10, 28, 10, 0, 0),
                MailerId = mailerId,
                ChildMailerId = childMailerId,
                FromTimeDelay = 0,
                ToTimeDelay = 120,
                AddDays = 0
            }
        };
    }

    private MailerPlanRoute CreateTestRoute(string mailerId, string childMailerId, string fromOffice, string toOffice)
    {
        return new MailerPlanRoute
        {
            FromPostOfficeId = fromOffice,
            ToPostOfficeId = toOffice,
            FromTime = new DateTime(2025, 10, 28, 8, 0, 0),
            ToTime = new DateTime(2025, 10, 28, 10, 0, 0),
            MailerId = mailerId,
            ChildMailerId = childMailerId,
            FromTimeDelay = 0,
            ToTimeDelay = 120,
            AddDays = 0
        };
    }

    #endregion
}
