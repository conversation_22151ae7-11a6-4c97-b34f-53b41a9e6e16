﻿using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Contracts.PlanningTemplate;

namespace TMS.PlanningService.Application.Services.Inferfaces;

public interface IExternalDataService
{
    Task EnrichWithEmployeeDataAsync(List<PlanningTemplateDto> planningTemplateDtos);
    Task EnrichWithRouteDataAsync(List<PlanningTemplateDto> planningTemplateDtos);

    /**
     * EnrichWithEmployeeDataAsync(planningTemplateDtos); => chỉ enrich CreatedBy
     * EnrichWithEmployeeDataAsync(planningTemplateDtos, true) => enrich cả CreatedBy + UpdatedBy
     * **/
    Task GenericEnrichWithEmployeeDataAsync<T>(List<T> items, bool? getUserUpdate = false);
    Task EnrichWithDataDailyPlanAsync(List<DailyPlanDto> dtos);
    Task EnrichWithDailyPlanDetailAsync(DailyPlanDetailDto dto);
    Task EnrichWithPlanningTemplateByRouteAsync(List<PlanningTemplateByRouteIdDto> dtos);
}
