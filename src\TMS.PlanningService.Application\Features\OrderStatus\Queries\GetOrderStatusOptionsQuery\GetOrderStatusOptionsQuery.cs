﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MediatR;
using TMS.PlanningService.Contracts.Orders;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Features.OrderStatus.Queries.GetOrderStatusOptionsQuery;
 

public record GetOrderStatusOptionsQuery() : IRequest<PagedResult<OrderStatusOptionsResponse?>>;
