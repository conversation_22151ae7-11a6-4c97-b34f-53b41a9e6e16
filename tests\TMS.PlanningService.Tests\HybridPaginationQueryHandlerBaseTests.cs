using FluentAssertions;
using MediatR;
using Microsoft.Extensions.Logging;
using Moq;
using TMS.PlanningService.Application.Common.Queries;
using TMS.SharedKernel.Domain;
using Xunit;

namespace TMS.PlanningService.Tests;

#region Test Models

/// <summary>
/// Test query for unit testing
/// </summary>
public class TestQuery : IRequest<PagedResult<TestDto>>
{
    public int Page { get; set; }
    public int PageSize { get; set; }
    public string? Filter { get; set; }
}

/// <summary>
/// Test DTO for unit testing
/// </summary>
public class TestDto
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
    public string DataSource { get; set; } = string.Empty;
}

/// <summary>
/// Test summary (in-memory/cache model) for unit testing
/// </summary>
public class TestSummary
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
}

/// <summary>
/// Test entity (database model) for unit testing
/// </summary>
public class TestEntity
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Category { get; set; } = string.Empty;
}

#endregion

#region Testable Handler Implementation

/// <summary>
/// Concrete implementation of HybridPaginationQueryHandlerBase for unit testing
/// Exposes protected methods and provides controllable behavior
/// </summary>
public class TestableHybridPaginationQueryHandler
    : HybridPaginationQueryHandlerBase<TestQuery, TestDto, TestSummary, TestEntity>
{
    private readonly ILogger<TestableHybridPaginationQueryHandler> _logger;

    // Control flags for testing different scenarios
    public List<TestSummary> RedisDataToReturn { get; set; } = new();
    public List<TestSummary> DatabaseDataToReturn { get; set; } = new();
    public bool ShouldSetEstimatedTotalCount { get; set; } = false;
    public int? EstimatedTotalCountValue { get; set; }
    public bool ShouldSetIsPaginationAlreadyDone { get; set; } = false;

    protected override ILogger Logger => _logger;

    public TestableHybridPaginationQueryHandler(ILogger<TestableHybridPaginationQueryHandler> logger)
    {
        _logger = logger;
    }

    // Expose protected method for direct testing
    public Task<PagedResult<TestDto>> PublicExecuteHybridPaginationAsync(
        TestQuery request,
        int pageNumber,
        int pageSize,
        CancellationToken cancellationToken)
    {
        return ExecuteHybridPaginationAsync(request, pageNumber, pageSize, cancellationToken);
    }

    protected override Task<List<TestSummary>> FetchFromRedisAsync(
        TestQuery request,
        CancellationToken cancellationToken)
    {
        // Set flags if configured
        if (ShouldSetEstimatedTotalCount)
        {
            EstimatedTotalCount = EstimatedTotalCountValue;
        }

        if (ShouldSetIsPaginationAlreadyDone)
        {
            IsPaginationAlreadyDone = true;
        }

        return Task.FromResult(RedisDataToReturn);
    }

    protected override Task<List<TestSummary>> FetchFromDatabaseAsync(
        TestQuery request,
        CancellationToken cancellationToken)
    {
        return Task.FromResult(DatabaseDataToReturn);
    }

    protected override List<TestSummary> ApplyFilters(
        List<TestSummary> data,
        TestQuery request)
    {
        if (string.IsNullOrEmpty(request.Filter))
        {
            return data;
        }

        // Simple filtering by category
        return data.Where(d => d.Category == request.Filter).ToList();
    }

    protected override List<TestSummary> ApplySorting(
        List<TestSummary> data,
        TestQuery request)
    {
        // Sort by Id (ascending)
        return data.OrderBy(d => d.Id).ToList();
    }

    protected override Task<TestDto> MapToDtoAsync(
        TestSummary summary,
        string dataSource,
        CancellationToken cancellationToken)
    {
        var dto = new TestDto
        {
            Id = summary.Id,
            Name = summary.Name,
            Category = summary.Category,
            DataSource = dataSource
        };

        return Task.FromResult(dto);
    }

    protected override string GetUniqueKey(TestSummary summary)
    {
        return summary.Id;
    }
}

#endregion

/// <summary>
/// Unit tests for HybridPaginationQueryHandlerBase
/// Tests cover pagination, filtering, hybrid mode, and edge cases
/// </summary>
public class HybridPaginationQueryHandlerBaseTests
{
    private readonly Mock<ILogger<TestableHybridPaginationQueryHandler>> _loggerMock;
    private readonly TestableHybridPaginationQueryHandler _handler;

    public HybridPaginationQueryHandlerBaseTests()
    {
        _loggerMock = new Mock<ILogger<TestableHybridPaginationQueryHandler>>();
        _handler = new TestableHybridPaginationQueryHandler(_loggerMock.Object);
    }

    #region Helper Methods

    private List<TestSummary> CreateTestData(int count, string category = "A")
    {
        var data = new List<TestSummary>();
        for (int i = 1; i <= count; i++)
        {
            data.Add(new TestSummary
            {
                Id = i.ToString("D3"), // e.g., "001", "002", etc.
                Name = $"Item {i}",
                Category = category
            });
        }
        return data;
    }

    #endregion

    #region Metadata-First Optimization Tests

    [Fact]
    public async Task MetadataFirst_WithFullPage_ShouldReturnCorrectResults()
    {
        // Arrange: Simulate metadata-first optimization with full page
        var pageSize = 10;
        var totalCount = 50;

        _handler.RedisDataToReturn = CreateTestData(pageSize); // Only current page data
        _handler.ShouldSetEstimatedTotalCount = true;
        _handler.EstimatedTotalCountValue = totalCount;
        _handler.ShouldSetIsPaginationAlreadyDone = true;

        var request = new TestQuery { Page = 1, PageSize = pageSize };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(pageSize);
        result.TotalCount.Should().Be(totalCount);
        result.PageNumber.Should().Be(1);
        result.PageSize.Should().Be(pageSize);
        result.Items.All(x => x.DataSource == "Redis").Should().BeTrue();
    }

    [Fact]
    public async Task MetadataFirst_WithPartialPage_FilteredResults_ShouldNotFallbackToHybrid()
    {
        // Arrange: This tests the FIX for the bug where filtered results < pageSize
        // incorrectly triggered hybrid mode
        var pageSize = 10;
        var filteredCount = 3; // Only 3 results after filtering

        _handler.RedisDataToReturn = CreateTestData(filteredCount);
        _handler.ShouldSetEstimatedTotalCount = true;
        _handler.EstimatedTotalCountValue = filteredCount; // Accurate count from metadata
        _handler.ShouldSetIsPaginationAlreadyDone = true;

        var request = new TestQuery { Page = 1, PageSize = pageSize };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert - Should use Redis data, NOT fall back to hybrid mode
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(filteredCount);
        result.TotalCount.Should().Be(filteredCount);
        result.Items.All(x => x.DataSource == "Redis").Should().BeTrue();
    }

    [Fact]
    public async Task MetadataFirst_Page2_ShouldReturnDifferentResults()
    {
        // Arrange: Page 2 with metadata-first optimization
        var pageSize = 10;
        var totalCount = 50;

        // Simulate fetching different data for page 2
        _handler.RedisDataToReturn = CreateTestData(pageSize)
            .Select(x => new TestSummary
            {
                Id = (int.Parse(x.Id) + 10).ToString("D3"), // Items 11-20
                Name = x.Name,
                Category = x.Category
            }).ToList();

        _handler.ShouldSetEstimatedTotalCount = true;
        _handler.EstimatedTotalCountValue = totalCount;
        _handler.ShouldSetIsPaginationAlreadyDone = true;

        var request = new TestQuery { Page = 2, PageSize = pageSize };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(pageSize);
        result.Items.First().Id.Should().Be("011"); // First item of page 2
        result.TotalCount.Should().Be(totalCount);
    }

    #endregion

    #region Standard Pagination Tests (with .Skip() fix)

    [Fact]
    public async Task StandardPagination_Page1_ShouldReturnFirst10Items()
    {
        // Arrange: Redis has full dataset, no metadata-first optimization
        var totalItems = 50;
        _handler.RedisDataToReturn = CreateTestData(totalItems);

        var request = new TestQuery { Page = 1, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        result.Items.First().Id.Should().Be("001");
        result.Items.Last().Id.Should().Be("010");
        result.TotalCount.Should().Be(totalItems);
    }

    [Fact]
    public async Task StandardPagination_Page2_ShouldSkipFirst10AndReturnNext10()
    {
        // Arrange: Tests the .Skip() fix - page 2 should NOT return same items as page 1
        var totalItems = 50;
        _handler.RedisDataToReturn = CreateTestData(totalItems);

        var request = new TestQuery { Page = 2, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert - This test would FAIL before the .Skip() fix
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        result.Items.First().Id.Should().Be("011"); // Not "001"!
        result.Items.Last().Id.Should().Be("020");
    }

    [Fact]
    public async Task StandardPagination_Page3_ShouldSkipFirst20AndReturnNext10()
    {
        // Arrange
        var totalItems = 50;
        _handler.RedisDataToReturn = CreateTestData(totalItems);

        var request = new TestQuery { Page = 3, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        result.Items.First().Id.Should().Be("021");
        result.Items.Last().Id.Should().Be("030");
    }

    [Fact]
    public async Task StandardPagination_LastPagePartial_ShouldReturnRemainingItems()
    {
        // Arrange: 25 items, page size 10, page 3 should have 5 items
        var totalItems = 25;
        _handler.RedisDataToReturn = CreateTestData(totalItems);

        var request = new TestQuery { Page = 3, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(5); // Only 5 remaining
        result.Items.First().Id.Should().Be("021");
        result.Items.Last().Id.Should().Be("025");
        result.TotalCount.Should().Be(totalItems);
    }

    #endregion

    #region Filtering Tests

    [Fact]
    public async Task Filtering_WithMatchingResults_ShouldReturnFilteredData()
    {
        // Arrange: 30 items total, 10 in category "B"
        var categoryAData = CreateTestData(20, "A");
        var categoryBData = CreateTestData(10, "B");
        _handler.RedisDataToReturn = categoryAData.Concat(categoryBData).ToList();

        var request = new TestQuery { Page = 1, PageSize = 10, Filter = "B" };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        result.Items.All(x => x.Category == "B").Should().BeTrue();
        result.TotalCount.Should().Be(10);
    }

    [Fact]
    public async Task Filtering_WithFewerResultsThanPageSize_ShouldReturnAllMatches()
    {
        // Arrange: Tests total count calculation with filtered results
        var categoryAData = CreateTestData(20, "A");
        var categoryBData = CreateTestData(3, "B"); // Only 3 matching
        _handler.RedisDataToReturn = categoryAData.Concat(categoryBData).ToList();

        var request = new TestQuery { Page = 1, PageSize = 10, Filter = "B" };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(3);
        result.TotalCount.Should().Be(3); // Correct total count, not inflated
    }

    [Fact]
    public async Task Filtering_Page2_WithEnoughResults_ShouldSkipCorrectly()
    {
        // Arrange: 25 items in category "B", requesting page 2
        var categoryBData = CreateTestData(25, "B");
        _handler.RedisDataToReturn = categoryBData;

        var request = new TestQuery { Page = 2, PageSize = 10, Filter = "B" };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        result.Items.First().Id.Should().Be("011");
        result.Items.Last().Id.Should().Be("020");
    }

    #endregion

    #region Hybrid Mode Tests

    [Fact]
    public async Task HybridMode_RedisInsufficient_ShouldMergeRedisAndDatabase()
    {
        // Arrange: Redis has only 5 items (< pageSize), should trigger hybrid mode
        _handler.RedisDataToReturn = CreateTestData(5);
        _handler.DatabaseDataToReturn = CreateTestData(20)
            .Select(x => new TestSummary
            {
                Id = (int.Parse(x.Id) + 5).ToString("D3"), // Items 6-25
                Name = x.Name,
                Category = x.Category
            }).ToList();

        var request = new TestQuery { Page = 1, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        result.Items.First().Id.Should().Be("001");
        result.Items.Last().Id.Should().Be("010");
        result.Items.Any(x => x.DataSource.Contains("Hybrid")).Should().BeTrue();
    }

    [Fact]
    public async Task HybridMode_WithDuplicates_ShouldDeduplicate()
    {
        // Arrange: Redis and DB have overlapping data
        _handler.RedisDataToReturn = CreateTestData(5); // Items 1-5
        _handler.DatabaseDataToReturn = CreateTestData(10); // Items 1-10 (duplicates 1-5)

        var request = new TestQuery { Page = 1, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert - Should have 10 unique items, not 15
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        result.TotalCount.Should().Be(10);
        result.Items.Select(x => x.Id).Should().OnlyHaveUniqueItems();
    }

    [Fact]
    public async Task HybridMode_RedisPreferred_DuplicatesShouldUseRedisData()
    {
        // Arrange: Same IDs in both Redis and DB
        var redisData = new List<TestSummary>
        {
            new() { Id = "001", Name = "Redis Item", Category = "A" }
        };
        var dbData = new List<TestSummary>
        {
            new() { Id = "001", Name = "DB Item", Category = "A" },
            new() { Id = "002", Name = "DB Item 2", Category = "A" }
        };

        _handler.RedisDataToReturn = redisData;
        _handler.DatabaseDataToReturn = dbData;

        var request = new TestQuery { Page = 1, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert - Item 001 should use Redis name
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(2);
        result.Items.First(x => x.Id == "001").Name.Should().Be("Redis Item");
    }

    #endregion

    #region Edge Cases

    [Fact]
    public async Task EdgeCase_EmptyRedis_ShouldFallbackToDatabase()
    {
        // Arrange
        _handler.RedisDataToReturn = new List<TestSummary>();
        _handler.DatabaseDataToReturn = CreateTestData(10);

        var request = new TestQuery { Page = 1, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(10);
        result.Items.All(x => x.DataSource == "DB").Should().BeTrue();
    }

    [Fact]
    public async Task EdgeCase_NoDataAnywhere_ShouldReturnEmptyResult()
    {
        // Arrange
        _handler.RedisDataToReturn = new List<TestSummary>();
        _handler.DatabaseDataToReturn = new List<TestSummary>();

        var request = new TestQuery { Page = 1, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().BeEmpty();
        result.TotalCount.Should().Be(0);
        result.PageNumber.Should().Be(1);
    }

    [Fact]
    public async Task EdgeCase_PageBeyondAvailableData_ShouldReturnEmptyResult()
    {
        // Arrange
        _handler.RedisDataToReturn = CreateTestData(10);

        var request = new TestQuery { Page = 5, PageSize = 10 }; // Way beyond available data

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().BeEmpty();
        result.TotalCount.Should().Be(10); // Still reports total available
    }

    [Fact]
    public async Task EdgeCase_PageSize1_ShouldReturnSingleItem()
    {
        // Arrange
        _handler.RedisDataToReturn = CreateTestData(10);

        var request = new TestQuery { Page = 2, PageSize = 1 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().HaveCount(1);
        result.Items.First().Id.Should().Be("002");
    }

    [Fact]
    public async Task EdgeCase_NoFilter_EmptyResults_ShouldReturnEmpty()
    {
        // Arrange: Data exists but filter matches nothing
        _handler.RedisDataToReturn = CreateTestData(10, "A");

        var request = new TestQuery { Page = 1, PageSize = 10, Filter = "Z" }; // No matches

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Items.Should().BeEmpty();
        result.TotalCount.Should().Be(0);
    }

    #endregion

    #region Total Count Tests

    [Fact]
    public async Task TotalCount_WithEstimatedCount_ShouldUseEstimatedValue()
    {
        // Arrange: Metadata-first provides accurate total
        _handler.RedisDataToReturn = CreateTestData(10);
        _handler.ShouldSetEstimatedTotalCount = true;
        _handler.EstimatedTotalCountValue = 1000; // From metadata
        _handler.ShouldSetIsPaginationAlreadyDone = true;

        var request = new TestQuery { Page = 1, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.TotalCount.Should().Be(1000); // Uses estimated count
    }

    [Fact]
    public async Task TotalCount_WithoutEstimatedCount_ShouldUseFilteredDataCount()
    {
        // Arrange: Standard mode without metadata-first
        _handler.RedisDataToReturn = CreateTestData(50);

        var request = new TestQuery { Page = 1, PageSize = 10 };

        // Act
        var result = await _handler.PublicExecuteHybridPaginationAsync(
            request,
            request.Page,
            request.PageSize,
            CancellationToken.None);

        // Assert
        result.TotalCount.Should().Be(50); // Uses actual filtered count
    }

    #endregion
}
