﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernal.Kafka.Abstractions;

namespace TMS.PlanningService.Application.Services.Implements;

/// <summary>
/// Kafka-based implementation of IPlanningQueueService for distributed, multi-instance deployments.
/// This service produces messages to Kafka topics and relies on Kafka consumers to process them.
/// </summary>
public class KafkaPlanningQueueService : IPlanningQueueService
{
    private readonly ILogger<KafkaPlanningQueueService> _logger;
    private readonly IMessageProducer _messageProducer;

    public KafkaPlanningQueueService(
        ILogger<KafkaPlanningQueueService> logger,
        [FromKeyedServices(Constants.KafkaFlow)] IMessageProducer messageProducer)
    {
        _logger = logger;
        _messageProducer = messageProducer;
    }

    public Guid EnqueuePlanning(PlanningWebhookRequest planningRequest, int priority = 0)
    {
        var message = new KafkaPlanningWebhookMessage
        {
            Id = Guid.NewGuid().ToString(),
            PlanningRequest = planningRequest,
            Priority = priority,
            Timestamp = DateTime.UtcNow
        };

        try
        {
            // Produce message to Kafka (fire and forget for performance)
            // Use MailerId and ChildMailerId as the message key for partitioning
            var messageKey = $"{planningRequest.MailerRouteMaster.MailerId}_{planningRequest.MailerRouteMaster.ChildMailerId}";
            _ = _messageProducer.ProduceAsync(message, messageKey);

            _logger.LogInformation("Planning published to Kafka - Message ID: {MessageId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}, Topic: {Topic}",
                message.Id, planningRequest.MailerRouteMaster.MailerId, planningRequest.MailerRouteMaster.ChildMailerId, Constants.KafkaPlanningEvent);

            return Guid.Parse(message.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to publish planning to Kafka - MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                planningRequest.MailerRouteMaster.MailerId, planningRequest.MailerRouteMaster.ChildMailerId);
            throw;
        }
    }

    public List<BatchPlanningResult> EnqueuePlanningBatch(List<PlanningWebhookRequest> planningRequests, int priority = 0)
    {
        var results = new List<BatchPlanningResult>();

        _logger.LogInformation("Starting batch publish of {Count} planning items to Kafka with priority: {Priority}",
            planningRequests.Count, priority);

        foreach (var planningRequest in planningRequests)
        {
            try
            {
                var queueId = EnqueuePlanning(planningRequest, priority);

                results.Add(new BatchPlanningResult
                {
                    QueueId = queueId,
                    MailerID = planningRequest.MailerRouteMaster.MailerId,
                    ChildMailerID = planningRequest.MailerRouteMaster.ChildMailerId,
                    Success = true,
                    ErrorMessage = "Planning published to Kafka successfully"
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error publishing planning to Kafka in batch - MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                    planningRequest?.MailerRouteMaster?.MailerId ?? "Unknown",
                    planningRequest?.MailerRouteMaster?.ChildMailerId ?? "Unknown");

                results.Add(new BatchPlanningResult
                {
                    MailerID = planningRequest?.MailerRouteMaster?.MailerId ?? "Unknown",
                    ChildMailerID = planningRequest?.MailerRouteMaster?.ChildMailerId ?? "Unknown",
                    Success = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        var successCount = results.Count(r => r.Success);
        _logger.LogInformation("Completed batch publish - Total: {Total}, Successful: {Success}, Failed: {Failed}",
            planningRequests.Count, successCount, planningRequests.Count - successCount);

        return results;
    }

    // The following methods are not applicable for Kafka-based queue
    // as Kafka handles consumer management automatically
    public List<QueuedPlanningItem> DequeuePlanning(int batchSize = 100)
    {
        throw new NotSupportedException("DequeuePlanning is not supported in Kafka mode. Kafka consumers automatically pull messages.");
    }

    public void MarkAsProcessed(Guid queueId)
    {
        throw new NotSupportedException("MarkAsProcessed is not supported in Kafka mode. Kafka consumers automatically commit offsets.");
    }

    public void MarkAsFailed(Guid queueId, string errorMessage)
    {
        throw new NotSupportedException("MarkAsFailed is not supported in Kafka mode. Kafka handles retries and dead letter queues automatically.");
    }

    public QueueStatistics GetStatistics()
    {
        _logger.LogWarning("GetStatistics is not fully supported in Kafka mode. Returning default statistics.");

        // In Kafka mode, statistics would need to be tracked separately or queried from Kafka metrics
        return new QueueStatistics
        {
            PendingCount = 0,
            ProcessingCount = 0,
            CompletedCount = 0,
            FailedCount = 0,
            TotalProcessedToday = 0,
            LastProcessedAt = DateTime.MinValue,
            AverageProcessingTime = TimeSpan.Zero
        };
    }

    public QueuedPlanningItem? GetPlanningStatus(Guid queueId)
    {
        throw new NotSupportedException("GetPlanningStatus is not supported in Kafka mode. Use distributed tracing or event tracking instead.");
    }
}
