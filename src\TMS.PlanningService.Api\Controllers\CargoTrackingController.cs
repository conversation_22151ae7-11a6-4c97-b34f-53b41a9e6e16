﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.CargoTracking.Queries.GetCargoTrackingByMailerId;
using TMS.PlanningService.Application.Features.CargoTracking.Queries.GetCargoTrackingRoutes;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/cargo-tracking")]
[Produces("application/json")]
public class CargoTrackingController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<CargoTrackingController> _logger;

    public CargoTrackingController(
        IMediator mediator,
        ILogger<CargoTrackingController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    ///  Get cargo tracking match routes by mailer ids
    /// </summary>
    /// <param name="mailerIds">List mailerIds</param>
    /// <returns>List of cargo tracking match routes</returns>
    [ApiExplorerSettings(IgnoreApi = true)] // this one for internal use only
    [HttpPost("match-routes")]
    [ProducesResponseType(typeof(PagedResult<GetMatchRouteResponse>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResult<GetMatchRouteResponse>>> GetMatchRoutesAsync([FromBody] List<string> mailerIds)
    {
        _logger.LogInformation(
            "Get cargo tracking match routes - MailerIds: {MailerIds}",
            string.Join(", ", mailerIds));

        var query = new GetMatchRoutesQuery(mailerIds);
        var result = await _mediator.Send(query);

        return Ok(result);
    }

    /// <summary>
    /// Get cargo tracking by mailer id (MailerId) and child mailer id (ChildMailerId)
    /// Returns all route types for a specific cargo/order:
    /// - Master route (from mailer_route_masters)
    /// - Plan routes (from mailer_plan_routes)
    /// - Adjust routes (from mailer_adjust_routes)
    /// - Actual routes (from mailer_actual_routes)
    /// </summary>
    /// <returns>Complete cargo tracking information with all route types</returns>
    [HttpGet]
    [ProducesResponseType(typeof(Dictionary<string,CargoTrackingResponseDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<Dictionary<string, CargoTrackingResponseDto>>> GetCargoTracking(
    [FromQuery] string mailerId, string? childMailerId)
    {
        _logger.LogInformation(
           "Getting cargo tracking for mailerId: {mailerId} and childMailerId: {childMailerId}",
            mailerId, childMailerId);


        var query = new GetCargoTrackingByMailerIdQuery(mailerId, childMailerId);
        var result = await _mediator.Send(query);

        return Ok(result);
    }
}
