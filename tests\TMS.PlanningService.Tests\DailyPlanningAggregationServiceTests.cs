using System.Collections.Concurrent;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using TMS.PlanningService.Application.Services.Orders;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernal.SmoothRedis;
using Xunit;

namespace TMS.PlanningService.Tests;

public class DailyPlanningAggregationServiceTests
{
    private readonly Mock<ILogger<DailyPlanningAggregationService>> _loggerMock;
    private readonly Mock<ISmoothRedis> _redisMock;
    private readonly Mock<IOrderDataService> _orderDataServiceMock;
    private readonly Mock<IDailyPlanningGeneratorService> _dailyPlanningGeneratorServiceMock;

    public DailyPlanningAggregationServiceTests()
    {
        _loggerMock = new Mock<ILogger<DailyPlanningAggregationService>>();
        _redisMock = new Mock<ISmoothRedis>();
        _orderDataServiceMock = new Mock<IOrderDataService>();
        _dailyPlanningGeneratorServiceMock = new Mock<IDailyPlanningGeneratorService>();
    }

    [Fact]
    public void TimeWindowMetadata_ShouldStoreTemplateInformation()
    {
        // Arrange
        var templateId = Guid.NewGuid();
        var companyId = Guid.NewGuid();
        var executionDate = new DateOnly(2025, 10, 28);
        var fromTime = new DateTime(2025, 10, 28, 8, 0, 0);
        var toTime = new DateTime(2025, 10, 28, 10, 0, 0);

        // Act
        var metadata = new TimeWindowMetadata
        {
            FromTime = fromTime,
            ToTime = toTime,
            TemplateId = templateId,
            CompanyId = companyId,
            ExecutionDate = executionDate
        };

        // Assert
        metadata.TemplateId.Should().Be(templateId);
        metadata.CompanyId.Should().Be(companyId);
        metadata.ExecutionDate.Should().Be(executionDate);
        metadata.FromTime.Should().Be(fromTime);
        metadata.ToTime.Should().Be(toTime);
    }

    [Fact]
    public void RouteValidationResult_ShouldIndicateNextDayRequirement()
    {
        // Arrange
        var nextDayDate = new DateOnly(2025, 10, 29);
        var matchedWindow = new TimeWindowMetadata
        {
            FromTime = new DateTime(2025, 10, 28, 8, 0, 0),
            ToTime = new DateTime(2025, 10, 28, 10, 0, 0),
            TemplateId = Guid.NewGuid(),
            CompanyId = Guid.NewGuid(),
            ExecutionDate = new DateOnly(2025, 10, 28)
        };

        // Act
        var result = new RouteValidationResult
        {
            IsValid = false,
            RequiresNextDayPlan = true,
            MatchedWindow = matchedWindow,
            NextDayExecutionDate = nextDayDate
        };

        // Assert
        result.IsValid.Should().BeFalse();
        result.RequiresNextDayPlan.Should().BeTrue();
        result.MatchedWindow.Should().Be(matchedWindow);
        result.NextDayExecutionDate.Should().Be(nextDayDate);
    }

    [Theory]
    [InlineData("08:00:00", "10:00:00", "08:30:00", "09:30:00", true)]  // Route time within window
    [InlineData("08:00:00", "10:00:00", "07:30:00", "08:30:00", false)] // Route starts before window
    [InlineData("08:00:00", "10:00:00", "09:30:00", "11:00:00", false)] // Route ends after window
    [InlineData("08:00:00", "10:00:00", "08:00:00", "10:00:00", true)]  // Exact match
    public void TimeOfDayComparison_ShouldMatchCorrectly(
        string windowFromTime,
        string windowToTime,
        string routeFromTime,
        string routeToTime,
        bool expectedMatch)
    {
        // Arrange
        var baseDate = new DateTime(2025, 10, 28);
        var windowFrom = TimeSpan.Parse(windowFromTime);
        var windowTo = TimeSpan.Parse(windowToTime);
        var routeFrom = TimeSpan.Parse(routeFromTime);
        var routeTo = TimeSpan.Parse(routeToTime);

        // Act
        var fromTimeMatches = routeFrom >= windowFrom;
        var toTimeMatches = routeTo <= windowTo;
        var actualMatch = fromTimeMatches && toTimeMatches;

        // Assert
        actualMatch.Should().Be(expectedMatch);
    }

    [Fact]
    public void NextDayDetection_ShouldIdentifyDateMismatch()
    {
        // Arrange
        var currentDate = new DateOnly(2025, 10, 28);
        var nextDate = new DateOnly(2025, 10, 29);

        var routeTime = new DateTime(2025, 10, 29, 8, 30, 0); // Next day
        var routeDate = DateOnly.FromDateTime(routeTime.Date);

        // Act
        var isNextDay = routeDate > currentDate;
        var isSameDay = routeDate == currentDate;

        // Assert
        isNextDay.Should().BeTrue();
        isSameDay.Should().BeFalse();
        routeDate.Should().Be(nextDate);
    }

    [Fact]
    public void TimeWindowCache_ShouldGroupByOfficePairs()
    {
        // Arrange
        var timeWindows = new ConcurrentDictionary<string, List<TimeWindowMetadata>>();
        var officePairKey = "OFFICE1:OFFICE2";
        var templateId = Guid.NewGuid();

        var metadata = new TimeWindowMetadata
        {
            FromTime = new DateTime(2025, 10, 28, 8, 0, 0),
            ToTime = new DateTime(2025, 10, 28, 10, 0, 0),
            TemplateId = templateId,
            CompanyId = Guid.NewGuid(),
            ExecutionDate = new DateOnly(2025, 10, 28)
        };

        // Act
        timeWindows[officePairKey] = new List<TimeWindowMetadata> { metadata };

        // Assert
        timeWindows.Should().ContainKey(officePairKey);
        timeWindows[officePairKey].Should().HaveCount(1);
        timeWindows[officePairKey].First().TemplateId.Should().Be(templateId);
    }

    [Fact]
    public void DailyPlanTotals_ShouldCalculateDiffWeight()
    {
        // Arrange
        var planTotals = new DailyPlanTotals
        {
            DailyPlanningId = Guid.NewGuid(),
            TotalWeight = 100m,
            TotalRealWeight = 120m,
            UpdatedAt = DateTime.UtcNow,
            RouteKeys = new List<string> { "route1", "route2" }
        };

        // Act
        var diffWeight = planTotals.TotalDiffWeight;

        // Assert
        diffWeight.Should().Be(20m);
        planTotals.TotalWeight.Should().Be(100m);
        planTotals.TotalRealWeight.Should().Be(120m);
    }

    [Fact]
    public void MailerPlanRoute_TimeOfDayExtraction_ShouldIgnoreDate()
    {
        // Arrange
        var route = new MailerPlanRoute
        {
            FromPostOfficeId = "OFFICE1",
            ToPostOfficeId = "OFFICE2",
            FromTime = new DateTime(2025, 10, 29, 8, 30, 0), // Next day
            ToTime = new DateTime(2025, 10, 29, 10, 30, 0)
        };

        // Act
        var routeFromTimeOfDay = route.FromTime!.Value.TimeOfDay;
        var routeToTimeOfDay = route.ToTime!.Value.TimeOfDay;

        // Assert
        routeFromTimeOfDay.Should().Be(new TimeSpan(8, 30, 0));
        routeToTimeOfDay.Should().Be(new TimeSpan(10, 30, 0));
    }

    [Theory]
    [InlineData(2025, 10, 28, 2025, 10, 29, true)]  // Next day
    [InlineData(2025, 10, 28, 2025, 10, 28, false)] // Same day
    [InlineData(2025, 10, 28, 2025, 10, 30, true)]  // Two days later
    [InlineData(2025, 10, 28, 2025, 10, 27, false)] // Previous day
    public void DateComparison_ShouldDetectNextDay(
        int currentYear, int currentMonth, int currentDay,
        int routeYear, int routeMonth, int routeDay,
        bool expectedIsAfter)
    {
        // Arrange
        var currentDate = new DateOnly(currentYear, currentMonth, currentDay);
        var routeDate = new DateOnly(routeYear, routeMonth, routeDay);

        // Act
        var isAfter = routeDate > currentDate;

        // Assert
        isAfter.Should().Be(expectedIsAfter);
    }
}
