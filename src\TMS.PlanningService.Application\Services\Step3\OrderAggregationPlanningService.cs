﻿using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Orders;
using TMS.SharedKernal.SmoothRedis;

namespace TMS.PlanningService.Application.Services.Step3;

// this not use currently
/// <summary>
/// Service for processing order aggregation events and triggering planning operations
/// Handles batched order summaries from OrderService every 5 minutes
/// </summary>
public class OrderAggregationPlanningService : IOrderAggregationPlanningService
{
    private readonly ILogger<OrderAggregationPlanningService> _logger;
    private readonly ISmoothRedis _redis;
    private readonly IPlanningAggregationService _planningAggregationService;

    public OrderAggregationPlanningService(
        ILogger<OrderAggregationPlanningService> logger,
        ISmoothRedis redis,
        IPlanningAggregationService planningAggregationService)
    {
        _logger = logger;
        _redis = redis;
        _planningAggregationService = planningAggregationService;
    }

    public async Task ProcessOrderAggregationAsync(
        OrderAggregationEvent aggregation,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation(
                "Processing order aggregation - Event ID: {EventId}, Offices: {OfficeCount}, Total Orders: {TotalOrders}",
                aggregation.Id,
                aggregation.OfficeSummaries.Count,
                aggregation.OfficeSummaries.Sum(s => s.TotalOrders));

            // Step 1: Store aggregation data in Redis for quick access
            await CacheAggregationDataAsync(aggregation, cancellationToken);

            // Step 2: Analyze workload by office
            var workloadAnalysis = AnalyzeOfficeWorkload(aggregation.OfficeSummaries);

            // Step 3: Trigger planning operations based on workload
            await TriggerPlanningOperationsAsync(workloadAnalysis, cancellationToken);

            _logger.LogInformation(
                "Successfully processed order aggregation - Event ID: {EventId}",
                aggregation.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to process order aggregation - Event ID: {EventId}",
                aggregation.Id);
            throw;
        }
    }

    private async Task CacheAggregationDataAsync(
        OrderAggregationEvent aggregation,
        CancellationToken cancellationToken)
    {
        try
        {
            // Store all office summaries in Redis using batch operation
            var officeSummaryDict = aggregation.OfficeSummaries.ToDictionary(
                summary => $"planning:office-workload:{summary.CurrentOfficeId}",
                summary => summary
            );

            if (officeSummaryDict.Any())
            {
                await _redis.Batch.Cache.SetManyAsync(officeSummaryDict, TimeSpan.FromHours(6));

                _logger.LogWarning(
                    "Cached {Count} office workloads using batch operation",
                    officeSummaryDict.Count);
            }

            // Store complete aggregation
            var aggregationKey = $"planning:aggregation:{aggregation.Id}";
            await _redis.Cache.SetAsync(aggregationKey, aggregation, TimeSpan.FromHours(24));
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cache aggregation data");
            // Non-critical - continue processing
        }
    }

    private Dictionary<string, OfficeWorkloadAnalysis> AnalyzeOfficeWorkload(
        List<OrderSummaryByOffice> summaries)
    {
        var analysis = new Dictionary<string, OfficeWorkloadAnalysis>();

        foreach (var summary in summaries)
        {
            var workload = new OfficeWorkloadAnalysis
            {
                OfficeId = summary.CurrentOfficeId,
                OfficeName = summary.CurrentOfficeName,
                TotalOrders = summary.TotalOrders,
                TotalWeight = summary.TotalWeight,
                Priority = CalculatePriority(summary),
                NeedsRouteOptimization = summary.TotalOrders > 50,
                NeedsDriverAssignment = summary.TotalOrders > 20,
                EstimatedVehiclesNeeded = EstimateVehiclesNeeded(summary)
            };

            analysis[summary.CurrentOfficeId] = workload;

            _logger.LogInformation(
                "Office workload analysis - Office: {OfficeId}, Orders: {Orders}, Vehicles: {Vehicles}, Priority: {Priority}",
                workload.OfficeId, workload.TotalOrders, workload.EstimatedVehiclesNeeded, workload.Priority);
        }

        return analysis;
    }

    private async Task TriggerPlanningOperationsAsync(
        Dictionary<string, OfficeWorkloadAnalysis> workloadAnalysis,
        CancellationToken cancellationToken)
    {
        // Sort offices by priority (highest first)
        var sortedOffices = workloadAnalysis.Values
            .OrderByDescending(w => w.Priority)
            .ToList();

        foreach (var workload in sortedOffices)
        {
            // TODO: Implement your planning logic here
            // This is where you would:
            // 1. Calculate optimal routes for the office
            // 2. Assign drivers to routes
            // 3. Optimize vehicle utilization
            // 4. Schedule deliveries

            _logger.LogInformation(
                "Triggering planning operations for office {OfficeId} (Priority: {Priority})",
                workload.OfficeId, workload.Priority);

            // Example: Route optimization
            if (workload.NeedsRouteOptimization)
            {
                await OptimizeRoutesForOfficeAsync(workload, cancellationToken);
            }

            // Example: Driver assignment
            if (workload.NeedsDriverAssignment)
            {
                await AssignDriversForOfficeAsync(workload, cancellationToken);
            }
        }
    }

    private int CalculatePriority(OrderSummaryByOffice summary)
    {
        // Priority calculation based on:
        // - Number of orders (more orders = higher priority)
        // - Total weight (heavier loads = higher priority)
        // - Time sensitivity (can be added later)

        var priority = 0;

        // Order count factor
        priority += summary.TotalOrders;

        // Weight factor (1 point per 100kg)
        priority += (int)(summary.TotalWeight / 100);

        // Bonus for high-volume offices
        if (summary.TotalOrders > 100)
            priority += 50;

        return priority;
    }

    private int EstimateVehiclesNeeded(OrderSummaryByOffice summary)
    {
        // Simple estimation: 1 vehicle per 30 orders or 1000kg
        var vehiclesByOrders = (summary.TotalOrders + 29) / 30;
        var vehiclesByWeight = (int)((summary.TotalWeight + 999) / 1000);

        return Math.Max(vehiclesByOrders, vehiclesByWeight);
    }

    private async Task OptimizeRoutesForOfficeAsync(
        OfficeWorkloadAnalysis workload,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Optimizing routes for office {OfficeId} - {Orders} orders, {Vehicles} vehicles needed",
            workload.OfficeId, workload.TotalOrders, workload.EstimatedVehiclesNeeded);

        try
        {
            // Get aggregated route plans for routes originating from this office
            var allRouteAggregations = await _planningAggregationService.GetCurrentAggregationsAsync(cancellationToken);
            var officeRoutes = allRouteAggregations
                .Where(r => r.FromOfficeId == workload.OfficeId)
                .OrderByDescending(r => r.PriorityScore)
                .ToList();

            if (!officeRoutes.Any())
            {
                _logger.LogWarning(
                    "No route aggregations found for office {OfficeId}",
                    workload.OfficeId);
                return;
            }

            _logger.LogInformation(
                "Found {RouteCount} route aggregations for office {OfficeId}",
                officeRoutes.Count,
                workload.OfficeId);

            // Optimize routes based on aggregated data
            foreach (var route in officeRoutes)
            {
                if (route.NeedsOptimization)
                {
                    _logger.LogInformation(
                        "Route {RouteKey} needs optimization - Orders: {TotalOrders}, Duration: {Duration}min, Priority: {Priority}",
                        route.RouteKey,
                        route.TotalOrders,
                        route.TotalDurationMinutes,
                        route.PriorityScore);

                    // TODO: Implement specific route optimization logic
                    // - Consolidate similar routes
                    // - Optimize vehicle assignments based on vehicle type breakdown
                    // - Schedule optimal departure times based on earliest/latest times
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to optimize routes for office {OfficeId}", workload.OfficeId);
            // Continue processing other offices
        }
    }

    private async Task AssignDriversForOfficeAsync(
        OfficeWorkloadAnalysis workload,
        CancellationToken cancellationToken)
    {
        // TODO: Implement driver assignment logic
        // This is a placeholder - implement your actual driver assignment algorithm
        _logger.LogInformation(
            "Assigning drivers for office {OfficeId} - {Vehicles} vehicles needed",
            workload.OfficeId, workload.EstimatedVehiclesNeeded);

        await Task.CompletedTask;
    }
}

/// <summary>
/// Analysis result for an office's workload
/// </summary>
public class OfficeWorkloadAnalysis
{
    public string OfficeId { get; set; } = string.Empty;
    public string OfficeName { get; set; } = string.Empty;
    public int TotalOrders { get; set; }
    public decimal TotalWeight { get; set; }
    public int Priority { get; set; }
    public bool NeedsRouteOptimization { get; set; }
    public bool NeedsDriverAssignment { get; set; }
    public int EstimatedVehiclesNeeded { get; set; }
}
