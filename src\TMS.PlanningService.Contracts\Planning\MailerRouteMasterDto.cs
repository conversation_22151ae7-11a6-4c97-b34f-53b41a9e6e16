﻿using TMS.PlanningService.Contracts.Orders;

namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// DTO for MailerRouteMaster data
/// Represents the master route information for a mailer/order
/// </summary>
public class MailerRouteMasterDto
{
    /// <summary>
    /// Mailer ID (Order ID)
    /// </summary>
    public string MailerId { get; set; } = string.Empty;

    /// <summary>
    /// Child Mailer ID (Order Item ID)
    /// </summary>
    public string ChildMailerId { get; set; } = string.Empty;

    /// <summary>
    /// Current post office location
    /// </summary>
    public string? CurrentPostOffice { get; set; }

    /// <summary>
    /// Indicates if this mailer has a plan route
    /// </summary>
    public bool IsHaveMailerPlanRoute { get; set; }

    /// <summary>
    /// Current status
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Origin post office ID
    /// </summary>
    public string? FromPostOfficeId { get; set; }

    /// <summary>
    /// Origin post office name
    /// </summary>
    public string? FromPostOfficeName { get; set; }

    /// <summary>
    /// Destination post office ID
    /// </summary>
    public string? ToPostOfficeId { get; set; }

    /// <summary>
    /// Destination post office name
    /// </summary>
    public string? ToPostOfficeName { get; set; }

    /// <summary>
    /// Sender address
    /// </summary>
    public string? SenderAddress { get; set; }

    /// <summary>
    /// Receiver address
    /// </summary>
    public string? ReceiverAddress { get; set; }

    /// <summary>
    /// Planned start time
    /// </summary>
    public DateTime? PlanStartTime { get; set; }

    /// <summary>
    /// Planned end time
    /// </summary>
    public DateTime? PlanEndTime { get; set; }

    /// <summary>
    /// Planned duration in minutes
    /// </summary>
    public int PlanDurationMinutes { get; set; }

    /// <summary>
    /// Adjusted start time
    /// </summary>
    public DateTime? AdjustStartTime { get; set; }

    /// <summary>
    /// Adjusted end time
    /// </summary>
    public DateTime? AdjustEndTime { get; set; }

    /// <summary>
    /// Adjusted duration in minutes
    /// </summary>
    public int AdjustDurationMinutes { get; set; }

    /// <summary>
    /// Indicates if the mailer was forwarded
    /// </summary>
    public bool IsForwarded { get; set; }

    /// <summary>
    /// Created date
    /// </summary>
    public DateTime CreatedDate { get; set; }

    /// <summary>
    /// Created by user ID
    /// </summary>
    public string? CreatedUserId { get; set; }

    /// <summary>
    /// Last update date
    /// </summary>
    public DateTime LastUpdateDate { get; set; }

    /// <summary>
    /// Last updated by user
    /// </summary>
    public string? LastUpdateUser { get; set; }

    /// <summary>   
    ///  Order metrics for a specific mailer/order
    /// </summary>
    public OrderMetrics OrderMetrics { get; set; } = new();
}
