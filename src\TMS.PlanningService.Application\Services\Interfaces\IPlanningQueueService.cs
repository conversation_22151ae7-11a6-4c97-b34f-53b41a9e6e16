﻿using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Application.Services.Inferfaces;

public interface IPlanningQueueService
{
    /// <summary>
    /// Add planning data to the in-memory queue
    /// </summary>
    /// <param name="planningRequest">Planning webhook request</param>
    /// <param name="priority">Processing priority (higher = more urgent)</param>
    /// <returns>Queue item Id</returns>
    Guid EnqueuePlanning(PlanningWebhookRequest planningRequest, int priority = 0);

    /// <summary>
    /// Add multiple planning data to the in-memory queue in batch
    /// </summary>
    /// <param name="planningRequests">List of planning webhook requests</param>
    /// <param name="priority">Processing priority for all planning data</param>
    /// <returns>List of batch results with queue IDs or errors</returns>
    List<BatchPlanningResult> EnqueuePlanningBatch(List<PlanningWebhookRequest> planningRequests, int priority = 0);

    /// <summary>
    /// Dequeue planning data for batch processing
    /// </summary>
    /// <param name="batchSize">Maximum number of planning items to dequeue</param>
    /// <returns>List of queued planning items</returns>
    List<QueuedPlanningItem> DequeuePlanning(int batchSize = 100);

    /// <summary>
    /// Mark planning data as successfully processed
    /// </summary>
    /// <param name="queueId">Queue item Id</param>
    void MarkAsProcessed(Guid queueId);

    /// <summary>
    /// Mark planning data as failed and handle retry logic
    /// </summary>
    /// <param name="queueId">Queue item Id</param>
    /// <param name="errorMessage">Error message</param>
    void MarkAsFailed(Guid queueId, string errorMessage);

    /// <summary>
    /// Get queue statistics
    /// </summary>
    /// <returns>Queue statistics</returns>
    QueueStatistics GetStatistics();

    /// <summary>
    /// Get planning status by queue Id
    /// </summary>
    /// <param name="queueId">Queue item Id</param>
    /// <returns>Queue item status or null if not found</returns>
    QueuedPlanningItem? GetPlanningStatus(Guid queueId);
}

public class QueuedPlanningItem
{
    public Guid Id { get; set; } = Guid.NewGuid();
    public PlanningWebhookRequest PlanningRequest { get; set; } = null!;
    public int Priority { get; set; }
    public DateTime QueuedAt { get; set; } = DateTime.UtcNow;
    public DateTime? ProcessingStartedAt { get; set; }
    public DateTime? CompletedAt { get; set; }
    public DateTime? FailedAt { get; set; }
    public int RetryCount { get; set; } = 0;
    public int MaxRetries { get; set; } = 3;
    public string? ErrorMessage { get; set; }
    public QueueItemStatus Status { get; set; } = QueueItemStatus.Pending;
}

public enum QueueItemStatus
{
    Pending = 1,
    Processing = 2,
    Completed = 3,
    Failed = 4,
    RetryScheduled = 5
}

public class QueueStatistics
{
    public int PendingCount { get; set; }
    public int ProcessingCount { get; set; }
    public int CompletedCount { get; set; }
    public int FailedCount { get; set; }
    public int TotalProcessedToday { get; set; }
    public DateTime LastProcessedAt { get; set; }
    public TimeSpan AverageProcessingTime { get; set; }
}

public class BatchPlanningResult
{
    public Guid? QueueId { get; set; }
    public string MailerID { get; set; } = string.Empty;
    public string ChildMailerID { get; set; } = string.Empty;
    public bool Success { get; set; }
    public string? ErrorMessage { get; set; }
}