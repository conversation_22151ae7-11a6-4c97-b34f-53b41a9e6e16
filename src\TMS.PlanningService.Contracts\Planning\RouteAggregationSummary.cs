﻿using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// Aggregated summary of adjust plans grouped by FromOffice:ToOffice route
/// Used for optimizing inter-office mail routing and planning
/// </summary>
public class RouteAggregationSummary
{
    public Guid Id { get; set; } = SequentialGuidGenerator.NewPostgreSqlGuid();

    /// <summary>
    /// Composite key: FromTime:ToTime:FromOfficeId:ToOfficeId
    /// Formatted with time first for easy chronological sorting
    /// </summary>
    public string RouteKey { get; set; } = string.Empty;

    /// <summary>
    /// Source office ID
    /// </summary>
    public string FromOfficeId { get; set; } = string.Empty;

    /// <summary>
    /// Destination office ID
    /// </summary>
    public string ToOfficeId { get; set; } = string.Empty;

    /// <summary>
    /// Planned departure time from source office
    /// </summary>
    public DateTime? FromTime { get; set; }

    /// <summary>
    /// Planned arrival time at destination office
    /// </summary>
    public DateTime? ToTime { get; set; }

    /// <summary>
    /// Actual departure time from source office (from MailerActualRoute)
    /// </summary>
    public DateTime? ActualFromTime { get; set; }

    /// <summary>
    /// Actual arrival time at destination office (from MailerActualRoute)
    /// </summary>
    public DateTime? ActualToTime { get; set; }

    /// <summary>
    /// Total duration in minutes for this route
    /// </summary>
    public int TotalDurationMinutes { get; set; }

    /// <summary>
    /// Average duration per mailer in minutes
    /// </summary>
    public double AverageDurationMinutes { get; set; }

    /// <summary>
    /// Earliest planned start time for this route
    /// </summary>
    public DateTime? EarliestStartTime { get; set; }

    /// <summary>
    /// Latest planned end time for this route
    /// </summary>
    public DateTime? LatestEndTime { get; set; }

    /// <summary>
    /// Breakdown by transport provider type
    /// </summary>
    public List<OptionsCountDto> TransportProviderBreakdown { get; set; } = new();

    /// <summary>
    /// Breakdown by transport vehicle type
    /// </summary>
    public List<OptionsCountDto> VehicleTypeBreakdown { get; set; } = new();

    /// <summary>
    /// Breakdown by transport method
    /// </summary>
    public List<OptionsCountDto> TransportMethodBreakdown { get; set; } = new();

    /// <summary>
    /// Priority score for this route (higher = more critical)
    /// </summary>
    public int PriorityScore { get; set; }

    /// <summary>
    /// Indicates if this route needs optimization
    /// </summary>
    public bool NeedsOptimization { get; set; }

    /// <summary>
    /// Total number of orders on this route (from OrderService)
    /// </summary>
    public int TotalOrders { get; set; }

    /// <summary>
    /// Total number of order items on this route (from OrderService)
    /// </summary>
    public int TotalItems { get; set; }

    /// <summary>
    /// Total weight of orders on this route (from OrderService)
    /// </summary>
    public decimal TotalWeight { get; set; }

    /// <summary>
    /// Total real weight of orders on this route (from OrderService)
    /// </summary>
    public decimal TotalRealWeight { get; set; }

    /// <summary>
    /// Total calculated weight of orders on this route (from OrderService)
    /// </summary>
    public decimal TotalCalWeight { get; set; }

    /// <summary>
    /// Detailed information about orders on this route (snapshot)
    /// Key: MailerId, Value: OrderOnRoute containing all child mailer IDs for that order
    /// OrderDetails.Keys represents all unique order IDs (MailerIds) currently on this route
    /// Each OrderOnRoute.Items contains the list of all child mailer IDs (order items) for that order
    /// </summary>
    public Dictionary<string, OrderOnRoute> OrderDetails { get; set; } = new();

    /// <summary>
    /// Timestamp when this route was first created/aggregated in the system (immutable)
    /// This value never changes after the route is first created
    /// Represents the original creation time
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Timestamp when aggregation was last updated
    /// This updates every time the route data changes
    /// </summary>
    public DateTime AggregatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Timestamp when this aggregation was last persisted to database
    /// Used for in-memory change detection to avoid unnecessary database writes
    /// Null if never persisted
    /// </summary>
    public DateTime? LastPersistedAt { get; set; }

    public decimal TotalDiffWeight { get; set; }

    /// <summary>
    /// Name of Priority Plan associated with this route, if any
    /// </summary>
    public Guid? PriorityPlanId { get; set; }
    public string? PriorityPlanName {get; set; }
    
    /// Daily Planning ID this route aggregation belongs to (for daily planning only)
    /// Null for normal/priority aggregations
    /// </summary>
    public Guid? DailyPlanningId { get; set; }

    /// <summary>
    /// Source office business operation type (for daily planning only)
    /// Default value for normal/priority aggregations
    /// </summary>
    public BusinessOperation FromBusinessOperation { get; set; }

    /// <summary>
    /// Destination office business operation type (for daily planning only)
    /// Default value for normal/priority aggregations
    /// </summary>
    public BusinessOperation ToBusinessOperation { get; set; }


    public bool? IsPlanningExist { get; set; } = false;

    public bool? IsDeleted { get; set; } = false;
}
 
/// <summary>
/// Lightweight metadata for route aggregations
/// Used for efficient filtering without fetching full aggregation objects
/// Stored in AllRoutesKey for fast change detection
/// </summary>
public class RouteMetadata
{
    /// <summary>
    /// Composite key: FromTime:ToTime:FromOfficeId:ToOfficeId
    /// Office IDs are embedded in the key, parse if needed
    /// </summary>
    public string RouteKey { get; set; } = string.Empty;

    /// <summary>
    /// Timestamp when aggregation was last updated
    /// </summary>
    public DateTime AggregatedAt { get; set; }

    /// <summary>
    /// Timestamp when aggregation was last persisted to database
    /// Null if never persisted
    /// </summary>
    public DateTime? LastPersistedAt { get; set; }
}

/// <summary>
/// Represents an order on a route with all its child mailer IDs (order items)
/// Snapshot of order details at aggregation time
/// </summary>
public class OrderOnRoute
{
    /// <summary>
    /// Mailer ID (Order ID) - unique identifier for this order
    /// </summary>
    public string MailerId { get; set; } = string.Empty;

    /// <summary>
    /// List of all child mailer IDs (order items) for this order
    /// Example: order01 has items [order01/2, order01/3, order01/4]
    /// </summary>
    public List<OrderItemDetail> Items { get; set; } = new();

    /// <summary>
    /// Current status of the order (from parent order)
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Service type for this order
    /// </summary>
    public string? ServiceTypeId { get; set; }

    /// <summary>
    /// Extra services for this order
    /// </summary>
    public string? ExtraService { get; set; }

    /// <summary>
    /// Total weight of all items (sum of all child mailer weights)
    /// </summary>
    public decimal? Weight { get; set; }

    /// <summary>
    /// Total real weight of all items (sum of all child mailer real weights)
    /// </summary>
    public decimal? RealWeight { get; set; }

    /// <summary>
    /// Total calculated weight of all items (sum of all child mailer calculated weights)
    /// </summary>
    public decimal? CalWeight { get; set; }

    /// <summary>
    /// Total number of items (sum of all child mailer items)
    /// </summary>
    public int? TotalItems { get; set; }
    /// <summary>
    /// 
    /// </summary>
    public bool IsDeleted { get; set; } = false;
}

/// <summary>
/// Represents a single order item (child mailer) within an order
/// </summary>
public class OrderItemDetail
{
    /// <summary>
    /// Child Mailer ID (Order Item ID)
    /// Example: "order01/2", "order01/3"
    /// </summary>
    public string ChildMailerId { get; set; } = string.Empty;

    /// <summary>
    /// Child Mailer ID (Order Item ID)
    /// Example: "order01/2", "order01/3"
    /// </summary>
    public string MailerId { get; set; } = string.Empty;
     
    /// <summary>
    /// Weight of this specific child mailer
    /// </summary>
    public decimal Weight { get; set; }

    /// <summary>
    /// Real weight of this specific child mailer
    /// </summary>
    public decimal RealWeight { get; set; }

    /// <summary>
    /// Calculated weight of this specific child mailer
    /// </summary>
    public decimal CalWeight { get; set; }

    /// <summary>
    /// Current status of the order on this route
    /// </summary>
    public string? Status { get; set; }

    /// <summary>
    /// Service type for this order
    /// </summary>
    public string? ServiceTypeId { get; set; }

    /// <summary>
    /// From time and To Time from actual route
    /// </summary>
    public DateTime? ActualFromTime { get; set; }
     
    /// <summary>
    ///  
    /// </summary>
    public DateTime? ActualToTime { get; set; }

    /// <summary>
    /// Current parent container ID (bag/pouch)
    /// </summary>
    public string? CurrentParentId { get; set; }

    /// <summary>
    /// Current parent container type
    /// </summary>
    public string? CurrentParentType { get; set; }

    /// <summary>
    /// Current packing list ID
    /// </summary>
    public string? CurrentPackingListId { get; set; }

    /// <summary>
    /// Status delete of order item
    /// </summary>
    public bool? IsDeleted { get; set; } = false;
}
 


