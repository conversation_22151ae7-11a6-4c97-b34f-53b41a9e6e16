﻿using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.Dto;
public class PriorityPlanGroupDto
{
    public Guid? Id { get; set; }
    public Guid? PriorityPlanId { get; set; }
    public LogicalOperator? LogicOperator { get; set; }
    public int StepNumber { get; set; }
    public List<PriorityPlanGroupAttrDto> PriorityAttributes{ get; set; } = new List<PriorityPlanGroupAttrDto>();
};
