﻿using FluentValidation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application;
using TMS.PlanningService.Application.Features.Planning.Commands.QueueBatchPlanning;
using TMS.PlanningService.Application.Features.Planning.Commands.QueuePlanning;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Api.Controllers.Webhook;

[ApiExplorerSettings(IgnoreApi = true)]
[ApiController]
//[Route("api/v{version:apiVersion}/webhook/planning")]
[Produces("application/json")]
public class PlanningWebhookController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly IPlanningQueueService _queueService;
    private readonly ILogger<PlanningWebhookController> _logger;

    public PlanningWebhookController(
        IMediator mediator,
        IPlanningQueueService queueService,
        ILogger<PlanningWebhookController> logger)
    {
        _mediator = mediator;
        _queueService = queueService;
        _logger = logger;
    }

    /// <summary>
    /// Receive planning webhook from external system - queues for batch processing
    /// </summary>
    /// <param name="request">Planning webhook data</param>
    /// <param name="priority">Processing priority (higher = more urgent)</param>
    /// <returns>Webhook processing result</returns>
    [HttpPost("receive")]
    [ProducesResponseType(typeof(PlanningWebhookResponse), StatusCodes.Status202Accepted)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize(Policy = PermissionDefinition.WebhookPolicy)]
    public async Task<ActionResult<PlanningWebhookResponse>> ReceivePlanning(
        [FromBody] PlanningWebhookRequest request,
        [FromQuery] int priority = 0)
    {
        try
        {
            _logger.LogInformation("Received planning webhook for Mailer Id: {MailerId}, Child Mailer Id: {ChildMailerId} with priority: {Priority}",
                request.MailerRouteMaster.MailerId, request.MailerRouteMaster.ChildMailerId, priority);

            // Queue the planning data for batch processing
            var command = new QueuePlanningCommand(request, priority);
            var queueId = await _mediator.Send(command);

            _logger.LogInformation("Successfully queued planning webhook - Mailer Id: {MailerId}, Child Mailer Id: {ChildMailerId}, Queue Id: {QueueId}",
                request.MailerRouteMaster.MailerId, request.MailerRouteMaster.ChildMailerId, queueId);

            return Accepted(new PlanningWebhookResponse
            {
                Success = true,
                Message = "Planning data queued for processing",
                ProcessedMailerId = request.MailerRouteMaster.MailerId,
                ProcessedChildMailerId = request.MailerRouteMaster.ChildMailerId,
                ProcessedAt = DateTime.UtcNow,
                AdditionalData = new Dictionary<string, object>
                {
                    ["queueId"] = queueId,
                    ["priority"] = priority,
                    ["planRouteCount"] = request.MailerPlanRoutes.Count,
                    ["adjustRouteCount"] = request.MailerAdjustRoutes.Count,
                    ["actualRouteCount"] = request.MailerActualRoutes.Count,
                    ["estimatedProcessingTime"] = "Within 5-15 minutes"
                }
            });
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning(ex, "Invalid planning data received for Mailer Id: {MailerId}",
                request.MailerRouteMaster.MailerId);
            return BadRequest(new PlanningWebhookResponse
            {
                Success = false,
                Message = ex.Message,
                ProcessedMailerId = request.MailerRouteMaster.MailerId,
                ProcessedChildMailerId = request.MailerRouteMaster.ChildMailerId,
                ProcessedAt = DateTime.UtcNow
            });
        }
        catch (ValidationException ex)
        {
            _logger.LogError(ex, "Validation planning webhook for Mailer Id: {MailerId}",
                request.MailerRouteMaster.MailerId);

            return BadRequest(new PlanningWebhookResponse
            {
                Success = false,
                Message = ex.Message,
                ProcessedMailerId = request.MailerRouteMaster.MailerId,
                ProcessedChildMailerId = request.MailerRouteMaster.ChildMailerId,
                ProcessedAt = DateTime.UtcNow
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error queueing planning webhook for Mailer Id: {MailerId}",
                request.MailerRouteMaster.MailerId);
            return StatusCode(StatusCodes.Status500InternalServerError, new PlanningWebhookResponse
            {
                Success = false,
                Message = "An unexpected error occurred while queueing the planning data",
                ProcessedMailerId = request.MailerRouteMaster.MailerId,
                ProcessedChildMailerId = request.MailerRouteMaster.ChildMailerId,
                ProcessedAt = DateTime.UtcNow
            });
        }
        
    }

    /// <summary>
    /// Receive multiple planning data in batch from external system - queues all for batch processing
    /// </summary>
    /// <param name="batchRequest">Batch planning webhook data</param>
    /// <returns>Batch webhook processing result</returns>
    [HttpPost("receive-batch")]
    [ProducesResponseType(typeof(BatchPlanningWebhookResponse), StatusCodes.Status202Accepted)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [Authorize(Policy = PermissionDefinition.WebhookPolicy)]
    public async Task<ActionResult<BatchPlanningWebhookResponse>> ReceiveBatchPlanning([FromBody] BatchPlanningWebhookRequest batchRequest)
    {
        try
        {
            _logger.LogInformation("Received batch planning webhook - Batch Id: {BatchId}, Planning Count: {PlanningCount}, Source: {Source}",
                batchRequest.BatchId, batchRequest.PlanningData.Count, batchRequest.Source);

            // Queue the entire batch for processing
            var command = new QueueBatchPlanningCommand(batchRequest);
            var result = await _mediator.Send(command);

            _logger.LogInformation("Successfully processed batch planning webhook - Batch Id: {BatchId}, Queued: {Queued}, Skipped: {Skipped}",
                batchRequest.BatchId, result.QueuedPlanningData, result.SkippedPlanningData);

            return Accepted(result);
        } 
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error processing batch planning webhook - Batch Id: {BatchId}", batchRequest.BatchId);
            return StatusCode(StatusCodes.Status500InternalServerError, new BatchPlanningWebhookResponse
            {
                Success = false,
                Message = "An unexpected error occurred while processing the batch planning data",
                ProcessedAt = DateTime.UtcNow,
                BatchId = batchRequest.BatchId
            });
        }
    }

    /// <summary>
    /// Check planning processing status by queue Id
    /// </summary>
    /// <param name="queueId">Queue Id returned from receive endpoint</param>
    /// <returns>Planning processing status</returns>
    [HttpGet("status/{queueId:guid}")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [Authorize(Policy = PermissionDefinition.WebhookPolicy)]
    public IActionResult GetPlanningStatus(Guid queueId)
    {
        try
        {
            var queuedPlanning = _queueService.GetPlanningStatus(queueId);

            if (queuedPlanning == null)
            {
                return NotFound(new
                {
                    Success = false,
                    Message = "Planning data not found in queue",
                    QueueId = queueId
                });
            }

            return Ok(new
            {
                Success = true,
                QueueId = queuedPlanning.Id,
                MailerID = queuedPlanning.PlanningRequest.MailerRouteMaster.MailerId,
                ChildMailerID = queuedPlanning.PlanningRequest.MailerRouteMaster.ChildMailerId,
                Status = queuedPlanning.Status.ToString(),
                queuedPlanning.Priority,
                queuedPlanning.QueuedAt,
                queuedPlanning.ProcessingStartedAt,
                queuedPlanning.CompletedAt,
                queuedPlanning.FailedAt,
                queuedPlanning.RetryCount,
                queuedPlanning.MaxRetries,
                queuedPlanning.ErrorMessage
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking planning status for Queue Id: {QueueId}", queueId);
            return StatusCode(StatusCodes.Status500InternalServerError, new
            {
                Success = false,
                Message = "An error occurred while checking planning status"
            });
        }
    }

    /// <summary>
    /// Get queue metrics and statistics
    /// </summary>
    /// <returns>Queue statistics</returns>
    [HttpGet("metrics")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    [Authorize(Policy = PermissionDefinition.WebhookPolicy)]
    public IActionResult GetQueueMetrics()
    {
        try
        {
            var stats = _queueService.GetStatistics();

            return Ok(new
            {
                Success = true,
                Timestamp = DateTime.UtcNow,
                Queue = new
                {
                    PendingPlanning = stats.PendingCount,
                    ProcessingPlanning = stats.ProcessingCount,
                    CompletedPlanning = stats.CompletedCount,
                    FailedPlanning = stats.FailedCount,
                    stats.TotalProcessedToday,
                    stats.LastProcessedAt,
                    AverageProcessingTime = stats.AverageProcessingTime.TotalMilliseconds + "ms"
                },
                Performance = new
                {
                    ThroughputCapacity = "500 planning records per batch, every 2 minutes",
                    EstimatedDailyCapacity = "360,000 planning records/day",
                    QueueType = "In-Memory Static Storage"
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving queue metrics");
            return StatusCode(StatusCodes.Status500InternalServerError, new
            {
                Success = false,
                Message = "Error retrieving queue metrics"
            });
        }
    }

    /// <summary>
    /// Health check endpoint for webhook service
    /// </summary>
    /// <returns>Health status</returns>
    [AllowAnonymous]
    [HttpGet("health")]
    [ProducesResponseType(typeof(object), StatusCodes.Status200OK)]
    public IActionResult Health()
    {
        return Ok(new { Status = "Healthy", Timestamp = DateTime.UtcNow });
    }
}
