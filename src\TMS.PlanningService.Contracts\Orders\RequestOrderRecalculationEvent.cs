using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernal.RabbitMq.Abstractions;

namespace TMS.PlanningService.Contracts.Orders;

/// <summary>
/// Event sent to OrderService when OrderMetrics cannot be retrieved
/// Contains the original RabbitMqPlanningEvent so OrderService can send it back for recalculation
/// </summary>
public class RequestOrderRecalculationEvent : IEvent
{
    public string EventType => "ReqorderRecalculation";
    public string EventId { get; set; } = Guid.NewGuid().ToString();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// The original planning event that needs recalculation
    /// OrderService will send this back to PlanningService after processing
    /// </summary>
    public object OriginalPlanningEvent { get; set; } = null!;

    /// <summary>
    /// List of Order IDs (Mailer IDs) that were not found in cache
    /// </summary>
    public List<string> MissingOrderIds { get; set; } = new();

    /// <summary>
    /// Reason for requesting recalculation
    /// </summary>
    public string Reason { get; set; } = "OrderMetrics not found in cache";

    /// <summary>
    /// Original request timestamp for tracking
    /// </summary>
    public DateTime RequestTimestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Current retry attempt number (extracted from original planning event)
    /// </summary>
    public int RetryCount { get; set; } = 0;

    /// <summary>
    /// Maximum number of retries allowed before giving up
    /// </summary>
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Original event ID to link back to the source planning event
    /// Used for tracking and debugging retry chains
    /// </summary>
    public string OriginalEventId { get; set; } = "";

    /// <summary>
    /// Timestamp of the first attempt (null for subsequent retries)
    /// Helps track how long we've been trying to process this event
    /// </summary>
    public DateTime? FirstAttemptTime { get; set; }
}
