﻿using MediatR;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Application.Features.Planning.Commands.QueueBatchPlanning;

public class QueueBatchPlanningCommandHandler : IRequestHandler<QueueBatchPlanningCommand, BatchPlanningWebhookResponse>
{
    private readonly IPlanningQueueService _queueService;

    public QueueBatchPlanningCommandHandler(IPlanningQueueService queueService)
    {
        _queueService = queueService;
    }

    public Task<BatchPlanningWebhookResponse> Handle(QueueBatchPlanningCommand request, CancellationToken cancellationToken)
    {
        var results = _queueService.EnqueuePlanningBatch(request.BatchRequest.PlanningData);

        var successfulResults = results.Where(r => r.Success).ToList();
        var failedResults = results.Where(r => !r.Success).ToList();

        var response = new BatchPlanningWebhookResponse
        {
            Success = true,
            Message = $"Batch processed: {successfulResults.Count} queued, {failedResults.Count} failed",
            BatchId = request.BatchRequest.BatchId,
            QueuedPlanningData = successfulResults.Count,
            SkippedPlanningData = failedResults.Count,
            ProcessedAt = DateTime.UtcNow,
            AdditionalData = new Dictionary<string, object>
            {
                ["queuedItems"] = successfulResults.Select(r => new { r.QueueId, r.MailerID, r.ChildMailerID }),
                ["failedItems"] = failedResults.Select(r => new { r.MailerID, r.ChildMailerID, r.ErrorMessage })
            }
        };

        return Task.FromResult(response);
    }
}