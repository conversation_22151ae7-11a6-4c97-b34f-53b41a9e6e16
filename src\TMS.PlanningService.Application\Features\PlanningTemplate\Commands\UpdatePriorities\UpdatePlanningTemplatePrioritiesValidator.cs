﻿using FluentValidation;
using TMS.PlanningService.Contracts.PlanningTemplate;
using TMS.SharedKernel.Constants;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.UpdatePriorities;

public class UpdatePlanningTemplatePrioritiesValidator : AbstractValidator<UpdatePlanningTemplatePrioritiesRequest>
{
    public UpdatePlanningTemplatePrioritiesValidator()
    {
        RuleFor(x => x.Items)
        .NotNull()
        .WithMessage(string.Format(ValidationMessages.Required, nameof(UpdatePlanningTemplatePrioritiesRequest.Items)))
        .Must(items => items != null && items.Count > 0)
        .WithMessage("Items cannot be empty");

        RuleForEach(x => x.Items).ChildRules(item =>
        {
            item.RuleFor(i => i.Id)
             .NotEmpty()
             .WithMessage(string.Format(ValidationMessages.Required, nameof(UpdatePlanningTemplatePriorityItem.Id)));

            item.RuleFor(i => i.PriorityNumber)
             .GreaterThan(0)
             .WithMessage(string.Format(ValidationMessages.MinValue, nameof(UpdatePlanningTemplatePriorityItem.PriorityNumber), 1));
        });

        RuleFor(x => x.Items)
        .Must(items => items == null || items.Select(i => i.Id).Distinct().Count() == items.Count)
        .WithMessage("Duplicate Ids are not allowed in Items");
    }
}
