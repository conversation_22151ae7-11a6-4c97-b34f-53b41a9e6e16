﻿using System.ComponentModel.DataAnnotations;

namespace TMS.PlanningService.Contracts.Planning;

public class PlanningWebhookRequest
{
    public MailerRouteMaster MailerRouteMaster { get; set; } = new();
    public List<MailerPlanRoute> MailerPlanRoutes { get; set; } = new();
    public List<MailerAdjustRoute> MailerAdjustRoutes { get; set; } = new();
    public List<MailerActualRoute> MailerActualRoutes { get; set; } = new();
  
}

public class MailerRouteMaster
{ 
    public string MailerId { get; set; } = string.Empty;
    public string? ChildMailerId { get; set; } = string.Empty;
    public string? CurrentPostOffice { get; set; }
    public string? NextPostOffice { get; set; }
    public bool? IsCorrectRoute { get; set; }
    public bool? IsFullMailerPlanRoute { get; set; }
    public bool IsHaveMailerPlanRoute { get; set; }
    public string? Status { get; set; }
    public string? FromPostOfficeId { get; set; }
    public string? ToPostOfficeId { get; set; }
    public string? SenderAddress { get; set; }
    public string? ReceiverAddress { get; set; }
    public string? LastPOInPlan { get; set; }
    public DateTime? PlanStartTime { get; set; }
    public DateTime? PlanEndTime { get; set; }
    public int PlanDurationMinutes { get; set; }
    public DateTime? AdjustStartTime { get; set; }
    public DateTime? AdjustEndTime { get; set; }
    public DateTime? ActualStartTime { get; set; }
    public DateTime? ActualEndTime { get; set; }
    public int AdjustDurationMinutes { get; set; }
    public bool IsForwarded { get; set; }
    public string? POsSendWrongWay { get; set; }
    public string? CurrentSLAType { get; set; }
    public DateTime? CurrentSLATime { get; set; }
    public DateTime? CreatedDate { get; set; }
    public string? CreatedUserId { get; set; }
    public DateTime? LastUpdateDate { get; set; }
    public string? LastUpdateUser { get; set; }
}

public class MailerPlanRoute
{  
    public string MailerId { get; set; } = string.Empty;
    public string? ChildMailerId { get; set; } = string.Empty;
    public string? FromPostOfficeId { get; set; }
    public string? ToPostOfficeId { get; set; }
    public string? LeadTimeId { get; set; }
    public string? TransportProviderType { get; set; }
    public string? TransportProviderTypeName { get; set; }
    public string? TransportVehicleType { get; set; }
    public string? TransportVehicleTypeName { get; set; }
    public string? TransportMethodId { get; set; }
    public string? TransportMethodName { get; set; }
    public DateTime? FromTime { get; set; }
    public int FromTimeDelay { get; set; }
    public DateTime? ToTime { get; set; }
    public int ToTimeDelay { get; set; }
    public int AddDays { get; set; }
    public int Step { get; set; }
    public string? Type { get; set; }
    public string? TypeName { get; set; }
    public int HistoryId { get; set; }
    public int? PreviousStep { get; set; } 
    public string? LeadTimeTypeId { get; set; }
    public string? LeadTimeTypeName { get; set; }
    public string? ServiceTypeId { get; set; }
    public string? ServiceTypeName { get; set; }
    public string? ExtraService { get; set; }
    public string? ExtraServiceName { get; set; }

}

public class MailerActualRoute : MailerPlanRoute
{
    // Add more properties specific to actual routes if needed
}

public class MailerAdjustRoute : MailerPlanRoute
{
    // Add more properties specific to actual routes if needed
}
