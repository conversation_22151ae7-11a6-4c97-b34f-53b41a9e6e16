﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Contracts.Orders;
public class OrderMetrics
{
    public string OrderId { get; set; } = string.Empty;
    public DateTime? AcceptedTime { get; set; } = DateTime.UtcNow;
    public string? CurrentOfficeId { get; set; }
    public List<OrderItemMetric> Items { get; set; } = new();
    public decimal Weight { get; set; }
    public decimal RealWeight { get; set; }
    public decimal CalWeight { get; set; }
    public bool IsDeleted { get; set; }
    public string? ServiceTypeId { get; set; } 
    public string? ServiceTypeName { get; set; }
    public List<ExtraService>? ExtraServices { get; set; }
    public string? CurrentStatusId { get; set; }
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;
    public string? SenderPostOfficeId { get; set; }
    public string? ReceiverPostOfficeId { get; set; }
    public string? SenderWardId { get; set; }
    public string? ReceiverWardId { get; set; }
    public string? SenderProvinceId { get; set; }
    public string? ReceiverProvinceId { get; set; }
    public string? CurrentParentId { get; set; }
    public string? CurrentParentType { get; set; }
    public string? CurrentPackingListId { get; set; }
}

public class OrderItemMetric
{
    public string OrderItemId { get; set; } = string.Empty;
    public string OrderId { get; set; } = string.Empty;
    public decimal Weight { get; set; }
    public decimal RealWeight { get; set; }
    public decimal CalWeight { get; set; }
    public decimal? L { get; set; }
    public decimal? H { get; set; }
    public decimal? W { get; set; }
    public string? CurrentStatusId { get; set; }
    public string? CurrentPostOfficeId { get; set; }
}

public class ExtraService
{
    public string ExtraServiceId { get; set; } = string.Empty;
    public string ExtraServiceName { get; set; } = string.Empty;
}
