﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.LeadTime.Queries.GetLeadTimeById;
using TMS.PlanningService.Application.Features.LeadTime.Queries.GetLeadTimes;
using TMS.PlanningService.Application.Features.LeadTimePartner.CreateLeadTimePartner;
using TMS.PlanningService.Application.Features.LeadTimePartner.UpdateLeadTimePartner;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.CreatePriorityPlan;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdatePriorityPlan;
using TMS.PlanningService.Application.Features.PriorityPlan.Queries.GetPriorityPlanById;
using TMS.PlanningService.Application.Features.PriorityRoute.Queries.GetPriorityPlans;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.LeadTimePartner;
using TMS.PlanningService.Contracts.PmsSync;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/lead-times")]
[Produces("application/json")]
public class LeadTimeController : ControllerBase
{
    private readonly IMediator _mediator;

    public LeadTimeController(IMediator mediator)
    {
        _mediator = mediator;
    }


    ///// <summary>
    ///// Get all lead time has type = "Kết nối đối tác VC"
    ///// </summary> 
    ///// <returns>List of lead time</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(PagedResult<LeadTimeConfigDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResult<LeadTimeConfigDto>>> GetLeadTimesIncludePartner([FromBody] GetLeadTimeRequest request)
    {
        var query = new GetLeadTimesQuery(request);
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    ///// <summary>
    ///// Get all lead time type = "Kết nối đối tác VC" by Id
    ///// </summary>
    ///// <param name="id">Lead time Id</param>
    ///// <returns>Lead time details</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(typeof(LeadTimeConfigDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<LeadTimeConfigDto>> GetLeadTimeById(string id)
    {
        var query = new GetLeadTimeByIdQuery(id);
        var result = await _mediator.Send(query);

        if (result == null)
            return NotFound();

        return Ok(result);
    }
}
