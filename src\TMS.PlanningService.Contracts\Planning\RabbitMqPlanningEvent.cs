﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using TMS.SharedKernal.RabbitMq.Abstractions;

namespace TMS.PlanningService.Contracts.Planning;
public class RabbitMqPlanningEvent : IEvent
{
    public string EventType => "RabbitMqPlanningEvent";
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public List<PlanningWebhookRequest> PlanningData { get; set; } = new();
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    public string EventId => Guid.NewGuid().ToString();
    DateTime IEvent.CreatedAt => DateTime.UtcNow;

    /// <summary>
    /// Number of times this event has been processed (for retry tracking)
    /// Incremented each time OrderMetrics are not found and event is re-sent
    /// </summary>
    public int ProcessingAttempts { get; set; } = 0;

    /// <summary>
    /// Maximum number of processing attempts before giving up
    /// Default is 3 to prevent infinite loops
    /// </summary>
    public int MaxProcessingAttempts { get; set; } = 3;

    /// <summary>
    /// History of processing attempts for debugging and tracking
    /// Each entry contains timestamp and reason for retry
    /// </summary>
    public List<string> ProcessingHistory { get; set; } = new();
}
