﻿using System.Linq.Expressions;
using LinqKit;
using Mapster;
using MediatR;
using TMS.PlanningService.Contracts.Dto;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using Entity = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Features.TransportVehicleType.Queries.GetTransportVehicleTypes;

public class GetTransportVehicleTypesQueryHandler : IRequestHandler<GetTransportVehicleTypesQuery, List<TransportVehicleTypeDto>>
{
    private readonly IMetadataCacheService _metadataCacheService;
    private readonly IBaseRepository<TransportVehicleTypeDto> _baseRepository;
    public GetTransportVehicleTypesQueryHandler(IMetadataCacheService metadataCacheService,
        IBaseRepository<TransportVehicleTypeDto> baseRepository)
    {
        _metadataCacheService = metadataCacheService;
        _baseRepository = baseRepository;
    }

    public async Task<List<TransportVehicleTypeDto>> Handle(GetTransportVehicleTypesQuery request, CancellationToken cancellationToken)
    {
        var vehicleTypes = await _metadataCacheService.GetTransportVehicleTypesAsync<Entity.TransportVehicleType>();
        var data = vehicleTypes.Select(x => new TransportVehicleTypeDto
        {
            Id = x.Id,
            Name = x.Name,
        }).ToList();

        if (!data.Any())
        {
            data = (await _baseRepository.GetAllAsync()).Select(x => new TransportVehicleTypeDto
            {
                Id = x.Id,
                Name = x.Name,
            }).ToList();
        }

        return data.OrderBy(x => x.Id).ToList();
    }
}
