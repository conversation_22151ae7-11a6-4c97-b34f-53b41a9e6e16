﻿using FluentValidation;
using Microsoft.AspNetCore.Http;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.ImportPlanningTemplates;
public class ImportPlanningTemplatesCommandValidator : AbstractValidator<ImportPlanningTemplatesCommand>
{
    private readonly string[] AllowedExtensions = { ".xlsx", ".xls" };
    private readonly int MaxFileSizeInMB = 5;

    public ImportPlanningTemplatesCommandValidator()
    {
        var maxFileSizeInBytes = MaxFileSizeInMB * 1024 * 1024;
        RuleFor(c => c.File)
            .NotNull().WithMessage("File is required")
            .Must(f => f.Length > 0).WithMessage("File cannot be empty")
            .Must(f => f.Length <= maxFileSizeInBytes).WithMessage($"File size cannot exceed {MaxFileSizeInMB}MB")
            .Must(IsExcelFile).WithMessage($"File extension must be one of: {string.Join(", ", AllowedExtensions)}");
    }

    private bool IsExcelFile(IFormFile file)
    {
        if (file == null)
            return false;

        var extension = Path.GetExtension(file.FileName)?.ToLowerInvariant();

        return !string.IsNullOrEmpty(extension) && AllowedExtensions.Contains(extension);
    }
}
