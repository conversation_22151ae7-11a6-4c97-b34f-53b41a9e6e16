﻿using MediatR;
using TMS.PlanningService.Application.Services.Inferfaces;

namespace TMS.PlanningService.Application.Features.Planning.Commands.QueuePlanning;

public class QueuePlanningCommandHandler : IRequestHandler<QueuePlanningCommand, Guid>
{
    private readonly IPlanningQueueService _queueService;

    public QueuePlanningCommandHandler(IPlanningQueueService queueService)
    {
        _queueService = queueService;
    }

    public Task<Guid> Handle(QueuePlanningCommand request, CancellationToken cancellationToken)
    {
        var queueId = _queueService.EnqueuePlanning(request.PlanningRequest, request.Priority);
        return Task.FromResult(queueId);
    }
}