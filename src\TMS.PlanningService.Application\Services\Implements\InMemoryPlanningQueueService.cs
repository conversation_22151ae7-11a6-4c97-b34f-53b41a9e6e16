﻿using System.Collections.Concurrent;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Application.Services.Implements;

public class InMemoryPlanningQueueService : IPlanningQueueService
{
    private readonly ConcurrentQueue<QueuedPlanningItem> _pendingQueue = new();
    private readonly ConcurrentDictionary<Guid, QueuedPlanningItem> _allItems = new();
    private readonly object _statsLock = new();
    private QueueStatistics _stats = new();

    public Guid EnqueuePlanning(PlanningWebhookRequest planningRequest, int priority = 0)
    {
        if (planningRequest?.MailerRouteMaster == null)
            throw new ArgumentException("Planning request and MailerRouteMaster cannot be null");

        if (string.IsNullOrWhiteSpace(planningRequest.MailerRouteMaster.MailerId))
            throw new ArgumentException("MailerId is required");

        if (string.IsNullOrWhiteSpace(planningRequest.MailerRouteMaster.ChildMailerId))
            throw new ArgumentException("ChildMailerId is required");

        var queuedItem = new QueuedPlanningItem
        {
            Id = Guid.NewGuid(),
            PlanningRequest = planningRequest,
            Priority = priority,
            QueuedAt = DateTime.UtcNow,
            Status = QueueItemStatus.Pending
        };

        _pendingQueue.Enqueue(queuedItem);
        _allItems.TryAdd(queuedItem.Id, queuedItem);

        lock (_statsLock)
        {
            _stats.PendingCount++;
        }

        return queuedItem.Id;
    }

    public List<BatchPlanningResult> EnqueuePlanningBatch(List<PlanningWebhookRequest> planningRequests, int priority = 0)
    {
        var results = new List<BatchPlanningResult>();

        foreach (var request in planningRequests)
        {
            try
            {
                var queueId = EnqueuePlanning(request, priority);
                results.Add(new BatchPlanningResult
                {
                    QueueId = queueId,
                    MailerID = request.MailerRouteMaster.MailerId,
                    ChildMailerID = request.MailerRouteMaster.ChildMailerId,
                    Success = true
                });
            }
            catch (Exception ex)
            {
                results.Add(new BatchPlanningResult
                {
                    MailerID = request?.MailerRouteMaster?.MailerId ?? "Unknown",
                    ChildMailerID = request?.MailerRouteMaster?.ChildMailerId ?? "Unknown",
                    Success = false,
                    ErrorMessage = ex.Message
                });
            }
        }

        return results;
    }

    public List<QueuedPlanningItem> DequeuePlanning(int batchSize = 100)
    {
        var items = new List<QueuedPlanningItem>();
        var processedCount = 0;

        while (processedCount < batchSize && _pendingQueue.TryDequeue(out var item))
        {
            if (item.Status == QueueItemStatus.Pending)
            {
                item.Status = QueueItemStatus.Processing;
                item.ProcessingStartedAt = DateTime.UtcNow;
                items.Add(item);
                processedCount++;

                lock (_statsLock)
                {
                    _stats.PendingCount--;
                    _stats.ProcessingCount++;
                }
            }
        }

        return items;
    }

    public void MarkAsProcessed(Guid queueId)
    {
        if (_allItems.TryGetValue(queueId, out var item))
        {
            item.Status = QueueItemStatus.Completed;
            item.CompletedAt = DateTime.UtcNow;

            lock (_statsLock)
            {
                _stats.ProcessingCount--;
                _stats.CompletedCount++;
                _stats.TotalProcessedToday++;
                _stats.LastProcessedAt = DateTime.UtcNow;

                if (item.ProcessingStartedAt.HasValue)
                {
                    var processingTime = item.CompletedAt.Value - item.ProcessingStartedAt.Value;
                    _stats.AverageProcessingTime = TimeSpan.FromMilliseconds(
                        (_stats.AverageProcessingTime.TotalMilliseconds + processingTime.TotalMilliseconds) / 2);
                }
            }
        }
    }

    public void MarkAsFailed(Guid queueId, string errorMessage)
    {
        if (_allItems.TryGetValue(queueId, out var item))
        {
            item.RetryCount++;
            item.ErrorMessage = errorMessage;
            item.FailedAt = DateTime.UtcNow;

            if (item.RetryCount < item.MaxRetries)
            {
                item.Status = QueueItemStatus.RetryScheduled;
                _pendingQueue.Enqueue(item);

                lock (_statsLock)
                {
                    _stats.ProcessingCount--;
                    _stats.PendingCount++;
                }
            }
            else
            {
                item.Status = QueueItemStatus.Failed;

                lock (_statsLock)
                {
                    _stats.ProcessingCount--;
                    _stats.FailedCount++;
                }
            }
        }
    }

    public QueueStatistics GetStatistics()
    {
        lock (_statsLock)
        {
            return new QueueStatistics
            {
                PendingCount = _stats.PendingCount,
                ProcessingCount = _stats.ProcessingCount,
                CompletedCount = _stats.CompletedCount,
                FailedCount = _stats.FailedCount,
                TotalProcessedToday = _stats.TotalProcessedToday,
                LastProcessedAt = _stats.LastProcessedAt,
                AverageProcessingTime = _stats.AverageProcessingTime
            };
        }
    }

    public QueuedPlanningItem? GetPlanningStatus(Guid queueId)
    {
        return _allItems.TryGetValue(queueId, out var item) ? item : null;
    }
}
