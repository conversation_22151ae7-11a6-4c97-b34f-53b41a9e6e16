﻿using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Application.Services.Step2.DailyPlanning;

/// <summary>
/// Service for generating daily plannings from planning templates
/// Runs at midnight (0:00 AM) to create today's plannings
/// </summary>
public interface IDailyPlanningGeneratorService
{
    /// <summary>
    /// Generates real execution plans for the specified date from all active planning templates
    /// </summary>
    /// <param name="executionDate">The date for which to generate plans (default: today)</param>
    /// <param name="companyId">Optional company ID to filter templates (null = all companies)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of plans generated</returns>
    Task<int> GenerateDailyPlansAsync(
        DateOnly? executionDate = null,
        Guid? companyId = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Generates a single real plan from a specific template
    /// </summary>
    /// <param name="templateId">Planning template ID</param>
    /// <param name="executionDate">Execution date</param>
    /// <param name="planCounter">Optional plan counter for batch generation (improves performance)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Generated plan ID</returns>
    Task<List<RouteAggregationSummary>> GeneratePlanFromTemplateAsync(
        Guid templateId,
        DateOnly executionDate,
        int? planCounter = null,
        CancellationToken cancellationToken = default);

    Task GenerateNewDateFromTemplateAsync(
        Guid templateId,
        DateOnly executionDate,
        int? planCounter = null,
        CancellationToken cancellationToken = default);
}
