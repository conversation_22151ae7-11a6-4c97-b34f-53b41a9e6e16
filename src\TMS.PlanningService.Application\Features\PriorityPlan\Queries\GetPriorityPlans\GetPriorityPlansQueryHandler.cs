﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using TMS.PlanningService.Application.Features.PriorityRoute.Queries.GetPriorityPlans;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;
using TMS.SharedKernel.Utilities;
using Entity = TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Queries.GetPriorityPlans;

public class GetPriorityPlansQueryHandler : IRequestHandler<GetPriorityPlansQuery, PagedResult<PriorityPlanDto>>
{
    private readonly IBaseRepository<Entity.Metadata.PriorityPlan> _priorityPlanRepository;
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IExternalDataService _externalDataService;

    public GetPriorityPlansQueryHandler(
        IBaseRepository<Entity.Metadata.PriorityPlan> priorityPlanRepository,
        ICurrentFactorProvider currentFactorProvider,
        IExternalDataService externalDataService)
    {
        _priorityPlanRepository = priorityPlanRepository;
        _currentFactorProvider = currentFactorProvider;
        _externalDataService = externalDataService;
    }

    public async Task<PagedResult<PriorityPlanDto>> Handle(GetPriorityPlansQuery request, CancellationToken cancellationToken)
    {
        var normalizedSearchTerm = (request.ParamRequest.SearchTerm ?? "").RemoveAccents().ToLowerInvariant();
        var sortOptions = GetSortOptions(request.ParamRequest.SortOrder);
        var pagedResult = await _priorityPlanRepository.GetPagedAsync(
            page: request.ParamRequest.Page,
            pageSize: request.ParamRequest.PageSize,
            predicate: x => x.CompanyId == _currentFactorProvider.CompanyId
                       && (string.IsNullOrEmpty(normalizedSearchTerm) || EF.Functions.Like(x.SearchValue, $"%{normalizedSearchTerm}%"))
                       && (request.ParamRequest.IsActive == null || x.IsActive == request.ParamRequest.IsActive)
                       && (request.ParamRequest.CreatedByIds == null || request.ParamRequest.CreatedByIds.Count == 0 || request.ParamRequest.CreatedByIds.Contains(x.CreatedBy))
                       && x.IsDeleted != true,
            selector: x => new PriorityPlanDto
            {
                Id = x.Id,
                PriorityPlanName = x.PriorityPlanName,
                Description = x.Description,
                IsActive = x.IsActive,
                CreatedAt = x.CreatedAt,
                CreatedBy = x.CreatedBy,
                UpdatedAt = x.UpdatedAt,
                UpdatedBy = x.UpdatedBy,
            }
            , sortOptions: sortOptions
            , cancellationToken: cancellationToken);

        var priorityRouteDtos = pagedResult.Items.ToList();

        await _externalDataService.GenericEnrichWithEmployeeDataAsync(priorityRouteDtos);
        return new PagedResult<PriorityPlanDto>(
            priorityRouteDtos,
            pagedResult.TotalCount,
            pagedResult.PageNumber,
            pagedResult.PageSize);
    }

    private IEnumerable<ISortOption<Entity.Metadata.PriorityPlan>> GetSortOptions(SortOrderPriorityPlan? sortEnum)
    {
        var sorts = SortBuilder<Entity.Metadata.PriorityPlan>.Create();

        return sortEnum switch
        {
            SortOrderPriorityPlan.CreatedAtAsc => sorts.ThenBy(v => v.CreatedAt),
            SortOrderPriorityPlan.CreatedAtDesc => sorts.ThenByDescending(v => v.CreatedAt),
            _ => sorts.ThenByDescending(v => v.CreatedAt)
        };
    }
}
