﻿using System.Linq.Expressions;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using TMS.PlanningService.Application.Services.Implements;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.PlanningTemplate;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;
using TMS.SharedKernel.Utilities;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Queries.GetPlanningTemplates;

public class GetPlanningTemplatesQueryHandler : IRequestHandler<GetPlanningTemplatesQuery, PagedResult<PlanningTemplateDto>>
{
    private readonly IBaseRepository<PlanningTemplateEntity> _repository;
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IExternalDataService _externalDataService;

    public GetPlanningTemplatesQueryHandler(
        IBaseRepository<PlanningTemplateEntity> repository,
        ICurrentFactorProvider currentFactorProvider,
        IExternalDataService externalDataService)
    {
        _repository = repository;
        _currentFactorProvider = currentFactorProvider;
        _externalDataService = externalDataService;
    }

    public async Task<PagedResult<PlanningTemplateDto>> Handle(GetPlanningTemplatesQuery request, CancellationToken cancellationToken)
    {
        var predicate = BuildPredicate(request.ParamRequest);
        var sortOptions = GetSortOptions(request.ParamRequest.SortOrder);
        var includeFunc = GetIncludeFunction();

        var pagedResult = await _repository.GetPagedAsync(
            request.ParamRequest.Page,
            request.ParamRequest.PageSize,
            predicate,
            sortOptions,
            includeFunc,
            cancellationToken);

        var planningTemplateDtos = MapToDto(pagedResult.Items);

        await _externalDataService.EnrichWithEmployeeDataAsync(planningTemplateDtos);
        await _externalDataService.EnrichWithRouteDataAsync(planningTemplateDtos);

        return new PagedResult<PlanningTemplateDto>(
            planningTemplateDtos,
            pagedResult.TotalCount,
            pagedResult.PageNumber,
            pagedResult.PageSize);
    }

    private Expression<Func<PlanningTemplateEntity, bool>> BuildPredicate(GetPlanningTemplatesRequest paramRequest)
    {
        var predicate = PredicateBuilder.New<PlanningTemplateEntity>(true);

        predicate = predicate.And(x => !x.IsDeleted);

        if (_currentFactorProvider.CompanyId != Guid.Empty)
            predicate = predicate.And(x => x.CompanyId == _currentFactorProvider.CompanyId);

        if (!string.IsNullOrEmpty(paramRequest.SearchTerm))
        {
            var normalizedSearchTerm = paramRequest.SearchTerm.RemoveAccents().ToLowerInvariant();
            predicate = predicate.And(x => EF.Functions.Like(x.SearchValue, $"%{normalizedSearchTerm}%"));
        }

        if (paramRequest.VehicleTypeIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.VehicleTypeIds.Contains(x.VehicleTypeId));

        if (paramRequest.CreatedByIds?.Any() == true)
            predicate = predicate.And(x => paramRequest.CreatedByIds.Contains(x.CreatedBy));

        if (paramRequest.IsActive.HasValue)
            predicate = predicate.And(x => paramRequest.IsActive == x.IsActive);

        return predicate;
    }
    private static Func<IQueryable<PlanningTemplateEntity>, IQueryable<PlanningTemplateEntity>> GetIncludeFunction()
    {
        return query => query.Include(x => x.Details);
    }
    private static List<PlanningTemplateDto> MapToDto(IEnumerable<PlanningTemplateEntity> entities)
    {
        return entities.Select(entity => new PlanningTemplateDto
        {
            Id = entity.Id,
            CompanyId = entity.CompanyId,
            RouteId = entity.RouteId,
            Code = entity.Code ?? string.Empty,
            Name = entity.Name,
            PriorityNumber = entity.PriorityNumber,
            VehicleTypeId = entity.VehicleTypeId,
            TotalDistance = entity.TotalDistance,
            TotalDuration = entity.TotalDuration,
            StopCount = entity.OfficeCount,
            IsActive = entity.IsActive,
            CreatedBy = entity.CreatedBy,
            CreatedAt = entity.CreatedAt,
            UpdatedBy = entity.UpdatedBy,
            UpdatedAt = entity.UpdatedAt,
            Details = MapDetailToDto(entity.Details)
        }).ToList();
    }
    private static List<PlanningTemplateDetailDto> MapDetailToDto(IEnumerable<PlanningTemplateDetailEntity> details)
    {
        return details.Select(detail => new PlanningTemplateDetailDto
        {
            Id = detail.Id,
            PlanningTemplateId = detail.PlanningTemplateId,
            PostOfficeId = detail.PostOfficeId,
            FromTime = detail.FromTime,
            FromAddDays = detail.FromAddDays,
            ToTime = detail.ToTime,
            ToTimeAddDays = detail.ToTimeAddDays,
            BusinessOperation = detail.BusinessOperation,
            BusinessOperationName = detail.BusinessOperation.GetDescription(),
            DistanceBetweenPoints = detail.DistanceBetweenPoints,
            StepNumber = detail.StepNumber
        }).ToList();
    }
    private IEnumerable<ISortOption<PlanningTemplateEntity>> GetSortOptions(SortOrderBase? sortEnum)
    {
        var sorts = SortBuilder<PlanningTemplateEntity>.Create();

        return sortEnum switch
        {
            SortOrderBase.CodeAsc => sorts.ThenBy(v => v.Code),
            SortOrderBase.CodeDesc => sorts.ThenByDescending(v => v.Code),
            SortOrderBase.PriorityNumberAsc => sorts.ThenBy(v => v.PriorityNumber),
            SortOrderBase.PriorityNumberDesc => sorts.ThenByDescending(v => v.PriorityNumber),
            SortOrderBase.CreatedAtAsc => sorts.ThenBy(v => v.CreatedAt),
            SortOrderBase.CreatedAtDesc => sorts.ThenByDescending(v => v.CreatedAt),
            _ => sorts.ThenByDescending(v => v.CreatedAt)
        };
    }
}
