{
  "ConnectionStrings": {
    "PlanningDBConnection": "Host=localhost;Database=PlanningDB;Username=********;Password=********"
  },
  "Cors": {
    "AllowedOrigins": [
      "*"
    ],
    "AllowedMethods": [
      "GET",
      "POST",
      "PUT",
      "DELETE",
      "PATCH"
    ],
    "AllowedHeaders": [
      "*"
    ],
    "AllowCredentials": true
  },
  "DefaultKey": "<set>",
  "Webhook": {
    "Key": "<set>"
  },
  "DriverService": {
    "Url": "<set>"
  },
  "RouteService": {
    "Url": "<set>"
  },
  "FleetService": {
    "Url": "<set>"
  },
  "FileService": {
    "Url": "<set>"
  },
  "CostService": {
    "Url": "<set>"
  },
  "OrderService": {
    "Url": "<set>"
  },
  "PmsPlanningPull": {
    "Key": "<set>",
    "Url": "<set>",
    "SyncCronTime": "0 0 */2 * * ?"
  },
  "Maintenance": {
    "Time": "0 */2 * * * ?"
  },
  "Identity": {
    "Issuer": "247-Eco-System",
    "Audience": "tms-api",
    "Key": "<set>"
  },
  "Serilog": {
    "Using": [
      "Serilog.Sinks.Console"
    ],
    "MinimumLevel": {
      "Default": "Information",
      "Override": {
        "Microsoft": "Information",
        "System": "Information",
        "Microsoft.EntityFrameworkCore.Database.Command": "Information"
      }
    },
    "WriteTo": [
      {
        "Name": "Async",
        "Args": {
          "configure": [
            {
              "Name": "Console",
              "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"
            }
          ]
        }
      }
    ]
  },
  "environment": "development",
  "OpenTelemetry": {
    "OtlpEndpoint": "<set>", // protobuf
    "Key": "<set>"
  },
  "KafkaFlow": {
    "BootstrapServers": "localhost:9092"
  },
  "Redis": {
    "ConnectionString": "localhost:6379",
    "Password": "your_secure_password",
    "Database": 2,
    "ConnectTimeout": 10000,
    "SyncTimeout": 5000,
    "AsyncTimeout": 5000,
    "ConnectRetry": 5,
    "AbortOnConnectFail": false
  },
  "RabbitMq": {
    "HostName": "<set>",
    "Port": 5672,
    "UserName": "<set>",
    "Password": "<set>",
    "Exchanges": [
      {
        "Name": "pms_events",
        "Type": "direct",
        "Queues": {
          "plannings": {
            "QueueName": "plannings_queue",
            "RoutingKey": "event.planning",
            "PrefetchCount": 10
          }
        }
      },
      {
        "Name": "order_events",
        "Type": "topic",
        "Queues": {
          "reqorders": {
            "QueueName": "reqorders_queue",
            "RoutingKey": "event.reqorder.*",
            "PrefetchCount": 5
          },
          "deleteorders": {
            "QueueName": "deleteorders_queue",
            "RoutingKey": "event.deleteorder.*",
            "PrefetchCount": 5
          }
        }
      }
    ]
  }
}