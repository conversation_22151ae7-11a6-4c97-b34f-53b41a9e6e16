﻿using MediatR;
using Microsoft.Extensions.Logging;
using Quartz;
using TMS.PlanningService.Application.Features.Planning.Commands.ReceivePlanning;
using TMS.PlanningService.Application.Services;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Application.Jobs;

[DisallowConcurrentExecution]
public class PlanningQueueProcessingJob : IJob
{
    private readonly IPlanningQueueService _queueService;
    private readonly IMediator _mediator;
    private readonly ILogger<PlanningQueueProcessingJob> _logger;

    // Process planning data in batches to handle high volume efficiently
    // Increased batch size for better throughput when handling multiple planning records
    private const int BatchSize = 500;
    private const int MaxProcessingTimeMinutes = 10;

    public PlanningQueueProcessingJob(
        IPlanningQueueService queueService,
        IMediator mediator,
        ILogger<PlanningQueueProcessingJob> logger)
    {
        _queueService = queueService;
        _mediator = mediator;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var jobId = context.FireInstanceId;
        var startTime = DateTime.UtcNow;

        _logger.LogInformation("Starting in-memory planning processing job - Job Id: {JobId}", jobId);

        try
        {
            var totalProcessed = 0;
            var totalFailed = 0;
            var batchNumber = 1;

            // Log queue statistics before processing
            var initialStats = _queueService.GetStatistics();
            _logger.LogInformation("Queue Stats - Pending: {Pending}, Processing: {Processing}, Completed Today: {Completed}",
                initialStats.PendingCount, initialStats.ProcessingCount, initialStats.TotalProcessedToday);

            while (DateTime.UtcNow.Subtract(startTime).TotalMinutes < MaxProcessingTimeMinutes)
            {
                // Dequeue planning data from memory - this is very fast
                var pendingPlanning = _queueService.DequeuePlanning(BatchSize);

                if (!pendingPlanning.Any())
                {
                    _logger.LogInformation("No pending planning data found in memory queue. Job completed - Job Id: {JobId}", jobId);
                    break;
                }

                _logger.LogInformation("Processing batch {BatchNumber} with {PlanningCount} planning records from memory - Job Id: {JobId}",
                    batchNumber, pendingPlanning.Count, jobId);

                var batchProcessed = 0;
                var batchFailed = 0;

                foreach (var queuedPlanning in pendingPlanning)
                {
                    if (context.CancellationToken.IsCancellationRequested)
                    {
                        _logger.LogWarning("Job cancellation requested - Job Id: {JobId}", jobId);
                        break;
                    }

                    await ProcessSinglePlanningFromMemory(queuedPlanning);

                    if (queuedPlanning.Status == QueueItemStatus.Completed)
                        batchProcessed++;
                    else if (queuedPlanning.Status == QueueItemStatus.Failed)
                        batchFailed++;
                }

                totalProcessed += batchProcessed;
                totalFailed += batchFailed;
                batchNumber++;

                _logger.LogInformation("Completed batch {BatchNumber} - Processed: {Processed}, Failed: {Failed} - Job Id: {JobId}",
                    batchNumber - 1, batchProcessed, batchFailed, jobId);

                // Minimal delay between batches for maximum throughput
                await Task.Delay(10, context.CancellationToken); // Ultra-low delay for high-volume processing
            }

            var duration = DateTime.UtcNow.Subtract(startTime);
            var finalStats = _queueService.GetStatistics();

            _logger.LogInformation("In-memory planning processing job completed - Job Id: {JobId}, Duration: {Duration}, " +
                "Total Processed: {TotalProcessed}, Total Failed: {TotalFailed}, Queue Pending: {QueuePending}",
                jobId, duration, totalProcessed, totalFailed, finalStats.PendingCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in in-memory planning processing job - Job Id: {JobId}", jobId);
            throw;
        }
    }

    private async Task ProcessSinglePlanningFromMemory(QueuedPlanningItem queuedPlanning)
    {
        try
        {
            _logger.LogWarning("Processing queued planning from memory - Queue Id: {QueueId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                queuedPlanning.Id, queuedPlanning.PlanningRequest.MailerRouteMaster.MailerId, queuedPlanning.PlanningRequest.MailerRouteMaster.ChildMailerId);

            // Create the receive planning command
            var command = new ReceivePlanningCommand(
                queuedPlanning.PlanningRequest.MailerRouteMaster,
                queuedPlanning.PlanningRequest.MailerPlanRoutes,
                queuedPlanning.PlanningRequest.MailerAdjustRoutes,
                queuedPlanning.PlanningRequest.MailerActualRoutes
            );

            // Process the planning data
            var mailerRouteMaster = await _mediator.Send(command);

            // Mark as completed in the queue service
            _queueService.MarkAsProcessed(queuedPlanning.Id);

            _logger.LogWarning("Successfully processed queued planning from memory - Queue Id: {QueueId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                queuedPlanning.Id, queuedPlanning.PlanningRequest.MailerRouteMaster.MailerId, queuedPlanning.PlanningRequest.MailerRouteMaster.ChildMailerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process queued planning from memory - Queue Id: {QueueId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                queuedPlanning.Id, queuedPlanning.PlanningRequest.MailerRouteMaster.MailerId, queuedPlanning.PlanningRequest.MailerRouteMaster.ChildMailerId);

            // Mark as failed in the queue service (handles retry logic)
            _queueService.MarkAsFailed(queuedPlanning.Id, ex.Message);
        }
    }
}
