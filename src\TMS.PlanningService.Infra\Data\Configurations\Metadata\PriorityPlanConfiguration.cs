﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;
namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class PriorityPlanConfiguration : IEntityTypeConfiguration<PriorityPlan>
{
    public void Configure(EntityTypeBuilder<PriorityPlan> builder)
    {
        builder.ToTable("priority_plan");

        builder.<PERSON><PERSON>ey(o => new { o.Id });

        builder.Property(x => x.Id)
               .HasColumnName("id");

        builder.Property(x => x.CompanyId)
            .HasColumnName("company_id")
            .IsRequired();

        builder.Property(o => o.PriorityPlanName)
            .IsRequired()
            .HasMaxLength(50)
            .HasColumnName("priority_plan_name");

        builder.Property(o => o.Description)
            .HasMaxLength(250)
            .HasColumnName("description");

        builder.Property(x => x.IsActive)
            .HasColumnName("is_active");

        builder.Property(x => x.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(x => x.CreatedBy)
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(x => x.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        builder.Property(x => x.UpdatedBy)
            .HasColumnName("updated_by")
            .IsRequired();

        builder.Property(x => x.IsDeleted)
            .HasColumnName("is_deleted")
            .HasDefaultValue(false)
            .IsRequired();

        //builder.HasMany(x => x.GroupProperties)
        //       .WithOne()
        //       .HasForeignKey(p => p.PriorityPlanId)
        //       .OnDelete(DeleteBehavior.Cascade);
    }
}
