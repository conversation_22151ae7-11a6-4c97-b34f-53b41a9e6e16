﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class LeadTimePartnerConfiguration : IEntityTypeConfiguration<LeadTimePartner>
{
    public void Configure(EntityTypeBuilder<LeadTimePartner> builder)
    {
        // Table
        builder.ToTable("lead_time_partner");

        // Primary Key
        builder.HasKey(o => new { o.Id });

        builder.Property(x => x.Id)
               .HasColumnName("id");

        builder.Property(x => x.LeadTimeId)
            .HasColumnName("lead_time_id")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.PartnerId)
            .HasColumnName("partner_id")
            .IsRequired();

        builder.Property(e => e.FromTime)
               .HasColumnName("from_time");

        builder.Property(e => e.ToTime)
               .HasColumnName("to_time");

        builder.Property(e => e.FromAddDays)
               .HasColumnName("from_add_days");

        builder.Property(e => e.ToAddDays)
               .HasColumnName("to_add_days");

        builder.Property(e => e.SenderPostOffice)
               .HasColumnName("sender_post_office");

        builder.Property(e => e.ReceivePostOffice)
               .HasColumnName("receive_post_office");

        builder.Property(e => e.Description)
               .HasColumnName("description");
    }
}
