﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.RouteAggregation.Queries.GetRouteAggregationByKey;
using TMS.PlanningService.Application.Features.RouteAggregation.Queries.GetRouteAggregations;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/normal-planning-aggregates")]
[Produces("application/json")]
public class PlanningAggregateController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<PlanningAggregateController> _logger;

    public PlanningAggregateController(
        IMediator mediator,
        ILogger<PlanningAggregateController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all planning aggregations with optional filters and pagination
    /// Returns real-time aggregated planning data enriched with order metrics from Redis
    /// </summary>
    /// <param name="request">Filter and pagination parameters</param>
    /// <returns>Paginated list of planning aggregations</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(PagedResult<RouteAggregationDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResult<RouteAggregationDto>>> GetPlanningAggregates(
        [FromBody] GetRouteAggregationsRequest request)
    {
        _logger.LogInformation(
            "Getting planning aggregates - Page: {Page}, PageSize: {PageSize}",
            request.Page,
            request.PageSize);

        var query = new GetRouteAggregationsQuery(request);
        var result = await _mediator.Send(query);

        return Ok(result);
    }

    /// <summary>
    /// Retrieves planning aggregation details for a specific route key.
    /// </summary>
    /// <remarks>
    /// This endpoint fetches route aggregation and associated order details.
    /// If no data is found, it returns HTTP 404 (Not Found).
    /// </remarks> 
    [HttpPost("route-aggregation-query")]
    [ProducesResponseType(typeof(RouteAggregationDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<RouteAggregationDto>> GetPlanningAggregateByRoute(
        [FromBody] GetRouteAggregationByKeyRequest request)
    {
        // Step 1: Log request metadata
        _logger.LogInformation(
            "[GetPlanningAggregateByRoute] Start fetching route aggregation - RouteKey: {RouteKey}, Page: {Page}, PageSize: {PageSize}, SearchTerm: {SearchTerm}",
            request.RouteKey,
            request.Page,
            request.PageSize,
            request.SearchTerm);
 
            // Step 2: Create and send query to mediator
            var query = new GetRouteAggregationByKeyQuery(request);
            var result = await _mediator.Send(query);

            // Step 3: Handle not found case
            if (result == null) 
                return NotFound(new { message = $"No planning aggregation found for route {request.RouteKey}" }); 
            return Ok(result);
         
    }
}
