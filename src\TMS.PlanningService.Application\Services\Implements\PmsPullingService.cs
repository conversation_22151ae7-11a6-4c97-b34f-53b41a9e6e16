﻿using MapsterMapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.IRepository;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using static TMS.PlanningService.Application.Jobs.PlanningPullingProcessingJob;
using Entity = TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Application.Services.Implements;

public class PmsPullingService : IPmsPullingService
{
    private readonly IPlanningServiceApi _planningServiceApi;
    private readonly ILogger<PmsPullingService> _logger;
    private readonly IServiceTypeRepository _serviceTypeRepository;
    private readonly IBaseRepository<Entity.Metadata.ExtraService> _extraServiceRepository;
    private readonly IBaseRepository<OrderStatus> _orderStatusRepository;
    private readonly IMetadataCacheService _metadataCacheService;
    private readonly IBaseRepository<LeadTime> _leadTimeRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMapper _mapper;

    public PmsPullingService(
        IPlanningServiceApi planningServiceApi,
        ILogger<PmsPullingService> logger,
        IServiceTypeRepository serviceTypeRepository,
        IBaseRepository<Entity.Metadata.ExtraService> extraServiceRepository,
        IBaseRepository<OrderStatus> orderStatusRepository,
        IMetadataCacheService metadataCacheService,
        IBaseRepository<LeadTime> leadTimeRepository,
        IUnitOfWork unitOfWork,
        IMapper mapper)
    {
        _planningServiceApi = planningServiceApi;
        _logger = logger;
        _serviceTypeRepository = serviceTypeRepository;
        _extraServiceRepository = extraServiceRepository;
        _orderStatusRepository = orderStatusRepository;
        _metadataCacheService = metadataCacheService;
        _leadTimeRepository = leadTimeRepository;
        _unitOfWork = unitOfWork;
        _mapper = mapper;
    }

    public async Task<int> SyncServiceTypeAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting service types synchronization from PMS API");

            var response = await _planningServiceApi.GetServiceTypeAsync();
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No service types returned from PMS API");
                return 0;
            }

            var serviceTypes = response.Data.Select(d => new Entity.Metadata.ServiceType
            {
                ServiceTypeId = d.ServiceTypeId,
                ServiceTypeName = d.ServiceTypeName,
                IsActive = d.IsActive,
                ServiceTypeStatus = d.ServiceTypeStatus
            }).OrderBy(x => x.ServiceTypeId).ToList();

            // Batch upsert for better performance (single DB roundtrip)
            await _serviceTypeRepository.UpsertRangeAsync(
                serviceTypes,
                keyColumns: new System.Linq.Expressions.Expression<Func<Entity.Metadata.ServiceType, object>>[]
                {
                    e => e.ServiceTypeId
                },
                updateColumns: null,
                cancellationToken: cancellationToken
            );

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Cache active service types to Redis
            var activeServiceTypes = serviceTypes.Where(st => st.IsActive == true).ToList();
            await _metadataCacheService.SetServiceTypesAsync(activeServiceTypes);

            _logger.LogInformation("Successfully synchronized {Count} service types", serviceTypes.Count);

            return serviceTypes.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing service types from PMS API");
            throw;
        }
    }

    public async Task<int> SyncExtraServiceAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting extra-service synchronization from PMS API");

            var response = await _planningServiceApi.GetAllExtraServiceAsync();
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No extra-service returned from PMS API");
                return 0;
            }

            var extraServices = response.Data.Select(ex => new Entity.Metadata.ExtraService
            {
                ServiceId = ex.ServiceId,
                ServiceName = ex.ServiceName,
                IsActive = ex.IsActive,
            }).OrderBy(x => x.ServiceId).ToList();

            // Batch upsert for better performance (single DB roundtrip)
            await _extraServiceRepository.UpsertRangeAsync(
                extraServices,
                keyColumns: new System.Linq.Expressions.Expression<Func<Entity.Metadata.ExtraService, object>>[]
                {
                    e => e.ServiceId
                },
                updateColumns: null,
                cancellationToken: cancellationToken
            );

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Cache active extra services to Redis
            var activeExtraServices = extraServices.Where(st => st.IsActive == true).ToList();
            await _metadataCacheService.SetExtraServicesAsync(activeExtraServices);

            _logger.LogInformation("Successfully synchronized {Count} extra-service", extraServices.Count);

            return extraServices.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing extra-service from PMS API");
            throw;
        }
    }

    public async Task<int> SyncOrderStatusAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Starting order status synchronization from PMS API");

            var response = await _planningServiceApi.GetOrderStatusAsync();
            if (response?.Data == null || !response.Data.Any())
            {
                _logger.LogWarning("No order status returned from PMS API");
                return 0;
            }

            var orderStatuses = response.Data.Select(os => new Entity.Metadata.OrderStatus
            {
                StatusId = os.StatusId,
                StatusName = os.StatusName,
                StatusNameTracking = os.StatusNameTracking,
                StatusGroupId = os.StatusGroupId,
                IsDataForMCC = os.IsDataForMCC
            }).OrderBy(x => x.StatusId).ToList();

            // Batch upsert for better performance (single DB roundtrip)
            await _orderStatusRepository.UpsertRangeAsync(
                orderStatuses,
                keyColumns: new System.Linq.Expressions.Expression<Func<OrderStatus, object>>[]
                {
                    e => e.StatusId
                },
                updateColumns: null,
                cancellationToken: cancellationToken
            );

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            // Cache all order statuses to Redis
            await _metadataCacheService.SetOrderStatusesAsync(orderStatuses);

            _logger.LogInformation("Successfully synchronized {Count} order status", orderStatuses.Count);

            return orderStatuses.Count;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error synchronizing order status from PMS API");
            throw;
        }
    }

    public async Task<SyncStats> SynLeadTimeAsync(CancellationToken cancellationToken = default)
    {
        var stats = new SyncStats();
        try
        {
            _logger.LogInformation("Starting lead time synchronization from PMS API");

            var data = await _planningServiceApi.FetchDataLeadTimeAsync(cancellationToken);
            if (data is null || !data.Data.Any())
            {
                _logger.LogInformation("No lead time returned from PMS API");
                return stats;
            }

            stats.TotalProcessed = data.TotalItems;

            var leadTimeIds = data.Data.Select(x => x.LeadTimeId).ToList();

            // Lightweight query: Only SELECT IDs of existing entities (not full entities)
            // This is much faster than loading full entities with ToListAsync
            var existingIds = await _leadTimeRepository
                .GetQueryable()
                .Where(l => leadTimeIds.Contains(l.LeadTimeId))
                .Select(l => l.LeadTimeId)
                .ToHashSetAsync(cancellationToken);

            // Map all DTOs to entities
            var entities = data.Data.Select(dto => _mapper.Map<LeadTime>(dto)).ToList();

            // Calculate stats BEFORE upsert (based on existing IDs)
            stats.TotalInserted = entities.Count(e => !existingIds.Contains(e.LeadTimeId));
            stats.TotalUpdated = entities.Count(e => existingIds.Contains(e.LeadTimeId));

            // Batch upsert - inserts new and updates existing in single operation
            // This is 2-3× faster than the previous approach
            var affectedRows = await _leadTimeRepository.UpsertRangeAsync(
                entities,
                keyColumns: new System.Linq.Expressions.Expression<Func<LeadTime, object>>[]
                {
                    e => e.LeadTimeId
                },
                updateColumns: null, // Update all non-key columns
                cancellationToken: cancellationToken
            );

            await _unitOfWork.SaveChangesAsync(cancellationToken);

            _logger.LogInformation(
                "Lead time sync completed - Processed: {Processed}, Inserted: {Inserted}, Updated: {Updated} ({AffectedRows} rows affected)",
                stats.TotalProcessed,
                stats.TotalInserted,
                stats.TotalUpdated,
                affectedRows);

            return stats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to sync lead time data");
            throw;
        }
    }
}
