﻿using System.ComponentModel.Design;
using MediatR;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Constants.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;
using Entities = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Features.LeadTimePartner.UpdateLeadTimePartner;

public class UpdateLeadTimePartnerCommandHandler : IRequestHandler<UpdateLeadTimePartnerCommand, Guid>
{
    private readonly IBaseRepository<Entities.LeadTimePartner> _leadTimePartnerRepository;
    private readonly IUnitOfWork _unitOfWork;
    public UpdateLeadTimePartnerCommandHandler(
        IBaseRepository<Entities.LeadTimePartner> leadTimePartnerRepository,
        IUnitOfWork unitOfWork)
    {
        _leadTimePartnerRepository = leadTimePartnerRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Guid> Handle(UpdateLeadTimePartnerCommand request, CancellationToken cancellationToken)
    {
        var updateRequest = request.ParamRequest;
        //Check if entity exists
        var entity = await _leadTimePartnerRepository.GetByIdAsync(updateRequest.Id, cancellationToken);
        if (entity == null)
            throw new BusinessRuleValidationException("LeadTimePartner", MessageFormatter.FormatNotFound("LeadTimePartner", updateRequest.Id));

        // Update main entity
        entity.PartnerId = updateRequest.PartnerId;
        entity.FromTime = updateRequest.FromTime;
        entity.ToTime = updateRequest.ToTime;
        entity.FromAddDays = updateRequest.FromAddDays;
        entity.ToAddDays = updateRequest.ToAddDays;
        entity.SenderPostOffice = updateRequest.SenderPostOffice;
        entity.ReceivePostOffice = updateRequest.ReceivePostOffice;
        entity.Description = updateRequest.Description;
        _leadTimePartnerRepository.Update(entity);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return entity.Id;
    }
}
