﻿using MediatR;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Common.Queries;

/// <summary>
/// Specialized base class for route aggregation query handlers
/// Extends HybridPaginationQueryHandlerBase with route-specific helper methods
/// Eliminates code duplication across GetRouteAggregationsQueryHandler and GetPriorityRouteAggregationsQueryHandler
/// </summary>
/// <typeparam name="TRequest">MediatR request type (query)</typeparam>
/// <typeparam name="TDto">DTO returned to client</typeparam>
public abstract class RouteAggregationQueryHandlerBase<TRequest, TDto>
    : HybridPaginationQueryHandlerBase<TRequest, TDto, RouteAggregationSummary, RouteAggregationEntity>
    where TRequest : IRequest<PagedResult<TDto>>
{
    /// <summary>
    /// Route service API for fetching post office data
    /// </summary>
    protected abstract IRouteServiceApi RouteServiceApi { get; }

    /// <summary>
    /// Metadata cache service for fetching cached post office data
    /// </summary>
    protected abstract TMS.SharedKernal.Caching.IMetadataCacheService MetadataCacheService { get; }

    // ============================================================================
    // COMMON HELPER METHODS (shared across all route aggregation handlers)
    // ============================================================================

    /// <summary>
    /// Parses JSON breakdown strings from database
    /// Common helper used by MapEntityToSummary
    /// </summary>
    protected List<OptionsCountDto> ParseJsonBreakdown(string? json)
    {
        if (string.IsNullOrEmpty(json))
        {
            return new List<OptionsCountDto>();
        }

        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<OptionsCountDto>>(json)
                ?? new List<OptionsCountDto>();
        }
        catch
        {
            return new List<OptionsCountDto>();
        }
    }

    /// <summary>
    /// Fetches post office data for the given aggregations
    /// First attempts to retrieve from cache, then falls back to API if needed
    /// Common helper used by MapToDtoAsync implementations
    /// </summary>
    protected async Task<List<PostOfficeDto>> FetchPostOfficeDataAsync(
        List<RouteAggregationSummary> aggregations)
    {
        try
        {
            var postOfficeCodes = aggregations
                .SelectMany(a => new[] { a.FromOfficeId, a.ToOfficeId })
                .Where(x => !string.IsNullOrEmpty(x))
                .Distinct()
                .ToList();

            if (!postOfficeCodes.Any())
            {
                return new List<PostOfficeDto>();
            }

            // First, try to get post offices from cache
            try
            {
                var cachedPostOffices = await MetadataCacheService.GetPostOfficesAsync<PostOfficeDto>(postOfficeCodes);

                if (cachedPostOffices != null && cachedPostOffices.Any())
                {
                    Logger.LogInformation("Retrieved {Count} post offices from cache", cachedPostOffices.Count);

                    // Check if all required codes are in cache
                    var cachedCodes = cachedPostOffices.Keys.ToHashSet();
                    var missingCodes = postOfficeCodes.Where(code => !cachedCodes.Contains(code)).ToList();

                    if (!missingCodes.Any())
                    {
                        // All post offices found in cache
                        return cachedPostOffices.Values.ToList();
                    }

                    // Some codes are missing, fetch from API and merge
                    Logger.LogInformation("Found {MissingCount} post offices not in cache, fetching from API", missingCodes.Count);
                    var missingPostOffices = await RouteServiceApi.GetPostOfficesByCodesAsync(missingCodes);

                    // Merge cached and API results
                    var allPostOffices = cachedPostOffices.Values.ToList();
                    allPostOffices.AddRange(missingPostOffices);

                    return allPostOffices;
                }
            }
            catch (Exception cacheEx)
            {
                Logger.LogWarning(cacheEx, "Error fetching post office data from cache, falling back to API");
            }

            // Fallback: Get all post offices from API if cache failed or was empty
            Logger.LogInformation("Fetching all {Count} post offices from API", postOfficeCodes.Count);
            var postOffices = await RouteServiceApi.GetPostOfficesByCodesAsync(postOfficeCodes);
            return postOffices.ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error fetching post office data from RouteServiceApi");
            return new List<PostOfficeDto>();
        }
    }

    /// <summary>
    /// Maps RouteAggregationEntity (from PostgreSQL) to RouteAggregationSummary (in-memory)
    /// Common helper used by FetchFromDatabaseAsync implementations
    /// </summary>
    protected RouteAggregationSummary MapEntityToSummary(RouteAggregationEntity entity)
    {
        return new RouteAggregationSummary
        {
            RouteKey = entity.RouteKey,
            FromOfficeId = entity.FromOfficeId,
            ToOfficeId = entity.ToOfficeId,
            FromTime = entity.FromTime,
            ToTime = entity.ToTime,
            ActualFromTime = entity.ActualFromTime,
            ActualToTime = entity.ActualToTime,
            TotalDurationMinutes = entity.TotalDurationMinutes,
            AverageDurationMinutes = entity.AverageDurationMinutes,
            EarliestStartTime = entity.EarliestStartTime,
            LatestEndTime = entity.LatestEndTime,
            TotalOrders = entity.TotalOrders,
            TotalItems = entity.TotalItems,
            TotalWeight = entity.TotalWeight,
            TotalRealWeight = entity.TotalRealWeight,
            TotalCalWeight = entity.TotalCalWeight,
            TotalDiffWeight = entity.TotalDiffWeight,
            PriorityScore = entity.PriorityScore,
            NeedsOptimization = entity.NeedsOptimization,
            TransportProviderBreakdown = ParseJsonBreakdown(entity.TransportProviderBreakdownJson),
            VehicleTypeBreakdown = ParseJsonBreakdown(entity.VehicleTypeBreakdownJson),
            TransportMethodBreakdown = ParseJsonBreakdown(entity.TransportMethodBreakdownJson),
            CreatedAt = entity.CreatedAt,        // Original creation time from database
            AggregatedAt = entity.AggregatedAt,
            LastPersistedAt = entity.SnapshotAt,
            PriorityPlanName = null,      // handle later by specific inherit class
            PriorityPlanId = entity.PriorityPlanId,        // handle later by specific inherit class
            DailyPlanningId = entity.DailyPlanningId,       // handle later by specific inherit class
            OrderDetails = new Dictionary<string, OrderOnRoute>()  // Historical data doesn't include detailed order info
        };
    }

    /// <summary>
    /// Provides unique key for deduplication (RouteKey for all route aggregation handlers)
    /// </summary>
    protected override string GetUniqueKey(RouteAggregationSummary summary)
    {
        return summary.RouteKey;
    }
}
