﻿using MediatR;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Application.Features.Planning.Commands.ReceivePlanning;

public record ReceivePlanningCommand(
    MailerRouteMaster MailerRouteMaster,
    List<MailerPlanRoute> MailerPlanRoutes,
    List<MailerAdjustRoute> MailerAdjustRoutes,
    List<MailerActualRoute> MailerActualRoutes
) : IRequest<MailerRouteMasterEntity>;
