﻿using MediatR;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Provinces;
using TMS.PlanningService.Contracts.Routes;
using TMS.PlanningService.Contracts.Wards;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;
using Entities = TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Queries.GetPriorityPlanById;

public class GetPriorityPlanByIdQueryHandler : IRequestHandler<GetPriorityPlanByIdQuery, PriorityPlanDto?>
{
    private readonly IBaseRepository<Entities.Metadata.PriorityPlan> _priorityPlanRepository;
    private readonly IBaseRepository<PriorityPlanGroup> _priorityPlanGroupProperty;
    private readonly IBaseRepository<PriorityPlanGroupAttr> _priorityPlanGroupAttr;
    private readonly IExternalDataService _externalDataService;
    private readonly IBaseRepository<ServiceType> _serviceTypeRepository;
    private readonly IBaseRepository<Entities.Metadata.ExtraService> _extraServiceRepository;
    private readonly IRouteServiceApi _routeServiceApi;

    public GetPriorityPlanByIdQueryHandler(
        IBaseRepository<Entities.Metadata.PriorityPlan> priorityPlanRepository,
        IBaseRepository<PriorityPlanGroup> priorityGroupProperty,
        IBaseRepository<PriorityPlanGroupAttr> priorityProperty,
        IExternalDataService externalDataService,
        IBaseRepository<ServiceType> serviceTypeRepository,
        IBaseRepository<Entities.Metadata.ExtraService> extraServiceRepository,
        IRouteServiceApi routeServiceApi)
    {
        _priorityPlanRepository = priorityPlanRepository;
        _priorityPlanGroupProperty = priorityGroupProperty;
        _priorityPlanGroupAttr = priorityProperty;
        _externalDataService = externalDataService;
        _serviceTypeRepository = serviceTypeRepository;
        _extraServiceRepository = extraServiceRepository;
        _routeServiceApi = routeServiceApi;
    }

    public async Task<PriorityPlanDto?> Handle(GetPriorityPlanByIdQuery request, CancellationToken cancellationToken)
    {
        var data = await _priorityPlanRepository.GetByIdAsync(request.Id);
        if (data == null)
            return null;

        var groupProperties = await _priorityPlanGroupProperty.FindWithIncludeAsync(predicate: x => x.PriorityPlanId == request.Id, includes: x => x.PriorityAttributes);
        //groupProperties = groupProperties.OrderBy(x => x.StepNumber).ThenBy(x => x.PriorityAttributes.OrderBy(x => x.StepNumber)).ToList();
        var dto = new PriorityPlanDto
        {
            Id = data.Id,
            PriorityPlanName = data.PriorityPlanName,
            Description = data.Description,
            IsActive = data.IsActive,
            CreatedAt = data.CreatedAt,
            UpdatedAt = data.UpdatedAt,
            CreatedBy = data.CreatedBy,
            UpdatedBy = data.UpdatedBy,
            PriorityPlanGroups = groupProperties
            .OrderBy(x => x.StepNumber)
            .Select(gp => new PriorityPlanGroupDto
            {
                Id = gp.Id,
                PriorityPlanId = data.Id,
                PriorityAttributes = gp.PriorityAttributes
                .OrderBy(pp => pp.StepNumber)
                .Select(pp => new PriorityPlanGroupAttrDto
                {
                    Id = pp.Id,
                    PriorityPlanGroupId = pp.PriorityPlanGroupId,
                    PropertyType = pp.PropertyType,
                    LocationType = pp.LocationType,
                    PropertyOperator = pp.PropertyOperator,
                    Values = pp.Values,
                    LogicOperator = pp.LogicOperator
                }).ToList()
            }).ToList()
        };

        //Nhóm các attribute theo PropertyType và LocationType để lấy dữ liệu chi tiết
        var groupedAttributes = dto.PriorityPlanGroups
            .SelectMany(g => g.PriorityAttributes)
            .GroupBy(a => new { a.PropertyType, a.LocationType })
            .Select(g => new
            {
                PropertyType = g.Key.PropertyType,
                LocationType = g.Key.LocationType,
                Values = g.SelectMany(a => (a.Values ?? "").Split(',', StringSplitOptions.RemoveEmptyEntries).Select(v => v.Trim()))
                          .Distinct()
                          .ToList()
            }).ToList();


        // Tạo danh sách cho từng loại
        var serviceTypeIds = new List<string>();
        var extraServiceIds = new List<string>();
        var wardIds = new List<string>();
        var provinceIds = new List<string>();
        var postOfficesIds = new List<string>();

        foreach (var group in groupedAttributes)
        {
            switch (group.PropertyType)
            {
                case PriorityType.BasicService:
                    serviceTypeIds.AddRange(group.Values);
                    break;

                case PriorityType.ExtraService:
                    extraServiceIds.AddRange(group.Values);
                    break;

                case PriorityType.Origin:
                case PriorityType.Destination:
                    if (group.LocationType == LocationType.Ward)
                        wardIds.AddRange(group.Values);
                    else if (group.LocationType == LocationType.Province)
                        provinceIds.AddRange(group.Values);
                    else if (group.LocationType == LocationType.PostOffice)
                        postOfficesIds.AddRange(group.Values);
                    break;
            }
        }

        //Lấy dữ liệu mapping
        var serviceTypes = serviceTypeIds.Any() ? await _serviceTypeRepository.FindAsync(x => serviceTypeIds.Contains(x.ServiceTypeId!), cancellationToken) 
                                                : new List<ServiceType>();
        var extraServices = extraServiceIds.Any() ? await _extraServiceRepository.FindAsync(x => extraServiceIds.Contains(x.ServiceId!), cancellationToken)
                                                  : new List<Entities.Metadata.ExtraService>();

        var wards = wardIds.Any() ? await _routeServiceApi.GetWardsByIds(new IdsRequest { Ids = wardIds }) : new List<WardDto>();
        var provinces = provinceIds.Any() ? await _routeServiceApi.GetProvincesByIds(new IdsRequest { Ids = provinceIds }) : new List<ProvinceDto>();
        var postOffices = postOfficesIds.Any() ? await _routeServiceApi.GetPostOfficesByCodesAsync(postOfficesIds) 
                                              : new List<PostOfficeDto>();

        //Convert về dictionary để dễ truy xuất
        var dicts = new
        {
            Service = serviceTypes.ToDictionary(x => x.ServiceTypeId ?? "", x => x),
            Extra = extraServices.ToDictionary(x => x.ServiceId ?? "", x => x),
            Ward = wards.ToDictionary(x => x.WardId, x => x),
            Province = provinces.ToDictionary(x => x.ProvinceId, x => x),
            PostOffice = postOffices.ToDictionary(x => x.PostOfficeCode, x => x)
        };

        foreach (var group in dto.PriorityPlanGroups)
        {
            foreach (var attr in group.PriorityAttributes)
            {
                var valueIds = (attr.Values ?? "").Split(',', StringSplitOptions.RemoveEmptyEntries)
                                .Select(v => v.Trim())
                                .ToList();

                if (valueIds.Count == 0)
                {
                    attr.ValueArray = new List<AttributeValuesDto>();
                    continue;
                }

                attr.ValueArray = BuildAttributeValues(attr, valueIds, dicts);
            }
        }

        var listDto = new List<PriorityPlanDto> { dto };
        await _externalDataService.GenericEnrichWithEmployeeDataAsync(listDto);

        return dto;
    }

    private List<AttributeValuesDto> BuildAttributeValues(PriorityPlanGroupAttrDto attr, List<string> valueIds, dynamic dicts)
    {
        var result = new List<AttributeValuesDto>();
        switch (attr.PropertyType)
        {
            case PriorityType.BasicService:
                foreach (var id in valueIds)
                {
                    if (dicts.Service.ContainsKey(id))
                    {
                        var s = dicts.Service[id];
                        result.Add(new AttributeValuesDto
                        {
                            Id = s.ServiceTypeId,
                            Code = s.ServiceTypeId,
                            Name = s.ServiceTypeName
                        });
                    }
                }
                break;

            case PriorityType.ExtraService:
                foreach (var id in valueIds)
                {
                    if (dicts.Extra.ContainsKey(id))
                    {
                        var s = dicts.Extra[id];
                        result.Add(new AttributeValuesDto
                        {
                            Id = s.ServiceId,
                            Code = s.ServiceId,
                            Name = s.ServiceName
                        });
                    }
                }
                break;

            case PriorityType.Origin:
            case PriorityType.Destination:
                switch (attr.LocationType)
                {
                    case LocationType.Ward:
                        foreach (var id in valueIds)
                        {
                            if (dicts.Ward.ContainsKey(id))
                            {
                                var w = dicts.Ward[id];
                                result.Add(new AttributeValuesDto
                                {
                                    Id = w.WardId,
                                    Code = w.WardId,
                                    Name = w.WardName
                                });
                            }
                        }
                        break;

                    case LocationType.Province:
                        foreach (var id in valueIds)
                        {
                            if (dicts.Province.ContainsKey(id))
                            {
                                var p = dicts.Province[id];
                                result.Add(new AttributeValuesDto
                                {
                                    Id = p.ProvinceId,
                                    Code = p.ProvinceId,
                                    Name = p.ProvinceName
                                });
                            }
                        }
                        break;

                    case LocationType.PostOffice:
                        foreach (var id in valueIds)
                        {
                            if (dicts.PostOffice.ContainsKey(id))
                            {
                                var po = dicts.PostOffice[id];
                                result.Add(new AttributeValuesDto
                                {
                                    Id = po.Id.ToString(),
                                    Code = po.PostOfficeCode,
                                    Name = po.PostOfficeName
                                });
                            }
                        }
                        break;
                }
                break;
            default:
                break;
        }

        return result;
    }

}
