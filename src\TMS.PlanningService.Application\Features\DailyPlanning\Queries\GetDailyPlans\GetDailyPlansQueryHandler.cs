﻿using System.Linq.Expressions;
using LinqKit;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.PlanningService.Application.Features.DailyPlanning.Queries.GetDailyPlans;

/// <summary>
/// Handler for getting daily plans with filtering and pagination
/// Combines database data with Redis aggregation counts
/// </summary>
public class GetDailyPlansQueryHandler
    : IRequestHandler<GetDailyPlansQuery, PagedResult<DailyPlanDto>>
{
    private readonly IBaseRepository<DailyPlanningEntity> _dailyPlanRepository;
    private readonly IDailyPlanningAggregationService _dailyPlanningAggregationService;
    private readonly ILogger<GetDailyPlansQueryHandler> _logger;
    private readonly IExternalDataService _externalDataService;
    private readonly IBaseRepository<RouteAggregationEntity> _routeAggregationRepository;

    public GetDailyPlansQueryHandler(
        IBaseRepository<DailyPlanningEntity> dailyPlanRepository,
        IDailyPlanningAggregationService dailyPlanningAggregationService,
        ILogger<GetDailyPlansQueryHandler> logger,
        IExternalDataService externalDataService,
        IBaseRepository<RouteAggregationEntity> routeAggregationRepository)
    {
        _dailyPlanRepository = dailyPlanRepository;
        _dailyPlanningAggregationService = dailyPlanningAggregationService;
        _logger = logger;
        _externalDataService = externalDataService;
        _routeAggregationRepository = routeAggregationRepository;
    }

    public async Task<PagedResult<DailyPlanDto>> Handle(
        GetDailyPlansQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Getting daily plans - Page: {Page}, PageSize: {PageSize}",
            request.Request.Page,
            request.Request.PageSize);

        var predicate = BuildPredicate(request.Request);
        var sortOptions = GetSortOptions(request.Request.SortOrder);

        // STEP 1: Get paged DailyPlanningEntity WITHOUT includes (lightweight)
        var pagedResult = await _dailyPlanRepository.GetPagedAsync(
           request.Request.Page,
           request.Request.PageSize,
           predicate,
           sortOptions,
           null, // No includes - don't load RouteAggregations table
           cancellationToken);

        if (!pagedResult.Items.Any())
        {
            _logger.LogInformation("No daily plans found");
            return new PagedResult<DailyPlanDto>(
                new List<DailyPlanDto>(),
                0,
                pagedResult.PageNumber,
                pagedResult.PageSize);
        }

        // STEP 2: Get plan totals from Redis cache (real-time weights) using batch GetManyAsync
        var planIds = pagedResult.Items.Select(p => p.Id).ToList();
        var planTotalsDict = await _dailyPlanningAggregationService.GetPlanTotalsBatchAsync(planIds, cancellationToken);

        // STEP 3: Get daily planning route aggregations from Redis
        // PERFORMANCE OPTIMIZATION: Extract route keys from plan totals and fetch ONLY those aggregations
        // Instead of fetching ALL aggregations and filtering (could be 10,000+), we fetch only what we need
        var allRouteKeys = planTotalsDict.Values
            .Where(totals => totals != null)
            .SelectMany(totals => totals.RouteKeys)
            .Distinct()
            .ToList();

        _logger.LogInformation("Fetching {Count} route aggregations for {PlanCount} daily plans", allRouteKeys.Count, planIds.Count);

        // Fetch relevant route aggregations from Redis
        var relevantAggregations = allRouteKeys.Any()
            ? await _dailyPlanningAggregationService.GetRouteAggregationsAsync(allRouteKeys, cancellationToken)
            : new List<RouteAggregationSummary>();

        var existingKeys = relevantAggregations.Select(a => (a.RouteKey, a.Id)).ToHashSet();

        // Fetch relevant aggregations from database as fallback for missing ones in redis
        var dbRouteAggregations = _routeAggregationRepository.GetQueryable()
                        .Where(ra => ra.AggregationType == "daily" && planIds.Contains(ra.DailyPlanningId ?? Guid.Empty))
                        .Select(x => new RouteAggregationSummary 
                        { 
                            Id = x.Id,
                            RouteKey = x.RouteKey,
                            TotalOrders = x.TotalOrders,
                            TotalCalWeight = x.TotalCalWeight,
                            TotalRealWeight = x.TotalRealWeight,
                            TotalWeight = x.TotalWeight,
                            TotalDiffWeight = x.TotalDiffWeight,
                        }).ToList() ?? new List<RouteAggregationSummary>();  // Filter by priority aggregation type

        // Add only those not already in relevantAggregations
        dbRouteAggregations = dbRouteAggregations.Where(dbAgg => !existingKeys.Contains((dbAgg.RouteKey, dbAgg.Id))).ToList();
        relevantAggregations.AddRange(dbRouteAggregations);

        // planning should be ordered by sequency
        relevantAggregations = relevantAggregations.OrderBy(x => x.RouteKey).ToList();

        // STEP 4: Map to DTOs with Redis totals and aggregation data
        var dtos = pagedResult.Items.Select(plan =>
            MapToDto(plan, relevantAggregations, planTotalsDict)).ToList();

        await _externalDataService.EnrichWithDataDailyPlanAsync(dtos);

        _logger.LogInformation(
            "Retrieved {Count} daily plans (Total: {Total}, Page: {Page}/{TotalPages})",
            dtos.Count,
            pagedResult.TotalCount,
            pagedResult.PageNumber,
            (pagedResult.TotalCount + pagedResult.PageSize - 1) / pagedResult.PageSize);

        return new PagedResult<DailyPlanDto>(
                  dtos,
                  pagedResult.TotalCount,
                  pagedResult.PageNumber,
                  pagedResult.PageSize);
    }

    private DailyPlanDto MapToDto(
        DailyPlanningEntity plan,
        List<RouteAggregationSummary> allAggregations,
        Dictionary<Guid, DailyPlanTotals> planTotalsDict)
    {
        // Filter aggregations for this specific plan using DailyPlanningId
        var relevantAggregations = allAggregations
            .Where(a => a.DailyPlanningId == plan.Id)
            .ToList();

        // Calculate counts from Redis aggregations
        var routeCount = relevantAggregations.Count;
        var totalOrders = relevantAggregations.Sum(a => a.TotalOrders);

        // Get real-time totals from Redis cache (if available), otherwise fallback to database
        var hasCachedTotals = planTotalsDict.TryGetValue(plan.Id, out var cachedTotals);

        var postOffices = new List<DailyPlanPostOfficeDto>();
        if (!string.IsNullOrWhiteSpace(plan.PostOfficeCodes))
        {
            var codes = plan.PostOfficeCodes.Split(new[] { ' ' }, StringSplitOptions.RemoveEmptyEntries);
            for (int i = 0; i < codes.Length - 1; i++)
            {
                postOffices.Add(new DailyPlanPostOfficeDto
                {
                    FromCode = codes[i],
                    ToCode = codes[i + 1],
                });
            }
        }

        return new DailyPlanDto
        {
            Id = plan.Id,
            CompanyId = plan.CompanyId,
            PlanningTemplateId = plan.PlanningTemplateId,
            RouteId = plan.RouteId,
            ExecutionDate = plan.ExecutionDate,
            Code = plan.Code,
            Name = plan.Name,
            TotalDistance = plan.TotalDistance,
            TotalDuration = plan.TotalDuration,
            OfficeCount = plan.OfficeCount,
            VehicleTypeId = plan.VehicleTypeId,
            RouteCode = plan.RouteCode,
            PostOfficeCodes = plan.PostOfficeCodes,
            PriorityNumber = plan.PriorityNumber,
            Status = plan.Status,
            ActualStartTime = plan.ActualStartTime,
            ActualEndTime = plan.ActualEndTime,
            IsActive = plan.IsActive,
            // Use Redis cached totals (real-time) if available, otherwise use database values
            TotalEstimateWeight = hasCachedTotals ? (cachedTotals?.TotalWeight ?? 0) / 1000 : plan.TotalWeight / 1000,
            TotalOnVehicleWeight = 0,  // need implement later when having US loading mailer onto vehicle
            RouteCount = routeCount,
            TotalOrders = totalOrders,
            CreatedAt = plan.CreatedAt,
            UpdatedAt = plan.UpdatedAt,
            PostOfficeDtos = postOffices
        };
    }

    private Expression<Func<DailyPlanningEntity, bool>> BuildPredicate(GetDailyPlansRequest request)
    {
        var predicate = PredicateBuilder.New<DailyPlanningEntity>(true);

        if (request.ExecutionDateFrom.HasValue)
            predicate = predicate.And(p => p.ExecutionDate >= request.ExecutionDateFrom.Value);
        if (request.ExecutionDateTo.HasValue)
            predicate = predicate.And(p => p.ExecutionDate <= request.ExecutionDateTo.Value);
        if (!string.IsNullOrEmpty(request.Status))
            predicate = predicate.And(p => p.Status == request.Status);
        if (request.IsActive.HasValue)
            predicate = predicate.And(p => p.IsActive == request.IsActive.Value);
        if (request.PlanningTemplateId.HasValue)
            predicate = predicate.And(p => p.PlanningTemplateId == request.PlanningTemplateId.Value);
        if (request.VehicleTypeIds?.Any() == true)
            predicate = predicate.And(p => request.VehicleTypeIds.Contains(p.VehicleTypeId));
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLowerInvariant();
            predicate = predicate.And(p =>
                p.Code.ToLower().Contains(searchTerm) ||
                p.Name.ToLower().Contains(searchTerm) ||
                p.RouteCode.ToLower().Contains(searchTerm));
        }

        return predicate;
    }
    private IEnumerable<ISortOption<DailyPlanningEntity>> GetSortOptions(SortOrderDailyPlan? sortOrder)
    {
        var sorts = SortBuilder<DailyPlanningEntity>.Create();

        return sortOrder switch
        {
            SortOrderDailyPlan.CodeAsc => sorts.ThenBy(v => v.Code),
            SortOrderDailyPlan.CodeDesc => sorts.ThenByDescending(v => v.Code),
            SortOrderDailyPlan.ExecutionDateAsc => sorts.ThenBy(v => v.ExecutionDate),
            SortOrderDailyPlan.ExecutionDateDesc => sorts.ThenByDescending(v => v.ExecutionDate),
            _ => sorts.ThenByDescending(v => v.ExecutionDate)
        };
    }
}
