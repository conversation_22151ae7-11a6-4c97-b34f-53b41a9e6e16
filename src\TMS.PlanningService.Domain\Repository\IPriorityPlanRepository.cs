﻿using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Domain.IRepository;
public interface IPriorityPlanRepository : IBaseRepository<PriorityPlan>
{
    public Task<List<PriorityPlan>> GetActivePriorityPlanByCompanyIdAsync(Guid companyId, CancellationToken cancellationToken = default);

    public Task<List<PriorityPlan>> GetActivePriorityPlanAsync(CancellationToken cancellationToken = default);

}
