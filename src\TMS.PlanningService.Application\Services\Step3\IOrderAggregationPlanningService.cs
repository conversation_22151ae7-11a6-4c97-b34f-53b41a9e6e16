﻿using TMS.PlanningService.Contracts.Orders;

namespace TMS.PlanningService.Application.Services.Step3;

/// <summary>
/// Service for processing order aggregations and triggering planning operations
/// </summary>
public interface IOrderAggregationPlanningService
{
    /// <summary>
    /// Process order aggregation event from OrderService
    /// Triggers planning calculations based on office workload
    /// </summary>
    Task ProcessOrderAggregationAsync(
        OrderAggregationEvent aggregation,
        CancellationToken cancellationToken = default);
}
