﻿using FluentValidation;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.SharedKernel.Constants;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.CreatePriorityPlan;

public class CreatePriorityPlanGroupAttrValidator : AbstractValidator<CreatePriorityPlanGroupAttrRequest>
{
    public CreatePriorityPlanGroupAttrValidator()
    {
        RuleFor(x => x.PropertyType)
        .IsInEnum()
        .NotNull()
        .WithMessage(string.Format(ValidationMessages.Required, "PropertyType"));

        RuleFor(x => x.PropertyOperator)
        .IsInEnum()
        .NotNull()
        .WithMessage(string.Format(ValidationMessages.Required, "PropertyOperator"));

        RuleFor(x => x.Values)
        .NotEmpty()
        .WithMessage(string.Format(ValidationMessages.Required, "Values"));

        //RuleFor(x => x.LogicOperator)
        //.NotEmpty()
        //.WithMessage(string.Format(ValidationMessages.Required, "LogicOperator"));

        // Todo: Add more validation rules for Details if needed like checking for valid PostOfficeId, Time ranges, etc.
    }
}
