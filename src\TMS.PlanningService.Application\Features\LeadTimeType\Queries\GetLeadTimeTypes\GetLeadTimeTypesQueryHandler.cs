﻿using System.Linq.Expressions;
using LinqKit;
using Mapster;
using MediatR;
using TMS.PlanningService.Contracts.Dto;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using Entity = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Features.LeadTimeType.Queries.GetLeadTimeTypes;

public class GetLeadTimeTypesQueryHandler : IRequestHandler<GetLeadTimeTypesQuery, List<LeadTimeTypeDto>>
{
    private readonly IMetadataCacheService _metadataCacheService;
    private readonly IBaseRepository<Entity.LeadTimeType> _baseRepository;
    public GetLeadTimeTypesQueryHandler(IMetadataCacheService metadataCacheService,
        IBaseRepository<Entity.LeadTimeType> baseRepository)
    {
        _metadataCacheService = metadataCacheService;
        _baseRepository = baseRepository;
    }

    public async Task<List<LeadTimeTypeDto>> Handle(GetLeadTimeTypesQuery request, CancellationToken cancellationToken)
    {
        var leadTimeTypes = await _metadataCacheService.GetLeadTimeTypesAsync<Entity.LeadTimeType>();
        var data = leadTimeTypes.Select(x => new LeadTimeTypeDto
        {
            Id = x.Id,
            Name = x.Name,
        }).ToList();

        if (!data.Any())
        {
            data = (await _baseRepository.GetAllAsync(cancellationToken))
                  .Select(x => new LeadTimeTypeDto
                  {
                      Id = x.Id,
                      Name = x.Name,
                  }).ToList();
        }

        return data.OrderBy(x => x.Id).ToList();
    }
}
