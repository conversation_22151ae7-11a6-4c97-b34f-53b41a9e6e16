﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>


  <ItemGroup>
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.0" />
    <PackageReference Include="TMS.SharedKernel.EntityFrameworkCore" Version="1.*" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TMS.PlanningService.Domain\TMS.PlanningService.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Update="DatabaseInitializer\Schemas03.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DatabaseInitializer\Schemas02.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DatabaseInitializer\Schemas01.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DatabaseInitializer\SchemasDefault.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="DatabaseInitializer\SchemasQuartz.sql">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Migrations\" />
  </ItemGroup>


</Project>
