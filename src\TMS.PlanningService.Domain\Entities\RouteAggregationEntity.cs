﻿using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Domain.Entities;

/// <summary>
/// Persisted snapshot of route aggregation data from Redis cache
/// Used for historical tracking and analytics of route performance
/// Supports multiple aggregation types: normal, daily, priority
/// </summary>
public class RouteAggregationEntity : EntityBase
{
    /// <summary>
    /// Route identifier: FromTime:ToTime:FromOfficeId:ToOfficeId
    /// Formatted with time first for easy chronological sorting
    /// Time information is already embedded in the key
    /// </summary>
    public string RouteKey { get; set; } = string.Empty;

    /// <summary>
    /// Aggregation type discriminator: 'normal', 'daily', 'priority'
    /// Used to separate different types of route aggregations
    /// </summary>
    public string AggregationType { get; set; } = "normal";

    /// <summary>
    /// Last snapshot timestamp - when this record was last updated from Redis
    /// </summary>
    public DateTime SnapshotAt { get; set; }

    /// <summary>
    /// Reference to daily planning
    /// NULL for normal/priority aggregations (no daily plan link)
    /// Actual plan ID for daily aggregations
    /// Note: Unique constraint uses COALESCE at database level to handle NULL uniqueness
    /// </summary>
    public Guid? DailyPlanningId { get; set; }

    /// <summary>
    /// Source office ID
    /// </summary>
    public string FromOfficeId { get; set; } = string.Empty;

    /// <summary>
    /// Destination office ID
    /// </summary>
    public string ToOfficeId { get; set; } = string.Empty;

    /// <summary>
    /// Planned departure time from source office
    /// </summary>
    public DateTime? FromTime { get; set; }

    /// <summary>
    /// Planned arrival time at destination office
    /// </summary>
    public DateTime? ToTime { get; set; }

    /// <summary>
    /// Actual departure time from source office (from MailerActualRoute)
    /// Only set if all actual routes have the same value
    /// </summary>
    public DateTime? ActualFromTime { get; set; }

    /// <summary>
    /// Actual arrival time at destination office (from MailerActualRoute)
    /// Only set if all actual routes have the same value
    /// </summary>
    public DateTime? ActualToTime { get; set; }

    /// <summary>
    /// Total duration in minutes for this route
    /// </summary>
    public int TotalDurationMinutes { get; set; }

    /// <summary>
    /// Average duration per mailer in minutes
    /// </summary>
    public double AverageDurationMinutes { get; set; }

    /// <summary>
    /// Earliest planned start time for this route
    /// </summary>
    public DateTime? EarliestStartTime { get; set; }

    /// <summary>
    /// Latest planned end time for this route
    /// </summary>
    public DateTime? LatestEndTime { get; set; }

    /// <summary>
    /// Total number of orders on this route (from OrderService)
    /// </summary>
    public int TotalOrders { get; set; }

    /// <summary>
    /// Total number of order items on this route (from OrderService)
    /// </summary>
    public int TotalItems { get; set; }

    /// <summary>
    /// Total weight of orders on this route (from OrderService)
    /// </summary>
    public decimal TotalWeight { get; set; }

    /// <summary>
    /// Total real weight of orders on this route (from OrderService)
    /// </summary>
    public decimal TotalRealWeight { get; set; }

    /// <summary>
    /// Total calculated weight of orders on this route (from OrderService)
    /// </summary>
    public decimal TotalCalWeight { get; set; }

    /// <summary>
    /// Priority score for this route (higher = more critical)
    /// </summary>
    public int PriorityScore { get; set; }

    /// <summary>
    /// Indicates if this route needs optimization
    /// </summary>
    public bool NeedsOptimization { get; set; }

    /// <summary>
    /// Transport provider breakdown (JSON serialized)
    /// </summary>
    public string TransportProviderBreakdownJson { get; set; } = "[]";

    /// <summary>
    /// Vehicle type breakdown (JSON serialized)
    /// </summary>
    public string VehicleTypeBreakdownJson { get; set; } = "[]";

    /// <summary>
    /// Transport method breakdown (JSON serialized)
    /// </summary>
    public string TransportMethodBreakdownJson { get; set; } = "[]";

    /// <summary>
    /// Original aggregation timestamp from Redis
    /// </summary>
    public DateTime AggregatedAt { get; set; }

    /// <summary>
    /// Record creation timestamp
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Navigation property to DailyPlanning (only used when AggregationType='daily')
    /// </summary>
    public DailyPlanningEntity? DailyPlanning { get; set; }

    /// <summary>
    /// Collection of orders associated with this route aggregation
    /// Orders remain in this collection even after moving to other routes
    /// </summary>
    public List<RouteAggregationOrderEntity> Orders { get; set; } = new();

    public BusinessOperation FromBusinessOperation { get; set; }

    public BusinessOperation ToBusinessOperation { get; set; }

    public decimal TotalDiffWeight { get; set; }

    public Guid? PriorityPlanId { get; set; }
}
