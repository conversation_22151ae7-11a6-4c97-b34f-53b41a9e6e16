﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Infra.Data.Configurations;

public class MailerRouteMasterConfiguration : IEntityTypeConfiguration<MailerRouteMasterEntity>
{
    public void Configure(EntityTypeBuilder<MailerRouteMasterEntity> builder)
    {
        builder.ToTable("mailer_route_masters");

        builder.HasKey(x => new { x.MailerId, x.ChildMailerId, x.CreatedDate });

        builder.Property(x => x.MailerId)
            .HasColumnName("mailer_id")
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.ChildMailerId)
            .HasColumnName("child_mailer_id")
            .HasMaxLength(50);

        builder.Property(x => x.CurrentPostOffice)
            .HasColumnName("current_post_office")
            .HasMaxLength(10);

        builder.Property(x => x.NextPostOffice)
            .HasColumnName("next_post_office")
            .HasMaxLength(50);

        builder.Property(x => x.IsCorrectRoute)
            .HasColumnName("is_correct_route");

        builder.Property(x => x.IsFullMailerPlanRoute)
            .HasColumnName("is_full_mailer_plan_route");

        builder.Property(x => x.IsHaveMailerPlanRoute)
            .HasColumnName("is_have_mailer_plan_route");

        builder.Property(x => x.Status)
            .HasColumnName("status")
            .HasMaxLength(50);

        builder.Property(x => x.FromPostOfficeId)
            .HasColumnName("from_post_office_id")
            .HasMaxLength(10);

        builder.Property(x => x.ToPostOfficeId)
            .HasColumnName("to_post_office_id")
            .HasMaxLength(10);

        builder.Property(x => x.SenderAddress)
            .HasColumnName("sender_address")
            .HasMaxLength(500);

        builder.Property(x => x.ReceiverAddress)
            .HasColumnName("receiver_address")
            .HasMaxLength(500);

        builder.Property(x => x.LastPOInPlan)
            .HasColumnName("last_po_in_plan")
            .HasMaxLength(500);

        builder.Property(x => x.PlanStartTime)
            .HasColumnName("plan_start_time");

        builder.Property(x => x.PlanEndTime)
            .HasColumnName("plan_end_time");

        builder.Property(x => x.PlanDurationMinutes)
            .HasColumnName("plan_duration_minutes");

        builder.Property(x => x.AdjustStartTime)
            .HasColumnName("adjust_start_time");

        builder.Property(x => x.AdjustEndTime)
            .HasColumnName("adjust_end_time");

        builder.Property(x => x.ActualStartTime)
            .HasColumnName("actual_start_time");

        builder.Property(x => x.ActualEndTime)
            .HasColumnName("actual_end_time");

        builder.Property(x => x.AdjustDurationMinutes)
            .HasColumnName("adjust_duration_minutes");

        builder.Property(x => x.IsForwarded)
            .HasColumnName("is_forwarded");

        builder.Property(x => x.POsSendWrongWay)
            .HasColumnName("pos_send_wrong_way")
            .HasMaxLength(500);

        builder.Property(x => x.CurrentSLAType)
            .HasColumnName("current_sla_type")
            .HasMaxLength(500);

        builder.Property(x => x.CurrentSLATime)
           .HasColumnName("current_sla_time");

        builder.Property(x => x.CreatedDate)
            .HasColumnName("created_date");

        builder.Property(x => x.CreatedUserId)
            .HasColumnName("created_user_id")
            .HasMaxLength(50);

        builder.Property(x => x.LastUpdateDate)
            .HasColumnName("last_update_date");

        builder.Property(x => x.LastUpdateUser)
            .HasColumnName("last_update_user")
            .HasMaxLength(50);

        // Relationships
        builder.HasMany(x => x.PlanRoutes)
            .WithOne(x => x.MailerRouteMaster)
            .HasForeignKey(x => new { x.MailerId, x.ChildMailerId, x.MasterCreatedDate })
            .HasPrincipalKey(x => new { x.MailerId, x.ChildMailerId, x.CreatedDate })
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.AdjustRoutes)
            .WithOne(x => x.MailerRouteMaster)
            .HasForeignKey(x => new { x.MailerId, x.ChildMailerId, x.MasterCreatedDate })
            .HasPrincipalKey(x => new { x.MailerId, x.ChildMailerId, x.CreatedDate }) 
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.ActualRoutes)
           .WithOne(x => x.MailerRouteMaster)
           .HasForeignKey(x => new { x.MailerId, x.ChildMailerId, x.MasterCreatedDate })
           .HasPrincipalKey(x => new { x.MailerId, x.ChildMailerId, x.CreatedDate }) 
           .OnDelete(DeleteBehavior.Cascade);
    }
}
