﻿using System.Collections.Concurrent;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Services.Orders;
using TMS.PlanningService.Application.Services.RouteOptimization;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernal.SmoothRedis;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Services.Step2.DailyPlanning;

/// <summary>
/// Service for real-time aggregation of routes for daily execution plans
/// Extends PlanningAggregationService to reuse core aggregation logic
/// Registered as singleton - no database dependencies in constructor
/// Database operations are performed by jobs that pass repositories as parameters
/// Maintains plan totals in Redis for high-performance access with 300K orders/day
/// </summary>
public class DailyPlanningAggregationService : PlanningAggregationService, IDailyPlanningAggregationService
{
    // Override Redis keys to use daily-plan-specific namespace
    protected override string RouteAggregationKeyPrefix => "daily-planning:route-aggregation:";
    protected override string AllRoutesKey => "daily-planning:all-route-keys";
    protected override string LockPrefix => "daily-route:";

    // Redis key for storing all active daily plan IDs (for efficient total updates)
    private const string AllDailyPlanIdsKey = "daily-planning:all-plan-ids";

    // Redis key prefix for plan totals (e.g., "daily-planning:plan-totals:{planId}")
    private const string PlanTotalsKeyPrefix = "daily-planning:plan-totals:";

    // FIXED: Batch size limits to prevent memory issues with large datasets
    private const int REDIS_BATCH_SIZE = 500; // Limit Redis batch operations
    private const int PERSISTENCE_BATCH_SIZE = 100; // Process 100 routes per database batch

    // Redis key for storing valid route time windows (shared across all instances)
    private const string RouteTimeWindowsKey = "daily-planning:route-time-windows";

    // Distributed lock configuration for various operations
    // Lock expiry: How long the lock stays valid (prevents deadlock if holder crashes)
    // Lock timeout: How long to wait trying to acquire the lock before giving up
    private static readonly TimeSpan LOCK_EXPIRY_TIME_WINDOWS = TimeSpan.FromSeconds(30);
    private static readonly TimeSpan LOCK_TIMEOUT_TIME_WINDOWS = TimeSpan.FromSeconds(10);
    private static readonly TimeSpan LOCK_EXPIRY_PLAN_REGISTRATION = TimeSpan.FromSeconds(10);
    private static readonly TimeSpan LOCK_TIMEOUT_PLAN_REGISTRATION = TimeSpan.FromSeconds(5);
    private static readonly TimeSpan LOCK_EXPIRY_PLAN_TOTALS = TimeSpan.FromSeconds(30);
    private static readonly TimeSpan LOCK_TIMEOUT_PLAN_TOTALS = TimeSpan.FromSeconds(10);

    // In-memory cache for time windows to reduce Redis calls
    // Trade-off: 30s staleness for 97% reduction in Redis load (critical at 1M orders/day)
    private static ConcurrentDictionary<string, List<TimeWindowMetadata>>? _timeWindowsCache;
    private static DateTime _timeWindowsCacheExpiry = DateTime.MinValue;
    private static readonly TimeSpan TimeWindowsCacheLifetime = TimeSpan.FromSeconds(30);
    private static readonly SemaphoreSlim _cacheRefreshLock = new SemaphoreSlim(1, 1);

    private readonly IServiceProvider _serviceProvider;

    public DailyPlanningAggregationService(
        ILogger<DailyPlanningAggregationService> logger,
        ISmoothRedis redis,
        IOrderDataService orderDataService,
        IServiceProvider serviceProvider,
        IRouteMetadataManager metadataManager)
        : base(logger, redis, orderDataService, metadataManager)
    {
        _serviceProvider = serviceProvider;
    }

    /// <summary>
    /// Loads valid route time windows from Redis with 30-second in-memory caching
    /// Reduces Redis load by 97% (from 11.6 calls/sec to 1 call/30sec per instance)
    /// Critical for 1M orders/day scalability
    /// Returns null if no time windows are configured (allows all routes during initialization)
    /// </summary>
    private async Task<ConcurrentDictionary<string, List<TimeWindowMetadata>>?> GetRouteTimeWindowsAsync()
    {
        // Check if cache is still valid (fast path - no lock)
        if (_timeWindowsCache != null && DateTime.UtcNow < _timeWindowsCacheExpiry)
        {
            return _timeWindowsCache;
        }

        // Cache expired - need to refresh (use lock to prevent thundering herd)
        await _cacheRefreshLock.WaitAsync();
        try
        {
            // Double-check after acquiring lock (another thread may have refreshed)
            if (_timeWindowsCache != null && DateTime.UtcNow < _timeWindowsCacheExpiry)
            {
                return _timeWindowsCache;
            }

            // Fetch from Redis
            var timeWindows = await _redis.Cache.GetAsync<ConcurrentDictionary<string, List<TimeWindowMetadata>>>(RouteTimeWindowsKey);

            // Update cache
            _timeWindowsCache = timeWindows;
            _timeWindowsCacheExpiry = DateTime.UtcNow.Add(TimeWindowsCacheLifetime);

            _logger.LogInformation(
                "Refreshed time windows cache - {RouteCount} office pairs configured, cache valid until {Expiry}",
                timeWindows?.Count ?? 0,
                _timeWindowsCacheExpiry);

            return timeWindows;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load route time windows from Redis - will allow all routes");
            return null; // Fail open - allow all routes if Redis is unavailable
        }
        finally
        {
            _cacheRefreshLock.Release();
        }
    }

    /// <summary>
    /// Generates a daily plan for the next day using the specified template
    /// Handles duplicate plan exceptions gracefully (thread-safe)
    /// Note: GeneratePlanFromTemplateAsync internally calls InitializeRouteAggregationsAsync
    /// Uses IServiceProvider to lazy-resolve IDailyPlanningGeneratorService to avoid circular dependency
    /// </summary>
    private async Task GenerateNextDayPlanAsync(
        Guid templateId,
        DateOnly executionDate,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation(
                "Generating next-day plan for template {TemplateId} on date {Date}",
                templateId,
                executionDate);

            // Lazy-resolve the generator service to avoid circular dependency at construction time
            using var scope = _serviceProvider.CreateScope();
            var dailyPlanningGeneratorService = scope.ServiceProvider.GetService<IDailyPlanningGeneratorService>();
            if (dailyPlanningGeneratorService == null)
            {
                _logger.LogError("Failed to resolve IDailyPlanningGeneratorService from service provider");
                return;
            }

            // Call the generator service to create the plan
            // This internally calls InitializeRouteAggregationsAsync with proper repository access
            await dailyPlanningGeneratorService.GenerateNewDateFromTemplateAsync(
                templateId,
                executionDate,
                planCounter: null, // Let the generator determine the counter
                cancellationToken);

            _logger.LogInformation(
                "Successfully generated next-day plan for template {TemplateId} on date {Date}",
                templateId,
                executionDate);
        }
        catch (DbUpdateException dbEx) when (dbEx.InnerException?.Message?.Contains("duplicate") == true ||
                                              dbEx.InnerException?.Message?.Contains("unique") == true)
        {
            // Plan already exists (concurrent request generated it) - this is OK
            _logger.LogInformation(
                "Next-day plan for template {TemplateId} on date {Date} already exists (generated by concurrent request)",
                templateId,
                executionDate);
        }
        catch (InvalidOperationException ioEx) when (ioEx.Message.Contains("already exists"))
        {
            // Plan already exists - this is OK
            _logger.LogInformation(
                "Next-day plan for template {TemplateId} on date {Date} already exists",
                templateId,
                executionDate);
        }
    }

    /// <summary>
    /// Helper method to check if a route should be included based on time windows
    /// Compares time-of-day only and detects if route belongs to next day
    /// Used inline during UpdateRouteAggregationsAsync after loading time windows from Redis
    /// </summary>
    private RouteValidationResult ValidateRouteAgainstTimeWindows(
        MailerPlanRoute route,
        ConcurrentDictionary<string, List<TimeWindowMetadata>> timeWindows)
    {
        var result = new RouteValidationResult { IsValid = false };

        // First check base validation (FromOffice and ToOffice must exist)
        if (!base.ShouldIncludeRoute(route))
        {
            return result;
        }

        // Create office pair key
        var officePairKey = $"{route.FromPostOfficeId}:{route.ToPostOfficeId}";

        // If this office pair doesn't have any valid time windows, exclude the route
        if (!timeWindows.TryGetValue(officePairKey, out var windows) || !windows.Any())
        {
            _logger.LogWarning(
                "Excluding route for mailer {MailerId} - no valid time windows found for {FromOffice} -> {ToOffice}",
                route.MailerId,
                route.FromPostOfficeId,
                route.ToPostOfficeId);
            return result;
        }

        // If route has no times, we can't validate it - exclude it
        if (!route.FromTime.HasValue || !route.ToTime.HasValue)
        {
            _logger.LogWarning(
                "Excluding route for mailer {MailerId} - missing FromTime or ToTime",
                route.MailerId);
            return result;
        }

        // Extract time-of-day from route times (ignore date portion)
        var routeFromTimeOfDay = route.FromTime.Value.TimeOfDay;
        var routeToTimeOfDay = route.ToTime.Value.TimeOfDay;
        var routeDate = DateOnly.FromDateTime(route.FromTime.Value.Date);

        // Check if the route's time-of-day matches any configured time window
        foreach (var window in windows)
        {
            if (!window.FromTime.HasValue || !window.ToTime.HasValue)
            {
                continue; // Skip windows without valid times
            }

            // Extract time-of-day from window times
            var windowFromTimeOfDay = window.FromTime.Value.TimeOfDay;
            var windowToTimeOfDay = window.ToTime.Value.TimeOfDay;

            // Compare time-of-day only (HH:mm:ss)
            var fromTimeMatches = !window.FromTime.HasValue || routeFromTimeOfDay >= windowFromTimeOfDay;
            var toTimeMatches = !window.ToTime.HasValue || routeToTimeOfDay <= windowToTimeOfDay;

            if (fromTimeMatches && toTimeMatches)
            {
                // Time-of-day matches! Now check if it's for the same day or next day
                if (routeDate == window.ExecutionDate)
                {
                    // Same day - route is valid for current plan
                    result.IsValid = true;
                    result.RequiresNextDayPlan = false;
                    result.MatchedWindow = window;
                    return result;
                }
                else if (routeDate > window.ExecutionDate)
                {
                    // Next day (or later) - need to generate plan for that date
                    result.IsValid = false; // Not valid for current plan
                    result.RequiresNextDayPlan = true;
                    result.MatchedWindow = window;
                    result.NextDayExecutionDate = routeDate;

                    _logger.LogInformation(
                        "Route for mailer {MailerId} matches time window but is for next day ({NextDay}) - will trigger plan generation",
                        route.MailerId,
                        routeDate);
                }
            }
        }

        // Route doesn't match any time window
        _logger.LogWarning(
            "Excluding route for mailer {MailerId} - times {FromTime} to {ToTime} don't match any valid window for {FromOffice} -> {ToOffice}",
            route.MailerId,
            route.FromTime,
            route.ToTime,
            route.FromPostOfficeId,
            route.ToPostOfficeId);
        return result;
    }

    /// <summary>
    /// Updates route aggregations in Redis and recalculates plan totals in real-time
    /// Plan IDs are automatically determined from existing aggregations in Redis
    /// No database access - all operations in Redis for high performance (300K orders/day)
    /// </summary>
    /// <param name="routes">Routes to aggregate</param>
    /// <param name="actualRoutes">Actual routes for comparison</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <param name="originalPlanningEvent">Optional original planning event to send to OrderService if metrics not found</param>
    public override async Task UpdateRouteAggregationsAsync(
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        List<MailerPlanRoute> planRoutes,
        bool isUsingAdjustRoutes,
        CancellationToken cancellationToken = default,
        RabbitMqPlanningEvent? originalPlanningEvent = null)
    {
        if (routes == null || !routes.Any())
        {
            _logger.LogWarning("No routes to aggregate for daily plans");
            return;
        }

        try
        {
            // Preload time windows from Redis (with caching) before processing routes
            // This ensures the cache is populated for ShouldIncludeRoute filtering
            var timeWindows = await GetRouteTimeWindowsAsync();

            if (timeWindows == null || !timeWindows.Any())
            {
                _logger.LogWarning("No time windows configured - cannot process routes for daily plans");
                return;
            }

            // STEP 1: Validate all routes and identify those requiring next-day plans
            var validationResults = new List<(MailerPlanRoute route, RouteValidationResult validation)>();
            var nextDayPlanRequests = new Dictionary<(Guid TemplateId, DateOnly ExecutionDate), TimeWindowMetadata>();

            foreach (var route in routes)
            {
                var validation = ValidateRouteAgainstTimeWindows(route, timeWindows);
                validationResults.Add((route, validation));

                // Collect next-day plan generation requests
                if (validation.RequiresNextDayPlan &&
                    validation.MatchedWindow != null &&
                    validation.NextDayExecutionDate.HasValue)
                {
                    var key = (validation.MatchedWindow.TemplateId, validation.NextDayExecutionDate.Value);
                    if (!nextDayPlanRequests.ContainsKey(key))
                    {
                        nextDayPlanRequests[key] = validation.MatchedWindow;
                    }
                }
            }

            // STEP 2: Generate next-day plans if needed
            if (nextDayPlanRequests.Any())
            {
                _logger.LogInformation(
                    "Detected {Count} next-day plan generation requests",
                    nextDayPlanRequests.Count);

                foreach (var request in nextDayPlanRequests)
                {
                    try
                    {
                        await GenerateNextDayPlanAsync(
                            request.Key.TemplateId,
                            request.Key.ExecutionDate,
                            cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex,
                            "Failed to generate next-day plan for template {TemplateId} on date {Date}",
                            request.Key.TemplateId,
                            request.Key.ExecutionDate);
                        // Continue with other plans
                    }
                }

                // STEP 3: Reload time windows after generating new plans
                _timeWindowsCache = null; // Invalidate cache
                timeWindows = await GetRouteTimeWindowsAsync();

                if (timeWindows == null || !timeWindows.Any())
                {
                    _logger.LogWarning("No time windows after next-day plan generation");
                    return;
                }

                // STEP 4: Re-validate all routes with updated time windows
                validationResults.Clear();
                foreach (var route in routes)
                {
                    var validation = ValidateRouteAgainstTimeWindows(route, timeWindows);
                    validationResults.Add((route, validation));
                }
            }

            // STEP 5: Map valid routes to predefined time window keys
            var mappedRoutes = new List<(MailerPlanRoute route, string mappedRouteKey)>();

            foreach (var (route, validation) in validationResults.Where(vr => vr.validation.IsValid))
            {
                var officePairKey = $"{route.FromPostOfficeId}:{route.ToPostOfficeId}";

                if (timeWindows.TryGetValue(officePairKey, out var windows) &&
                    validation.MatchedWindow != null)
                {
                    // Use the matched window's times to create the route key
                    var mappedKey = CreateRouteKey(
                        route.FromPostOfficeId!,
                        route.ToPostOfficeId!,
                        validation.MatchedWindow.FromTime,
                        validation.MatchedWindow.ToTime);
                    mappedRoutes.Add((route, mappedKey));
                }
            }

            if (!mappedRoutes.Any())
            {
                _logger.LogWarning("No routes matched any time windows");
                return;
            }

            // Group by the MAPPED route key (using time window times, not order times)
            var routeGroups = mappedRoutes
                .GroupBy(mr => mr.mappedRouteKey)
                .Select(g => new
                {
                    RouteKey = g.Key,
                    Routes = g.Select(mr => mr.route).ToList()
                })
                .ToList();

            _logger.LogInformation(
                "Mapped {TotalRoutes} incoming routes to {UniqueWindows} predefined time windows",
                routes.Count,
                routeGroups.Count);

            // Now process each group using the predefined route key
            var updateTasks = routeGroups.Select(routeGroup =>
                PrepareRouteAggregationAsync(routeGroup.RouteKey, routeGroup.Routes, actualRoutes, isUsingAdjustRoutes, cancellationToken, originalPlanningEvent)
            );

            await Task.WhenAll(updateTasks);

            // Fetch updated aggregations to determine affected plan IDs
            var routeKeys = routeGroups.Select(g => g.RouteKey).ToList();
            var cacheKeys = routeKeys.Select(routeKey => $"{RouteAggregationKeyPrefix}{routeKey}").ToArray();
            var results = await _redis.Batch.Cache.GetManyAsync<RouteAggregationSummary>(cacheKeys);

            var affectedPlanIds = results.Values
                .Where(agg => agg != null && agg.DailyPlanningId.HasValue && agg.DailyPlanningId.Value != Guid.Empty)
                .Select(agg => agg!.DailyPlanningId!.Value)
                .Distinct()
                .ToList();

            if (!affectedPlanIds.Any())
            {
                _logger.LogWarning("No plan IDs found in updated aggregations");
                return;
            }

            // Update plan totals for each affected plan
            foreach (var planId in affectedPlanIds)
            {
                await UpdatePlanTotalsInRedisAsync(planId, cancellationToken);
            }

            _logger.LogWarning(
                "Successfully updated route aggregations and plan totals for {PlanCount} daily plans",
                affectedPlanIds.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update route aggregations for daily plans");
            throw;
        }
    }

    /// <summary>
    /// Initializes route aggregations in Redis from generated daily plan
    /// Called by DailyPlanningGeneratorService after creating plan structure
    /// Repository is passed as parameter since this service is a singleton
    /// </summary>
    public async Task InitializeRouteAggregationsAsync(
        List<RouteAggregationSummary> initialAggregations,
        IBaseRepository<DailyPlanningEntity> dailyPlanRepository,
        CancellationToken cancellationToken = default,
        bool clearTimeWindow = true)
    {
        if (initialAggregations == null || !initialAggregations.Any())
        {
            _logger.LogWarning("No initial route aggregations to populate");
            return;
        }

        try
        {
            var aggregationsByPlan = initialAggregations
                .GroupBy(agg => agg.DailyPlanningId ?? Guid.Empty)
                .Where(g => g.Key != Guid.Empty)
                .ToDictionary(g => g.Key, g => g.ToList());

            // If no valid DailyPlanningIds, nothing to initialize
            if (!aggregationsByPlan.Any())
                return;

            // Acquire distributed lock for time window updates to prevent race conditions
            var lockResource = "daily-time-windows-update";
            await using var redisLock = await _redis.Locks.AcquireAsync(
                lockResource,
                expiry: LOCK_EXPIRY_TIME_WINDOWS,
                timeout: LOCK_TIMEOUT_TIME_WINDOWS);

            if (redisLock == null)
            {
                _logger.LogWarning(
                    "Failed to acquire lock for time windows update - Timeout after {TimeoutSeconds}s",
                    LOCK_TIMEOUT_TIME_WINDOWS.TotalSeconds);
                throw new InvalidOperationException("Could not update time windows - lock timeout");
            }

            // Load existing time windows from Redis (inside lock)
            var existingTimeWindows = await _redis.Cache.GetAsync<ConcurrentDictionary<string, List<TimeWindowMetadata>>>(RouteTimeWindowsKey)
                ?? new ConcurrentDictionary<string, List<TimeWindowMetadata>>();

            if (clearTimeWindow)
            {
                existingTimeWindows.Clear();
            }

            foreach (var aggregationsGroup in aggregationsByPlan)
            {
                // Get the daily plan ID and load plan details to get template info
                var dailyPlanId = aggregationsGroup.Key;
                var dailyPlanAggregations = aggregationsGroup.Value;
                _logger.LogInformation("Initializing {Count} route aggregations for daily plan {PlanId}", dailyPlanAggregations.Count(), dailyPlanId);

                // Query database to get template and execution date information using repository
                var dailyPlan = await dailyPlanRepository.GetQueryable()
                    .Include(dp => dp.PlanningTemplate)
                    .FirstOrDefaultAsync(dp => dp.Id == dailyPlanId, cancellationToken);

                if (dailyPlan == null)
                {
                    throw new InvalidOperationException($"Daily plan {dailyPlanId} not found");
                }

                // If no routes for this plan, skip
                if (!aggregationsGroup.Value.Any())
                {
                    continue;
                }

                // Build valid route time windows from the daily plan routes with enhanced metadata
                foreach (var agg in dailyPlanAggregations)
                {
                    var officePairKey = $"{agg.FromOfficeId}:{agg.ToOfficeId}";

                    if (!existingTimeWindows.ContainsKey(officePairKey))
                    {
                        existingTimeWindows[officePairKey] = new List<TimeWindowMetadata>();
                    }

                    // Create enhanced time window metadata with template information
                    var timeWindowMetadata = new TimeWindowMetadata
                    {
                        FromTime = agg.FromTime,
                        ToTime = agg.ToTime,
                        TemplateId = dailyPlan.PlanningTemplateId,
                        CompanyId = dailyPlan.CompanyId,
                        ExecutionDate = dailyPlan.ExecutionDate
                    };

                    // Check if this exact window already exists (avoid duplicates)
                    var exists = existingTimeWindows[officePairKey].Any(w =>
                        w.FromTime == agg.FromTime &&
                        w.ToTime == agg.ToTime &&
                        w.TemplateId == dailyPlan.PlanningTemplateId &&
                        w.ExecutionDate == dailyPlan.ExecutionDate);

                    if (!exists)
                    {
                        existingTimeWindows[officePairKey].Add(timeWindowMetadata);
                    }
                }


                var checkExistPlan = dailyPlanAggregations.FirstOrDefault()?.IsPlanningExist ?? false;
                if (!checkExistPlan)
                {
                    // Register this plan ID in the active daily plans set
                    await RegisterDailyPlanAsync(dailyPlanId, cancellationToken);

                    // Initialize plan totals in Redis with route keys list
                    var routeKeys = dailyPlanAggregations.Select(a => a.RouteKey).ToList();
                    await InitializePlanTotalsInRedisAsync(dailyPlanId, routeKeys, cancellationToken);

                    _logger.LogInformation(
                        "Initialized {Count} route aggregations and plan totals in Redis for daily plan {PlanId}",
                        dailyPlanAggregations.Count,
                        dailyPlanId);
                }

            }

            // Save updated time windows back to Redis (shared across all instances)
            await _redis.Cache.SetAsync(RouteTimeWindowsKey, existingTimeWindows, TimeSpan.FromDays(7));
            var totalWindows = existingTimeWindows.Values.Sum(list => list.Count);
            _logger.LogInformation(
                "Saved time windows to Redis for daily plan (total: {TotalCount} office pairs, {TotalWindows} windows)",
                existingTimeWindows.Count,
                totalWindows);

            #region Handle route aggregations key And all routes key
            // Save all route aggregations to Redis using batch operation for better performance
            // Store individual aggregations (one per route key) - last write wins if multiple plans share same route
            // This is consistent with normal planning behavior
            var newAggregations = initialAggregations.Where(x => x.IsPlanningExist != true);
            var aggregationDict = newAggregations
                .GroupBy(agg => agg.RouteKey)
                .ToDictionary(
                    g => $"{RouteAggregationKeyPrefix}{g.Key}",
                    g => g.OrderByDescending(a => a.AggregatedAt).First() // Take most recent if multiple plans share route key
                );

            await _redis.Batch.Cache.SetManyAsync(aggregationDict, TimeSpan.FromDays(7));

            // Update route metadata in AllRoutesKey
            var metadata = newAggregations
                            .GroupBy(a => a.RouteKey)
                            .Select(g => new RouteMetadata
                            {
                                RouteKey = g.Key,
                                AggregatedAt = g.First().AggregatedAt,
                                LastPersistedAt = null,
                                VehicleTypeIds = g.First().VehicleTypeBreakdown?
                                    .Select(vt => vt.Id)
                                    .Distinct()
                                    .ToList(),
                            }).ToDictionary(m => m.RouteKey);

            var existingMetadata = await _redis.Cache.GetAsync<Dictionary<string, RouteMetadata>>(AllRoutesKey)
                ?? new Dictionary<string, RouteMetadata>();

            foreach (var kvp in metadata)
            {
                existingMetadata[kvp.Key] = kvp.Value;
            }

            await _redis.Cache.SetAsync(AllRoutesKey, existingMetadata, TimeSpan.FromDays(7));
            #endregion

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize route aggregations in Redis");
            throw;
        }
    }

    /// <summary>
    /// Gets all route aggregations for a specific daily execution plan
    /// Filters routes by DailyPlanningId field (no route key parsing needed)
    /// Returns RouteAggregationSummary with populated DailyPlanningId
    /// </summary>
    public async Task<List<RouteAggregationSummary>> GetRouteAggregationsAsync(
        Guid dailyExecutionPlanId,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get all aggregations from Redis
            var allAggregations = await GetCurrentAggregationsAsync(cancellationToken);

            // Filter to only routes for this specific plan using DailyPlanningId field
            var planAggregations = allAggregations
                .Where(agg => agg.DailyPlanningId == dailyExecutionPlanId)
                .OrderBy(t => t.FromTime)
                .ToList();

            _logger.LogWarning(
                "Retrieved {Count} route aggregations for daily plan {DailyPlanId}",
                planAggregations.Count,
                dailyExecutionPlanId);

            return planAggregations;
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to get route aggregations for daily plan {DailyPlanId}",
                dailyExecutionPlanId);
            return new List<RouteAggregationSummary>();
        }
    }

    /// <summary>
    /// Gets route metadata for a specific daily execution plan
    /// </summary>
    public async Task<List<RouteMetadata>> GetRouteMetadataListAsync(
        Guid dailyExecutionPlanId,
        CancellationToken cancellationToken = default)
    {
        // For daily plans, we use the same metadata structure as base
        // but could filter by plan-specific routes in the future
        return await GetRouteMetadataListAsync(cancellationToken);
    }

    /// <summary>
    /// Reads plan totals from Redis and persists to database
    /// NO database queries for reading - only updates from Redis cache
    /// Optimized for 300K orders/day with real-time Redis updates
    /// Called by jobs that have access to scoped repositories
    /// </summary>
    public async Task UpdateDailyPlanTotalsAsync(
        Guid dailyExecutionPlanId,
        IBaseRepository<DailyPlanningEntity> dailyPlanRepository,
        IUnitOfWork unitOfWork,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogWarning("Reading plan totals from Redis for daily plan {DailyPlanId}", dailyExecutionPlanId);

            // Get plan totals from Redis (no DB query)
            var planTotalsKey = $"{PlanTotalsKeyPrefix}{dailyExecutionPlanId}";
            var planTotals = await _redis.Cache.GetAsync<DailyPlanTotals>(planTotalsKey);

            if (planTotals == null)
            {
                _logger.LogWarning(
                    "No plan totals found in Redis for daily plan {DailyPlanId} - will recalculate",
                    dailyExecutionPlanId);

                // Recalculate if missing from Redis
                await UpdatePlanTotalsInRedisAsync(dailyExecutionPlanId, cancellationToken);
                planTotals = await _redis.Cache.GetAsync<DailyPlanTotals>(planTotalsKey);

                if (planTotals == null)
                {
                    _logger.LogWarning("Failed to calculate plan totals for {DailyPlanId}", dailyExecutionPlanId);
                    return;
                }
            }

            // OPTIMIZATION: Check if totals have changed since last persistence
            // Only update database if values have actually changed
            var checkpointKey = $"daily-plan-totals-checkpoint:{dailyExecutionPlanId}";
            var lastPersistedTotals = await _redis.Cache.GetAsync<DailyPlanTotals>(checkpointKey);

            // Compare current totals with last persisted values
            if (lastPersistedTotals != null &&
                lastPersistedTotals.TotalWeight == planTotals.TotalWeight &&
                lastPersistedTotals.TotalRealWeight == planTotals.TotalRealWeight)
            {
                _logger.LogInformation(
                    "Daily plan {DailyPlanId} totals unchanged - skipping database update (TotalWeight: {TotalWeight}kg, TotalRealWeight: {TotalRealWeight}kg)",
                    dailyExecutionPlanId,
                    planTotals.TotalWeight,
                    planTotals.TotalRealWeight);
                return;
            }

            // Totals have changed - update database
            var affectedRows = await dailyPlanRepository.ExecuteUpdateAsync(
                predicate: e => e.Id == dailyExecutionPlanId,
                setterExpression: setters => setters
                    .SetProperty(e => e.TotalWeight, planTotals.TotalWeight)
                    .SetProperty(e => e.TotalRealWeight, planTotals.TotalRealWeight),
                cancellationToken: cancellationToken
            );

            if (affectedRows == 0)
            {
                _logger.LogWarning(
                    "Daily plan {DailyPlanId} not found in database - no rows updated",
                    dailyExecutionPlanId);
                return;
            }

            // Update checkpoint in Redis after successful database update
            await _redis.Cache.SetAsync(
                checkpointKey,
                planTotals,
                TimeSpan.FromHours(24)); // Checkpoint expires after 24 hours

            _logger.LogInformation(
                "Updated totals for daily plan {DailyPlanId} from Redis - TotalWeight: {TotalWeight}kg, TotalRealWeight: {TotalRealWeight}kg (previous: {PrevWeight}kg, {PrevRealWeight}kg)",
                dailyExecutionPlanId,
                planTotals.TotalWeight,
                planTotals.TotalRealWeight,
                lastPersistedTotals?.TotalWeight ?? 0,
                lastPersistedTotals?.TotalRealWeight ?? 0);
        }
        catch (Exception ex)
        {
            _logger.LogError(
                ex,
                "Failed to update totals for daily plan {DailyPlanId}",
                dailyExecutionPlanId);
            throw;
        }
    }

    /// <summary>
    /// Registers a daily plan ID in the active plans set
    /// </summary>
    private async Task RegisterDailyPlanAsync(Guid dailyPlanId, CancellationToken cancellationToken)
    {
        // Acquire lock to prevent concurrent modifications to the active plans set
        var lockResource = "daily-plan-registration";
        await using var redisLock = await _redis.Locks.AcquireAsync(
            lockResource,
            expiry: LOCK_EXPIRY_PLAN_REGISTRATION,
            timeout: LOCK_TIMEOUT_PLAN_REGISTRATION);

        if (redisLock == null)
        {
            _logger.LogWarning(
                "Failed to acquire lock for plan registration: {PlanId} - Timeout after {TimeoutSeconds}s",
                dailyPlanId,
                LOCK_TIMEOUT_PLAN_REGISTRATION.TotalSeconds);
            throw new InvalidOperationException($"Could not register daily plan {dailyPlanId} - lock timeout");
        }

        try
        {
            var activePlanIds = await _redis.Cache.GetAsync<HashSet<Guid>>(AllDailyPlanIdsKey)
                ?? new HashSet<Guid>();

            activePlanIds.Add(dailyPlanId);
            await _redis.Cache.SetAsync(AllDailyPlanIdsKey, activePlanIds, TimeSpan.FromDays(7));

            _logger.LogInformation("Registered daily plan {PlanId} in active plans set", dailyPlanId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to register daily plan {PlanId}", dailyPlanId);
            throw;
        }
    }

    /// <summary>
    /// Initializes plan totals to zero in Redis with route keys list
    /// </summary>
    private async Task InitializePlanTotalsInRedisAsync(Guid dailyPlanId, List<string> routeKeys, CancellationToken cancellationToken)
    {
        var planTotalsKey = $"{PlanTotalsKeyPrefix}{dailyPlanId}";
        var planTotals = new DailyPlanTotals
        {
            DailyPlanningId = dailyPlanId,
            TotalWeight = 0,
            TotalRealWeight = 0,
            UpdatedAt = DateTime.UtcNow,
            RouteKeys = routeKeys // Store route keys for direct lookup
        };

        await _redis.Cache.SetAsync(planTotalsKey, planTotals, TimeSpan.FromDays(7));

        _logger.LogWarning(
            "Initialized plan totals for daily plan {DailyPlanId} with {RouteCount} routes",
            dailyPlanId,
            routeKeys.Count);
    }

    /// <summary>
    /// Recalculates and updates plan totals in Redis for a specific plan
    /// Uses stored route keys list for direct lookup (no filtering needed)
    /// </summary>
    private async Task UpdatePlanTotalsInRedisAsync(Guid dailyPlanId, CancellationToken cancellationToken)
    {
        // Acquire distributed lock to prevent race conditions during concurrent plan totals updates
        var lockResource = $"daily-plan-totals:{dailyPlanId}";
        await using var redisLock = await _redis.Locks.AcquireAsync(
            lockResource,
            expiry: LOCK_EXPIRY_PLAN_TOTALS,
            timeout: LOCK_TIMEOUT_PLAN_TOTALS);

        if (redisLock == null)
        {
            _logger.LogWarning(
                "Failed to acquire lock for plan totals update: {PlanId} - Timeout after {TimeoutSeconds}s (skipping update)",
                dailyPlanId,
                LOCK_TIMEOUT_PLAN_TOTALS.TotalSeconds);
            return;
        }

        try
        {
            // Get existing plan totals to retrieve route keys list
            var planTotalsKey = $"{PlanTotalsKeyPrefix}{dailyPlanId}";
            var existingPlanTotals = await _redis.Cache.GetAsync<DailyPlanTotals>(planTotalsKey);

            if (existingPlanTotals == null || !existingPlanTotals.RouteKeys.Any())
            {
                _logger.LogWarning(
                    "No route keys found in plan totals for daily plan {DailyPlanId} - skipping update",
                    dailyPlanId);
                return;
            }

            // Fetch only the routes that belong to this plan using direct lookup
            var routeKeys = existingPlanTotals.RouteKeys;
            var cacheKeys = routeKeys.Select(routeKey => $"{RouteAggregationKeyPrefix}{routeKey}").ToArray();
            var results = await _redis.Batch.Cache.GetManyAsync<RouteAggregationSummary>(cacheKeys);
            var planRouteAggregations = results.Values
                .Where(s => s != null)
                .ToList();

            // Calculate totals by summing this plan's route aggregations
            var totalWeight = planRouteAggregations.Sum(r => r.TotalWeight);
            var totalRealWeight = planRouteAggregations.Sum(r => r.TotalRealWeight);

            // Update plan totals (preserve RouteKeys list)
            var planTotals = new DailyPlanTotals
            {
                DailyPlanningId = dailyPlanId,
                TotalWeight = totalWeight,
                TotalRealWeight = totalRealWeight,
                UpdatedAt = DateTime.UtcNow,
                RouteKeys = routeKeys // Preserve the route keys list
            };

            await _redis.Cache.SetAsync(planTotalsKey, planTotals, TimeSpan.FromDays(7));

            _logger.LogWarning(
                "Updated plan totals in Redis for daily plan {DailyPlanId} - Routes: {RouteCount}, TotalWeight: {TotalWeight}, TotalRealWeight: {TotalRealWeight}",
                dailyPlanId, planRouteAggregations.Count, totalWeight, totalRealWeight);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update plan totals in Redis for daily plan {DailyPlanId}", dailyPlanId);
            throw;
        }
    }

    /// <summary>
    /// Updates plan totals for all active daily plans
    /// Called after route aggregations are updated
    /// </summary>
    private async Task UpdateAllPlanTotalsInRedisAsync(CancellationToken cancellationToken)
    {
        try
        {
            // Use local time for consistency with plan generation
            var today = DateOnly.FromDateTime(DateTime.Now);
            var activePlanIds = await _redis.Cache.GetAsync<HashSet<Guid>>(AllDailyPlanIdsKey);

            if (activePlanIds == null || !activePlanIds.Any())
            {
                _logger.LogWarning("No active daily plans to update totals");
                return;
            }

            // Update totals for each active plan
            foreach (var planId in activePlanIds)
            {
                await UpdatePlanTotalsInRedisAsync(planId, cancellationToken);
            }

            _logger.LogWarning("Updated plan totals for {Count} active daily plans", activePlanIds.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update all plan totals in Redis");
            // Don't throw - this is a non-critical background operation
        }
    }

    /// <summary>
    /// Gets all active daily plan IDs from Redis
    /// </summary>
    public async Task<HashSet<Guid>> GetActiveDailyPlanIdsAsync(CancellationToken cancellationToken = default)
    {
        return await _redis.Cache.GetAsync<HashSet<Guid>>(AllDailyPlanIdsKey)
            ?? new HashSet<Guid>();
    }

    /// <summary>
    /// Gets plan totals from Redis cache for multiple daily plans in batch
    /// Uses GetManyAsync for efficient single-pipeline batch retrieval
    /// Returns real-time weight data: TotalWeight, TotalRealWeight, TotalDiffWeight
    /// </summary>
    public async Task<Dictionary<Guid, DailyPlanTotals>> GetPlanTotalsBatchAsync(
        List<Guid> planIds,
        CancellationToken cancellationToken = default)
    {
        if (planIds == null || !planIds.Any())
            return new Dictionary<Guid, DailyPlanTotals>();

        // Build Redis keys for all plan IDs
        var keys = planIds.Select(id => $"{PlanTotalsKeyPrefix}{id}").ToArray();

        // Fetch all plan totals in a single Redis pipeline call (most efficient!)
        var results = await _redis.Batch.Cache.GetManyAsync<DailyPlanTotals>(keys);

        // Map from Redis keys back to plan IDs, filtering out nulls
        var dictionary = new Dictionary<Guid, DailyPlanTotals>();
        foreach (var planId in planIds)
        {
            var redisKey = $"{PlanTotalsKeyPrefix}{planId}";
            if (results.TryGetValue(redisKey, out var totals) && totals != null)
            {
                dictionary[planId] = totals;
            }
        }

        _logger.LogWarning(
            "Batch fetched plan totals for {RequestedCount} plans, found {FoundCount} in cache",
            planIds.Count,
            dictionary.Count);

        return dictionary;
    }

    /// <summary>
    /// Gets lightweight metadata for all route aggregations (not filtered by plan)
    /// Used by snapshot job for efficient change detection
    /// </summary>
    public async Task<List<RouteMetadata>> GetAllRouteMetadataAsync()
    {
        return await base.GetRouteMetadataListAsync();
    }

    /// <summary>
    /// Override base PersistRouteAggregationsAsync to handle multi-plan persistence
    /// Daily aggregations create separate entities for each active plan
    /// Uses LastPersistedAt pattern for change detection
    /// </summary>
    public override async Task<int> PersistRouteAggregationsAsync(
        TMS.SharedKernel.Domain.IBaseRepository<TMS.PlanningService.Domain.Entities.RouteAggregationEntity> routeAggregationRepository,
        TMS.SharedKernel.Domain.IBaseRepository<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity> routeOrderRepository,
        TMS.SharedKernel.Domain.IUnitOfWork unitOfWork,
        DateTime snapshotTime,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Persisting daily plan route aggregations");

            // Get lightweight metadata first (fast - one Redis call)
            var allMetadata = await GetRouteMetadataListAsync(cancellationToken);

            if (allMetadata == null || !allMetadata.Any())
            {
                _logger.LogInformation("No daily plan route aggregations found in Redis - skipping");
                return 0;
            }

            _logger.LogWarning("Found {Count} daily plan route metadata entries in Redis", allMetadata.Count);

            // Filter to only routes that have changed since last persistence
            var changedRouteKeys = allMetadata
                .Where(m => !m.LastPersistedAt.HasValue || m.AggregatedAt != m.LastPersistedAt.Value)
                .Select(m => m.RouteKey)
                .ToList();

            if (!changedRouteKeys.Any())
            {
                _logger.LogInformation("No daily plan route aggregations have changed - skipping");
                return 0;
            }

            _logger.LogWarning(
                "Found {ChangedCount} changed daily plan routes out of {TotalCount}",
                changedRouteKeys.Count,
                allMetadata.Count);

            // Get all route aggregations from Redis
            var allAggregations = await GetCurrentAggregationsAsync(cancellationToken);

            // Filter to only changed routes
            var changedAggregations = allAggregations
                .Where(agg => changedRouteKeys.Contains(agg.RouteKey))
                .ToList();

            if (!changedAggregations.Any())
            {
                _logger.LogWarning("Failed to fetch any changed daily plan aggregations from Redis");
                return 0;
            }

            // Get all active daily plan IDs from Redis
            var activePlanIds = await GetActiveDailyPlanIdsAsync(cancellationToken);

            if (activePlanIds == null || !activePlanIds.Any())
            {
                _logger.LogWarning("No active daily plans found - skipping aggregation persistence");
                return 0;
            }

            // Batch processing configuration - use class constant
            var totalRoutesPersisted = 0;
            var totalOrdersPersisted = 0;
            var batchNumber = 0;

            // Process in batches to avoid huge transactions
            for (int i = 0; i < changedAggregations.Count; i += PERSISTENCE_BATCH_SIZE)
            {
                batchNumber++;
                var batchAggregations = changedAggregations.Skip(i).Take(PERSISTENCE_BATCH_SIZE).ToList();

                _logger.LogWarning(
                    "Processing batch {BatchNumber}/{TotalBatches} ({Count} routes)",
                    batchNumber,
                    (int)Math.Ceiling((double)changedAggregations.Count / PERSISTENCE_BATCH_SIZE),
                    batchAggregations.Count);

                // Create entities for each plan in this batch (1:N mapping - each Redis aggregation creates N entities)
                var batchEntities = new List<RouteAggregationEntity>();
                foreach (var planId in activePlanIds)
                {
                    foreach (var agg in batchAggregations)
                    {
                        // Override CreateEntityFromAggregation to set AggregationType='daily' and DailyPlanningId
                        var entity = CreateEntityFromAggregationForPlan(agg, snapshotTime, planId);
                        batchEntities.Add(entity);
                    }
                }

                if (!batchEntities.Any())
                {
                    continue;
                }

                // Upsert route aggregations for this batch
                // Key columns must match unique index: RouteKey + AggregationType + DailyPlanningId + CreatedAt
                var affectedRows = await routeAggregationRepository.UpsertRangeAsync(
                    batchEntities,
                    keyColumns: new System.Linq.Expressions.Expression<Func<TMS.PlanningService.Domain.Entities.RouteAggregationEntity, object>>[]
                    {
                        e => e.Id,
                        e => e.CreatedAt
                    },
                    updateColumns: new System.Linq.Expressions.Expression<Func<TMS.PlanningService.Domain.Entities.RouteAggregationEntity, object>>[]
                    {
                        e => e.SnapshotAt,
                        e => e.TotalOrders,
                        e => e.TotalItems,
                        e => e.TotalWeight,
                        e => e.TotalRealWeight,
                        e => e.TotalCalWeight,
                        e => e.TransportProviderBreakdownJson,
                        e => e.VehicleTypeBreakdownJson,
                        e => e.TransportMethodBreakdownJson,
                        e => e.AggregatedAt,
                        e => e.TotalDiffWeight
                    },
                    cancellationToken: cancellationToken
                );

                totalRoutesPersisted += batchEntities.Count;

                _logger.LogWarning(
                    "Batch {BatchNumber}: Upserted {Count} daily plan route aggregations ({AffectedRows} rows affected)",
                    batchNumber,
                    batchEntities.Count,
                    affectedRows);

                // Persist order details for this batch of routes
                var batchOrderEntities = new List<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity>();

                foreach (var aggregation in batchAggregations)
                {
                    if (!aggregation.OrderDetails.Any())
                    {
                        continue;
                    }

                    // For daily aggregations, we need to persist orders for each plan
                    foreach (var planId in activePlanIds)
                    {
                        // Find the persisted route aggregation entity ID for this plan
                        var routeEntity = batchEntities.FirstOrDefault(e => e.RouteKey == aggregation.RouteKey && e.DailyPlanningId == planId);
                        if (routeEntity == null)
                        {
                            _logger.LogWarning("Could not find route entity for {RouteKey} and plan {PlanId}, skipping order persistence", aggregation.RouteKey, planId);
                            continue;
                        }

                        // Create order entities for current snapshot
                        // CRITICAL: CreatedAt MUST match parent RouteAggregationEntity.CreatedAt for partition consistency
                        // Since OrderOnRoute now contains a list of Items (child mailers), we need to create one entity per item
                        var orderEntities = aggregation.OrderDetails.Values
                            .SelectMany(order => order.Items?.Select(item => new TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity
                            {
                                // Composite Foreign Key to RouteAggregation (both parts required)
                                RouteAggregationId = routeEntity.Id,
                                RouteAggregationCreatedAt = routeEntity.CreatedAt,
                                MailerId = item.MailerId,
                                ChildMailerId = item.ChildMailerId, // From individual item
                                Status = item.Status,
                                ServiceTypeId = order.ServiceTypeId,
                                ExtraService = order.ExtraService,
                                Weight = item.Weight, // Individual item weight
                                RealWeight = item.RealWeight, // Individual item real weight
                                CalWeight = item.CalWeight, // Individual item calculated weight
                                CreatedAt = routeEntity.CreatedAt, // Take from parent to ensure same partition
                                IsDeleted = item.IsDeleted
                            }) ?? Enumerable.Empty<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity>())
                            .ToList();

                        batchOrderEntities.AddRange(orderEntities);
                    }
                }

                // Bulk upsert all orders for this batch
                // Key columns must match unique index: composite FK + order identifiers + partition key
                // Each snapshot (RouteAggregationId, RouteAggregationCreatedAt) has its own set of orders
                if (batchOrderEntities.Any())
                {
                    var affectedOrderRows = await routeOrderRepository.UpsertRangeAsync(
                        batchOrderEntities,
                        keyColumns: new System.Linq.Expressions.Expression<Func<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity, object>>[]
                        {
                            e => e.RouteAggregationId,
                            e => e.RouteAggregationCreatedAt,  // Part of composite FK
                            e => e.MailerId,
                            e => e.ChildMailerId,
                            e => e.CreatedAt  // Partition key - required for partitioned table
                        },
                        updateColumns: new System.Linq.Expressions.Expression<Func<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity, object>>[]
                        {
                            e => e.Status,
                            e => e.Weight,
                            e => e.RealWeight,
                            e => e.CalWeight,
                            e => e.IsDeleted,
                        },
                        cancellationToken: cancellationToken
                    );

                    totalOrdersPersisted += batchOrderEntities.Count;

                    _logger.LogWarning(
                        "Batch {BatchNumber}: Upserted {OrderCount} order records ({AffectedRows} rows affected)",
                        batchNumber,
                        batchOrderEntities.Count,
                        affectedOrderRows);
                }

                // Update LastPersistedAt in Redis for this batch
                var batchRouteKeys = batchAggregations.Select(a => a.RouteKey).ToList();
                await UpdateLastPersistedAtAsync(batchRouteKeys, snapshotTime, cancellationToken);
            }

            _logger.LogInformation(
                "Completed batched persistence for {PlanCount} plans - Routes: {RouteCount}, Orders: {OrderCount}, Batches: {BatchCount}",
                activePlanIds.Count,
                totalRoutesPersisted,
                totalOrdersPersisted,
                batchNumber);

            return totalRoutesPersisted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to persist daily plan route aggregations");
            throw;
        }
    }

    /// <summary>
    /// Creates a RouteAggregationEntity for a specific daily plan
    /// Sets AggregationType='daily' and links to DailyPlanningId
    /// </summary>
    private RouteAggregationEntity CreateEntityFromAggregationForPlan(
        RouteAggregationSummary agg,
        DateTime snapshotTime,
        Guid dailyPlanId)
    {
        var entity = base.CreateEntityFromAggregation(agg, snapshotTime);

        // Override for daily planning
        entity.AggregationType = "daily";
        entity.DailyPlanningId = dailyPlanId;

        return entity;
    }
}

/// <summary>
/// DTO for storing daily plan totals in Redis
/// Optimized for high-performance real-time updates with 300K orders/day
/// Stores list of route keys for direct lookup without filtering
/// </summary>
public class DailyPlanTotals
{
    public Guid DailyPlanningId { get; set; }
    public decimal TotalWeight { get; set; }
    public decimal TotalRealWeight { get; set; }
    public DateTime UpdatedAt { get; set; }
    public decimal TotalDiffWeight => TotalRealWeight - TotalWeight;

    /// <summary>
    /// List of route keys that belong to this daily plan
    /// Used for direct lookup when recalculating plan totals
    /// Format: ["FromTime:ToTime:FromOfficeId:ToOfficeId", ...]
    /// </summary>
    public List<string> RouteKeys { get; set; } = new();
}

/// <summary>
/// Enhanced time window metadata with template information
/// Used for auto-generating daily plans when routes fall on next day
/// </summary>
public class TimeWindowMetadata
{
    /// <summary>
    /// Time window start (time-of-day only)
    /// </summary>
    public DateTime? FromTime { get; set; }

    /// <summary>
    /// Time window end (time-of-day only)
    /// </summary>
    public DateTime? ToTime { get; set; }

    /// <summary>
    /// Template ID used to generate daily plans
    /// </summary>
    public Guid TemplateId { get; set; }

    /// <summary>
    /// Company ID for the template
    /// </summary>
    public Guid CompanyId { get; set; }

    /// <summary>
    /// Current execution date this window was created for
    /// </summary>
    public DateOnly ExecutionDate { get; set; }
}

/// <summary>
/// Result of route validation against time windows
/// Indicates if route matches and whether next-day plan generation is needed
/// </summary>
public class RouteValidationResult
{
    public bool IsValid { get; set; }
    public bool RequiresNextDayPlan { get; set; }
    public TimeWindowMetadata? MatchedWindow { get; set; }
    public DateOnly? NextDayExecutionDate { get; set; }
}
