﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class LeadTimeTypeConfiguration : IEntityTypeConfiguration<LeadTimeType>
{
    public void Configure(EntityTypeBuilder<LeadTimeType> builder)
    {
        builder.ToTable("lead_time_type");

        // Primary Key
        builder.HasKey(x => x.Id)
               .HasName("pk_lead_time_type");

        builder.Property(x => x.Id)
            .HasColumnName("id")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.Name)
            .HasColumnName("name")
            .HasMaxLength(500);
    }
}
