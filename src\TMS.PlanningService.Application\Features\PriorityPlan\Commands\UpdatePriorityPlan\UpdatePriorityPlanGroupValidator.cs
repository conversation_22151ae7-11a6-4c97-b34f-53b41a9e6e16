﻿using FluentValidation;
using TMS.PlanningService.Contracts.PriorityPlan;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdatePriorityPlan;

public class UpdatePriorityPlanGroupValidator : AbstractValidator<UpdatePriorityPlanGroupRequest>
{
    public UpdatePriorityPlanGroupValidator()
    {
        RuleFor(x => x.PriorityPlanGroupAttributes)
        .NotNull()
        .NotEmpty()
        .WithMessage("Group properties cannot be null or empty")
        .Must(items => (items?.Count ?? 0) >= 1)
        .WithMessage("Priority route must have at least 1 propeties");

        //RuleFor(x => x.LogicOperator)
        //.NotEmpty()
        //.WithMessage(string.Format(ValidationMessages.Required, "LogicOperator"));

        // Todo: Add more validation rules for Details if needed like checking for valid PostOfficeId, Time ranges, etc.
        RuleForEach(x => x.PriorityPlanGroupAttributes)
        .SetValidator(new UpdatePriorityPlanGroupAttrValidator());
    }
}
