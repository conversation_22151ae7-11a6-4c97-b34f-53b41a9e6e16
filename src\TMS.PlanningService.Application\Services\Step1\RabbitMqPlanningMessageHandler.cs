﻿using System.Text.Json;
using MediatR;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Features.Planning.Commands.ReceivePlanning;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Application.Services.Step2.PriorityPlan;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernal.RabbitMq.Abstractions;

namespace TMS.PlanningService.Application.Services.Step1;

public class RabbitMqPlanningMessageHandler : IEventHandler<RabbitMqPlanningEvent>
{
    private readonly ILogger<RabbitMqPlanningMessageHandler> _logger;
    private readonly IMediator _mediator;
    private readonly IPlanningAggregationService _aggregationService;
    private readonly IPriorityPlanningAggregationService _priorityAggregationService;
    private readonly IDailyPlanningAggregationService _dailyPlanningAggregationService;

    // Semaphore to limit concurrent aggregation operations per instance
    // Prevents thread pool exhaustion and CPU thrashing under high load
    // Configuration: 150 per instance × 3 instances = 450 total system capacity
    // Note: Each instance needs its own protection even with load balancing
    //       because of message bursts, uneven distribution, and instance failures
    private static readonly SemaphoreSlim _aggregationSemaphore = new SemaphoreSlim(150, 150);

    public RabbitMqPlanningMessageHandler(
        ILogger<RabbitMqPlanningMessageHandler> logger,
        IMediator mediator,
        IPlanningAggregationService aggregationService,
        IPriorityPlanningAggregationService priorityAggregationService,
        IDailyPlanningAggregationService dailyPlanningAggregationService)
    {
        _logger = logger;
        _mediator = mediator;
        _aggregationService = aggregationService;
        _priorityAggregationService = priorityAggregationService;
        _dailyPlanningAggregationService = dailyPlanningAggregationService;
    }

    public async Task<bool> HandleAsync(
        RabbitMqPlanningEvent message,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var messageDetail = JsonSerializer.Serialize(message, new JsonSerializerOptions { WriteIndented = true });
            _logger.LogInformation($"RabbitMqPlanningMessageHandler message detail: {DateTime.UtcNow}, count: {message.PlanningData.Count}, message: {messageDetail}");

            foreach (var item in message.PlanningData)
            {
                _logger.LogInformation("PlanningMessageHandler received message - Message ID: {MessageId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                message.Id, item.MailerRouteMaster.MailerId, item.MailerRouteMaster.ChildMailerId);

                // Create the receive planning command
                var command = new ReceivePlanningCommand(
                    item.MailerRouteMaster,
                    item.MailerPlanRoutes,
                    item.MailerAdjustRoutes,
                    item.MailerActualRoutes
                );

                // Process the planning
                await _mediator.Send(command, cancellationToken);

#if BUILD_N
                return true;
#endif
                var isUsingAdjustRoutes = (item.MailerAdjustRoutes != null && item.MailerAdjustRoutes.Any());
                // Update route aggregations in real-time (fire-and-forget for non-blocking)
                // Use MailerAdjustRoutes if available, otherwise fallback to MailerPlanRoutes
                // Also include MailerActualRoutes to populate ActualFromTime and ActualToTime
                var routesToAggregate = isUsingAdjustRoutes
                    ? item.MailerAdjustRoutes.Cast<MailerPlanRoute>().ToList()
                    : item.MailerPlanRoutes?.Cast<MailerPlanRoute>().ToList() ?? new List<MailerPlanRoute>();

                var planRoutesAggregate = item.MailerPlanRoutes?.Cast<MailerPlanRoute>().ToList() ?? new List<MailerPlanRoute>();
                var actualRoutesAggregate = item.MailerActualRoutes?.Cast<MailerActualRoute>().ToList() ?? new List<MailerActualRoute>();

                //// Transform route times to represent transit segments [b] instead of origin schedules [a]
                //// NEW fromTime = current route's ToTime (departure from current office)
                //// NEW toTime = next route's FromTime (arrival at next office)
                //TransformRouteTimesForTransitSegments(routesToAggregate);
                //TransformRouteTimesForTransitSegments(planRoutesAggregate);
                //// Transform actual routes (cast to base type, objects modified by reference)
                //if (actualRoutesAggregate.Any())
                //{
                //    TransformRouteTimesForTransitSegments(actualRoutesAggregate.Cast<MailerPlanRoute>().ToList());
                //}

                if (routesToAggregate.Any())
                {
                    var mailerId = item.MailerRouteMaster.MailerId;
                    var uniqueRouteCount = routesToAggregate.Select(r => $"{r.FromPostOfficeId}:{r.ToPostOfficeId}").Distinct().Count();

                    // Create single-item event for OrderService fallback
                    // IMPORTANT: Don't pass entire message (which contains ALL items)
                    // Only pass the current item being processed to avoid sending unnecessary data to OrderService
                    var singleItemEvent = new RabbitMqPlanningEvent
                    {
                        Id = message.Id,
                        PlanningData = new List<PlanningWebhookRequest> { item },
                        Timestamp = message.Timestamp,
                        ProcessingAttempts = message.ProcessingAttempts
                    };

                    // Fire-and-forget with concurrency control
                    // Semaphore prevents thread pool exhaustion under high load
                    // Pass single-item event for fallback to OrderService if needed
                    // PlanRoutesAggregate used to recalculate orders/parcels against the adjusted route.
                    _ = UpdateRouteAggregationsWithThrottlingAsync(
                        routesToAggregate,
                        actualRoutesAggregate,
                        planRoutesAggregate,
                        mailerId,
                        uniqueRouteCount,
                        isUsingAdjustRoutes,
                        singleItemEvent);
                }

                _logger.LogInformation("Successfully processed planning from RabbitMq - Message ID: {MessageId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                    message.Id, item.MailerRouteMaster.MailerId, item.MailerRouteMaster.ChildMailerId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process planning from RabbitMq - Message ID: {MessageId}, Timestamp: {Timestamp}, EventType: {EventType}",
                message.Id, message.Timestamp, message.EventType);

            return false;
        }

        return true;
    }

    /// <summary>
    /// Throttles aggregation updates using semaphore to prevent CPU/thread pool exhaustion
    /// Fire-and-forget pattern with concurrency control
    /// </summary>
    private async Task UpdateRouteAggregationsWithThrottlingAsync(
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        List<MailerPlanRoute> planRoutes,
        string mailerId,
        int uniqueRouteCount,
        bool isUsingAdjustRoutes,
        RabbitMqPlanningEvent originalPlanningEvent)
    {
        // Check semaphore availability (non-blocking)
        var currentCount = _aggregationSemaphore.CurrentCount;

        // Progressive warning levels based on semaphore pressure
        if (currentCount == 0)
        {
            _logger.LogError(
                "CRITICAL: Semaphore fully exhausted (0/150 available) - order will wait indefinitely! MailerId: {MailerId}",
                mailerId);
        }
        else if (currentCount < 10)
        {
            _logger.LogError(
                "CRITICAL: Very high load - only {Available}/150 slots available. MailerId: {MailerId}",
                currentCount,
                mailerId);
        }
        else if (currentCount < 20)
        {
            _logger.LogWarning(
                "High aggregation load detected - {Available}/150 slots available. MailerId: {MailerId}",
                currentCount,
                mailerId);
        }

        // Wait for semaphore slot WITHOUT timeout
        // Better to wait and preserve data integrity than drop orders
        // RabbitMQ message visibility timeout is the ultimate safety valve
        var waitStart = DateTime.UtcNow;
        await _aggregationSemaphore.WaitAsync();
        var waitTime = (DateTime.UtcNow - waitStart).TotalMilliseconds;

        // Log if we had to wait a long time
        if (waitTime > 5000)  // > 5 seconds
        {
            _logger.LogWarning(
                "Aggregation waited {WaitMs}ms for semaphore slot - MailerId: {MailerId}",
                waitTime,
                mailerId);
        }

        try
        {
            await UpdateRouteAggregationsInParallelAsync(
                routes,
                actualRoutes,
                planRoutes,
                mailerId,
                uniqueRouteCount,
                isUsingAdjustRoutes,
                originalPlanningEvent);
        }
        finally
        {
            _aggregationSemaphore.Release();
        }
    }

    /// <summary>
    /// Updates all route aggregation services in parallel for maximum throughput
    /// Each service operates independently with its own error handling
    /// Non-critical failures are logged but don't block other services
    /// </summary>
    private async Task UpdateRouteAggregationsInParallelAsync(
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        List<MailerPlanRoute> planRoutes,
        string mailerId,
        int uniqueRouteCount,
        bool isUsingAdjustRoutes,
        RabbitMqPlanningEvent originalPlanningEvent)
    {
        var startTime = DateTime.UtcNow;

        // Run all three aggregation services in parallel
        var aggregationTasks = new[]
        {
            // 1. Regular route aggregations (all service types)
            UpdateRegularAggregationsAsync(routes, actualRoutes, planRoutes, mailerId, uniqueRouteCount, isUsingAdjustRoutes, originalPlanningEvent),

            // 2. Priority route aggregations (DE service type only)
            UpdatePriorityAggregationsAsync(routes, actualRoutes, planRoutes, mailerId, isUsingAdjustRoutes, originalPlanningEvent),

            // 3. Daily plan route aggregations (time-window based)
            UpdateDailyPlanAggregationsAsync(routes, actualRoutes, planRoutes, mailerId, isUsingAdjustRoutes, originalPlanningEvent)
        };

        // Wait for all to complete (even if some fail)
        await Task.WhenAll(aggregationTasks);

        var elapsed = (DateTime.UtcNow - startTime).TotalMilliseconds;
        _logger.LogInformation(
            "Completed all aggregation updates in parallel for MailerId: {MailerId} in {ElapsedMs}ms",
            mailerId,
            elapsed);
    }

    /// <summary>
    /// Updates regular route aggregations (all service types)
    /// </summary>
    private async Task UpdateRegularAggregationsAsync(
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        List<MailerPlanRoute> planRoutes,
        string mailerId,
        int uniqueRouteCount,
        bool isUsingAdjustRoutes,
        RabbitMqPlanningEvent originalPlanningEvent)
    {
        try
        {
            await _aggregationService.UpdateRouteAggregationsAsync(
                routes,
                actualRoutes,
                planRoutes,
                isUsingAdjustRoutes,
                CancellationToken.None,
                originalPlanningEvent);

            _logger.LogInformation(
                "[Regular] Updated route aggregations - MailerId: {MailerId}, UniqueRoutes: {RouteCount}, Source: {Source}",
                mailerId,
                uniqueRouteCount,
                isUsingAdjustRoutes ? "MailerAdjustRoute" : "MailerPlanRoute");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex,
                "[Regular] Failed to update route aggregations for MailerId: {MailerId}",
                mailerId);
        }
    }

    /// <summary>
    /// Updates priority route aggregations (DE service type only)
    /// </summary>
    private async Task UpdatePriorityAggregationsAsync(
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        List<MailerPlanRoute> planRoutes,
        string mailerId,
        bool isUsingAdjustRoutes,
        RabbitMqPlanningEvent originalPlanningEvent)
    {
        try
        {
            await _priorityAggregationService.UpdateRouteAggregationsAsync(
                routes,
                actualRoutes,
                planRoutes,
                isUsingAdjustRoutes,
                CancellationToken.None,
                originalPlanningEvent);

            var priorityRoutesCount = routes.Count();
            if (priorityRoutesCount > 0)
            {
                _logger.LogInformation(
                    "[Priority] Updated priority route aggregations - MailerId: {MailerId}, Routes: {RouteCount}, Source: {Source}",
                    mailerId,
                    priorityRoutesCount,
                    isUsingAdjustRoutes ? "MailerAdjustRoute" : "MailerPlanRoute");
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex,
                "[Priority] Failed to update priority route aggregations for MailerId: {MailerId}",
                mailerId);
        }
    }

    /// <summary>
    /// Updates daily plan route aggregations (time-window based matching)
    /// </summary>
    private async Task UpdateDailyPlanAggregationsAsync(
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        List<MailerPlanRoute> planRoutes,
        string mailerId,
        bool isUsingAdjustRoutes,
        RabbitMqPlanningEvent originalPlanningEvent)
    {
        try
        {
            await _dailyPlanningAggregationService.UpdateRouteAggregationsAsync(
                routes,
                actualRoutes,
                planRoutes,
                isUsingAdjustRoutes,
                CancellationToken.None,
                originalPlanningEvent);

            _logger.LogInformation(
                "[Daily] Updated daily plan route aggregations for MailerId: {MailerId}",
                mailerId);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex,
                "[Daily] Failed to update daily plan route aggregations for MailerId: {MailerId}",
                mailerId);
        }
    }

    /// <summary>
    /// Transforms route times from origin office schedules [a] to transit segment times [b]
    ///
    /// BEFORE (PMS Data - [a] schedule at origin office):
    /// Step 1: A→B (FromTime: 8:00, ToTime: 10:00)  - Schedule at office A
    /// Step 2: B→C (FromTime: 11:00, ToTime: 13:00) - Schedule at office B
    /// Step 3: C→D (FromTime: 14:00, ToTime: 16:00) - Schedule at office C
    ///
    /// AFTER (Transformed - [b] actual transit segment):
    /// Route A→B: FromTime = 10:00 (departs A), ToTime = 11:00 (arrives B)
    /// Route B→C: FromTime = 13:00 (departs B), ToTime = 14:00 (arrives C)
    /// Route C→D: FromTime = 16:00 (departs C), ToTime = null (last segment - no next arrival)
    /// </summary>
    private void TransformRouteTimesForTransitSegments(List<MailerPlanRoute> routes)
    {
        if (routes == null || !routes.Any())
            return;

        // Sort routes by Step to ensure correct sequence
        var sortedRoutes = routes.OrderBy(r => r.Step).ToList();

        // Transform each route 
        for (int i = 0; i < sortedRoutes.Count; i++)
        {
            var currentRoute = sortedRoutes[i];

            // NEW fromTime = current route's ToTime (when package leaves current office)
            var newFromTime = currentRoute.ToTime;

            // NEW toTime = next route's FromTime (when package arrives at next office)
            DateTime? newToTime = null;
            if (i < sortedRoutes.Count - 1)
            {
                var nextRoute = sortedRoutes[i + 1];
                newToTime = nextRoute.FromTime;
            }
            // For the last route, ToTime is null(no next office arrival time available)

            // Apply transformation
            currentRoute.FromTime = newFromTime;
            currentRoute.ToTime = newToTime;

            _logger.LogInformation(
                "Transformed route Step {Step}: {FromOffice}→{ToOffice} | FromTime: {FromTime}, ToTime: {ToTime}",
                currentRoute.Step,
                currentRoute.FromPostOfficeId,
                currentRoute.ToPostOfficeId,
                currentRoute.FromTime?.ToString("yyyy-MM-dd HH:mm") ?? "null",
                currentRoute.ToTime?.ToString("yyyy-MM-dd HH:mm") ?? "null");
        }
    }
}
