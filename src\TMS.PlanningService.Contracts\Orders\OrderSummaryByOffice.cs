namespace TMS.PlanningService.Contracts.Orders;

/// <summary>
/// Summary of orders grouped by office (received from OrderService)
/// </summary>
public class OrderSummaryByOffice
{
    public string CurrentOfficeId { get; set; } = string.Empty;
    public string CurrentOfficeName { get; set; } = string.Empty;
    public int TotalOrders { get; set; }
    public int TotalItems { get; set; }
    public decimal TotalWeight { get; set; }
    public decimal TotalRealWeight { get; set; }
    public decimal TotalCalWeight { get; set; }
    public List<OrderStatusCount> StatusBreakdown { get; set; } = new();
    public List<ServiceTypeCount> ServiceTypeBreakdown { get; set; } = new();
    public DateTime AnalyzedAt { get; set; } = DateTime.UtcNow;
    public DateTime? FromDate { get; set; }
    public DateTime? ToDate { get; set; }
}

public class OrderStatusCount
{
    public string StatusId { get; set; } = string.Empty;
    public string StatusName { get; set; } = string.Empty;
    public int Count { get; set; }
}

public class ServiceTypeCount
{
    public string ServiceTypeId { get; set; } = string.Empty;
    public string ServiceTypeName { get; set; } = string.Empty;
    public int Count { get; set; }
}
