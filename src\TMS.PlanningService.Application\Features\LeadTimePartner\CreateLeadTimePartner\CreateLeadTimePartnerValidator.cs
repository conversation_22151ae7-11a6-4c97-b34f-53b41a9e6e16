﻿using FluentValidation;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.CreatePriorityPlan;
using TMS.PlanningService.Contracts.LeadTimePartner;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.SharedKernel.Constants;

namespace TMS.PlanningService.Application.Features.LeadTimePartner.CreateLeadTimePartner;

public class CreateLeadTimePartnerValidator : AbstractValidator<CreateLeadTimePartnerRequest>
{
    public CreateLeadTimePartnerValidator()
    {
        RuleFor(x => x.LeadTimeId)
        .NotNull()
        .NotEmpty()
        .WithMessage(string.Format(ValidationMessages.Required, "LeadTimePartnerId"))
        .MaximumLength(50)
        .WithMessage(string.Format(ValidationMessages.MaxLength, "LeadTimePartnerId", 50));

        RuleFor(x => x.PartnerId)
        .NotNull()
        .WithMessage(string.Format(ValidationMessages.Required, "PartnerId"));

        RuleFor(x => x.FromTime)
        .NotNull()
        .WithMessage(string.Format(ValidationMessages.Required, "FromTime"));

        RuleFor(x => x.ToTime)
        .NotNull()
        .WithMessage(string.Format(ValidationMessages.Required, "ToTime"));

        RuleFor(x => x.SenderPostOffice)
        .NotNull()
        .NotEmpty()
        .WithMessage(string.Format(ValidationMessages.Required, "SenderPostOffice"))
        .MaximumLength(50)
        .WithMessage(string.Format(ValidationMessages.MaxLength, "SenderPostOffice", 50));

        RuleFor(x => x.ReceivePostOffice)
        .NotNull()
        .NotEmpty()
        .WithMessage(string.Format(ValidationMessages.Required, "ReceivePostOffice"))
        .MaximumLength(50)
        .WithMessage(string.Format(ValidationMessages.MaxLength, "ReceivePostOffice", 50));

    }
}
