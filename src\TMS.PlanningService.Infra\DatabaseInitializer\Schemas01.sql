-- ================================================================================
-- TMS Planning Service - PostgreSQL Partition Maintenance Scripts
-- Scripts for ongoing partition management, monitoring, and maintenance
-- PostgreSQL Version: 10+
-- Updated for snake_case naming convention
-- ================================================================================

-- ================================================================================
-- FUNCTION 1: ADD NEW MONTHLY PARTITIONS FOR PLANNING TABLES
-- ================================================================================
CREATE OR REPLACE FUNCTION add_monthly_planning_partitions(
    months_ahead INTEGER DEFAULT 3
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    start_date DATE;
    end_date DATE;
    partition_name TEXT;
    sql_command TEXT;
    counter INTEGER := 1;
    result_text TEXT := '';
BEGIN
    -- Find the latest partition end date for mailer_route_masters
    SELECT MAX(
        CASE
            WHEN pg_get_expr(c.relpartbound, c.oid) LIKE '%TO%' THEN
                (SELECT (regexp_matches(
                        pg_get_expr(c.relpartbound, c.oid),
                        'FROM \(''(\d{4}-\d{2}-\d{2})'
                ))[1])::DATE
            ELSE NULL
        END
    ) INTO start_date
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE c.relname LIKE 'mailer_route_masters_2%'
    AND n.nspname = current_schema();

    -- If no partitions exist, start from current month
    IF start_date IS NULL THEN
        start_date := date_trunc('month', CURRENT_DATE);
    END IF;

    result_text := format('Adding %s monthly partitions starting from %s', months_ahead, start_date);

    WHILE counter <= months_ahead LOOP
        end_date := start_date + INTERVAL '1 month';

        -- Create mailer_route_masters partition
        partition_name := format('mailer_route_masters_%s_%s',
            EXTRACT(YEAR FROM start_date)::TEXT,
            LPAD(EXTRACT(MONTH FROM start_date)::TEXT, 2, '0')
        );

        sql_command := format(
            'CREATE TABLE %I PARTITION OF mailer_route_masters FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );

        BEGIN
            EXECUTE sql_command;
            result_text := result_text || E'\n' || format('Created partition: %s', partition_name);
        EXCEPTION WHEN duplicate_table THEN
            result_text := result_text || E'\n' || format('Partition already exists: %s', partition_name);
        END;

        -- Create mailer_plan_routes partition
        partition_name := format('mailer_plan_routes_%s_%s',
            EXTRACT(YEAR FROM start_date)::TEXT,
            LPAD(EXTRACT(MONTH FROM start_date)::TEXT, 2, '0')
        );

        sql_command := format(
            'CREATE TABLE %I PARTITION OF mailer_plan_routes FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );

        BEGIN
            EXECUTE sql_command;
            result_text := result_text || E'\n' || format('Created partition: %s', partition_name);
        EXCEPTION WHEN duplicate_table THEN
            result_text := result_text || E'\n' || format('Partition already exists: %s', partition_name);
        END;

        -- Create mailer_adjust_routes partition
        partition_name := format('mailer_adjust_routes_%s_%s',
            EXTRACT(YEAR FROM start_date)::TEXT,
            LPAD(EXTRACT(MONTH FROM start_date)::TEXT, 2, '0')
        );

        sql_command := format(
            'CREATE TABLE %I PARTITION OF mailer_adjust_routes FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );

        BEGIN
            EXECUTE sql_command;
            result_text := result_text || E'\n' || format('Created partition: %s', partition_name);
        EXCEPTION WHEN duplicate_table THEN
            result_text := result_text || E'\n' || format('Partition already exists: %s', partition_name);
        END;

        -- Create mailer_actual_routes partition
        partition_name := format('mailer_actual_routes_%s_%s',
            EXTRACT(YEAR FROM start_date)::TEXT,
            LPAD(EXTRACT(MONTH FROM start_date)::TEXT, 2, '0')
        );

        sql_command := format(
            'CREATE TABLE %I PARTITION OF mailer_actual_routes FOR VALUES FROM (%L) TO (%L)',
            partition_name, start_date, end_date
        );

        BEGIN
            EXECUTE sql_command;
            result_text := result_text || E'\n' || format('Created partition: %s', partition_name);
        EXCEPTION WHEN duplicate_table THEN
            result_text := result_text || E'\n' || format('Partition already exists: %s', partition_name);
        END;

        start_date := end_date;
        counter := counter + 1;
    END LOOP;

    RETURN result_text;
END;
$$;

-- Example usage:
-- SELECT add_monthly_planning_partitions(6);

-- ================================================================================
-- FUNCTION 2: PARTITION STATISTICS AND MONITORING FOR PLANNING TABLES
-- ================================================================================
CREATE OR REPLACE VIEW planning_partition_statistics AS
SELECT
    n.nspname AS schema_name,
    c.relname AS partition_name,
    CASE
        WHEN c.relname LIKE 'mailer_route_masters_%' THEN 'mailer_route_masters'
        WHEN c.relname LIKE 'mailer_plan_routes_%' THEN 'mailer_plan_routes'
        WHEN c.relname LIKE 'mailer_adjust_routes_%' THEN 'mailer_adjust_routes'
        WHEN c.relname LIKE 'mailer_actual_routes_%' THEN 'mailer_actual_routes'

        ELSE 'unknown'
    END AS parent_table,
    pg_get_expr(c.relpartbound, c.oid) as partition_bounds,
    pg_size_pretty(pg_total_relation_size(c.oid)) AS size,
    pg_stat_get_tuples_inserted(c.oid) AS inserts,
    pg_stat_get_tuples_updated(c.oid) AS updates,
    pg_stat_get_tuples_deleted(c.oid) AS deletes,
    pg_stat_get_live_tuples(c.oid) AS live_tuples,
    pg_stat_get_dead_tuples(c.oid) AS dead_tuples
FROM pg_class c
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE (c.relname LIKE 'mailer_route_masters_%'
    OR c.relname LIKE 'mailer_plan_routes_%'
    OR c.relname LIKE 'mailer_adjust_routes_%'
    OR c.relname LIKE 'mailer_actual_routes_%')
    AND c.relkind = 'r'   -- Regular tables only
    AND n.nspname = current_schema()
ORDER BY parent_table, c.relname;

-- Query partition statistics:
-- SELECT * FROM planning_partition_statistics;

-- ================================================================================
-- FUNCTION 3: ARCHIVE OLD PLANNING PARTITIONS
-- ================================================================================
CREATE OR REPLACE FUNCTION archive_old_planning_partitions(
    months_to_keep INTEGER DEFAULT 12
)
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    cutoff_date DATE;
    partition_record RECORD;
    partition_date DATE;
    sql_command TEXT;
    result_text TEXT := '';
    archived_count INTEGER := 0;
BEGIN
    cutoff_date := date_trunc('month', CURRENT_DATE) - (months_to_keep || ' months')::INTERVAL;
    result_text := format('Archiving partitions older than %s (keeping %s months)', cutoff_date, months_to_keep);

    -- Create archive schema if it doesn't exist
	EXECUTE 'CREATE SCHEMA IF NOT EXISTS archive';

    -- Loop through all planning partitions
    FOR partition_record IN
        SELECT c.relname, n.nspname,
               CASE
                   WHEN c.relname LIKE 'mailer_route_masters_%' THEN 'mailer_route_masters'
                   WHEN c.relname LIKE 'mailer_plan_routes_%' THEN 'mailer_plan_routes'
                   WHEN c.relname LIKE 'mailer_adjust_routes_%' THEN 'mailer_adjust_routes'
                   WHEN c.relname LIKE 'mailer_actual_routes_%' THEN 'mailer_actual_routes'
               END AS parent_table
        FROM pg_class c
        JOIN pg_namespace n ON c.relnamespace = n.oid
        WHERE (c.relname LIKE 'mailer_route_masters_2%'
            OR c.relname LIKE 'mailer_plan_routes_2%'
            OR c.relname LIKE 'mailer_adjust_routes_2%'
            OR c.relname LIKE 'mailer_actual_routes_2%')
            AND c.relkind = 'r'
            AND n.nspname = current_schema()
            AND c.relname NOT LIKE '%_default'
    LOOP
        -- Extract date from partition name (format: table_YYYY_MM)
        BEGIN
            partition_date := (
			    (regexp_matches(partition_record.relname, '_(\d{4})_(\d{2})$'))[1] || '-' ||
			    (regexp_matches(partition_record.relname, '_(\d{4})_(\d{2})$'))[2] || '-01'
			)::DATE;

            -- Archive if partition is older than cutoff
            IF partition_date < cutoff_date THEN

                 -- Create archive table
				sql_command := format(
						'CREATE TABLE IF NOT EXISTS archive.%I AS TABLE %I WITH NO DATA',
						partition_record.relname || '_archived',
						partition_record.relname
				);
				EXECUTE sql_command;
					
				-- Copy data to archive
				sql_command := format(
						'INSERT INTO archive.%I SELECT * FROM %I',
						partition_record.relname || '_archived',
						partition_record.relname
				);
				EXECUTE sql_command;
					
				result_text := result_text || E'\n' || format('Archived partition: %s', partition_record.relname);
				archived_count := archived_count + 1;

            END IF;

        EXCEPTION WHEN OTHERS THEN
            result_text := result_text || E'\n' || format('Error processing partition %s: %s',
                partition_record.relname, SQLERRM);
        END;
    END LOOP;

    result_text := result_text || E'\n' || format('Total partitions processed: %s', archived_count);
    RETURN result_text;
END;
$$;

-- Example usage:
-- SELECT archive_old_planning_partitions(12); -- Keep 12 months of data

-- ================================================================================
-- FUNCTION 4: PLANNING PARTITION HEALTH CHECK
-- ================================================================================
CREATE OR REPLACE FUNCTION planning_partition_health_check()
RETURNS TABLE (
    check_name TEXT,
    status TEXT,
    details TEXT,
    recommendation TEXT
)
LANGUAGE plpgsql
AS $$
DECLARE
    future_partitions_count INTEGER;
    missing_partitions TEXT := '';
    large_partitions TEXT := '';
    empty_partitions TEXT := '';
    partition_record RECORD;
    expected_date DATE;
    current_month DATE;
BEGIN
    current_month := date_trunc('month', CURRENT_DATE);

    -- Check 1: Future partitions availability
    SELECT COUNT(*) INTO future_partitions_count
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE c.relname LIKE 'mailer_route_masters_2%'
        AND c.relkind = 'r'
        AND n.nspname = current_schema()
        AND c.relname NOT LIKE '%_default'
        AND (
			    (regexp_match(c.relname, '_(\d{4})_(\d{2})$'))[1] || '-' ||
			    (regexp_match(c.relname, '_(\d{4})_(\d{2})$'))[2] || '-01'
			)::DATE >= current_month;

    IF future_partitions_count < 3 THEN
        RETURN QUERY SELECT
            'Future Partitions'::TEXT,
            'WARNING'::TEXT,
            format('Only %s future partitions available', future_partitions_count)::TEXT,
            'Run add_monthly_planning_partitions() to create more partitions'::TEXT;
    ELSE
        RETURN QUERY SELECT
            'Future Partitions'::TEXT,
            'OK'::TEXT,
            format('%s future partitions available', future_partitions_count)::TEXT,
            'No action needed'::TEXT;
    END IF;

    -- Check 2: Partition size monitoring
    FOR partition_record IN
        SELECT c.relname, pg_size_pretty(pg_total_relation_size(c.oid)) as size_pretty,
               pg_total_relation_size(c.oid) as size_bytes
        FROM pg_class c
        JOIN pg_namespace n ON c.relnamespace = n.oid
        WHERE (c.relname LIKE 'mailer_route_masters_%'
            OR c.relname LIKE 'mailer_plan_routes_%'
            OR c.relname LIKE 'mailer_adjust_routes_%'
            OR c.relname LIKE 'mailer_actual_routes_%')
            AND c.relkind = 'r'
            AND n.nspname = current_schema()
            AND c.relname NOT LIKE '%_default'
    LOOP
        -- Flag partitions larger than 1GB
        IF partition_record.size_bytes > 1073741824 THEN -- 1GB
            large_partitions := large_partitions || partition_record.relname || ' (' || partition_record.size_pretty || '), ';
        END IF;

        -- Flag completely empty partitions older than current month
        IF partition_record.size_bytes = 0 THEN
            empty_partitions := empty_partitions || partition_record.relname || ', ';
        END IF;
    END LOOP;

    -- Report large partitions
    IF large_partitions != '' THEN
        RETURN QUERY SELECT
            'Large Partitions'::TEXT,
            'INFO'::TEXT,
            'Partitions > 1GB: ' || rtrim(large_partitions, ', ')::TEXT,
            'Consider archiving or monitoring performance'::TEXT;
    END IF;

    -- Report empty partitions
    IF empty_partitions != '' THEN
        RETURN QUERY SELECT
            'Empty Partitions'::TEXT,
            'INFO'::TEXT,
            'Empty partitions: ' || rtrim(empty_partitions, ', ')::TEXT,
            'These partitions contain no data'::TEXT;
    END IF;

    -- Check 3: Partition pruning effectiveness
    RETURN QUERY SELECT
        'Partition Pruning'::TEXT,
        'INFO'::TEXT,
        'Check query plans to ensure partition pruning is working'::TEXT,
        'Use EXPLAIN to verify WHERE clauses on created_date utilize partition pruning'::TEXT;

END;
$$;

-- Example usage:
-- SELECT * FROM planning_partition_health_check();

-- ================================================================================
-- FUNCTION 5: PLANNING DATA SUMMARY REPORT
-- ================================================================================
CREATE OR REPLACE FUNCTION planning_data_summary_report()
RETURNS TABLE (
    metric_name TEXT,
    metric_value TEXT,
    last_updated TIMESTAMPTZ
)
LANGUAGE plpgsql
AS $$
DECLARE
    total_masters BIGINT;
    total_plan_routes BIGINT;
    total_adjust_routes BIGINT;
    total_actual_routes BIGINT;
    oldest_record TIMESTAMPTZ;
    newest_record TIMESTAMPTZ;
    active_partitions INTEGER;
BEGIN
    -- Get total record counts
    EXECUTE 'SELECT COUNT(*) FROM mailer_route_masters' INTO total_masters;
    EXECUTE 'SELECT COUNT(*) FROM mailer_plan_routes' INTO total_plan_routes;
    EXECUTE 'SELECT COUNT(*) FROM mailer_adjust_routes' INTO total_adjust_routes;
    EXECUTE 'SELECT COUNT(*) FROM mailer_actual_routes' INTO total_actual_routes;

    -- Get date range
    EXECUTE 'SELECT MIN(created_date) FROM mailer_route_masters' INTO oldest_record;
    EXECUTE 'SELECT MAX(created_date) FROM mailer_route_masters' INTO newest_record;

    -- Count active partitions
    SELECT COUNT(*) INTO active_partitions
    FROM pg_class c
    JOIN pg_namespace n ON c.relnamespace = n.oid
    WHERE (c.relname LIKE 'mailer_route_masters_%'
        OR c.relname LIKE 'mailer_plan_routes_%'
        OR c.relname LIKE 'mailer_adjust_routes_%'
        OR c.relname LIKE 'mailer_actual_routes_%')
        AND c.relkind = 'r'
        AND n.nspname = current_schema()
        AND c.relname NOT LIKE '%_default';

    -- Return summary metrics
    RETURN QUERY SELECT 'Total Route Masters'::TEXT, total_masters::TEXT, NOW();
    RETURN QUERY SELECT 'Total Plan Routes'::TEXT, total_plan_routes::TEXT, NOW();
    RETURN QUERY SELECT 'Total Adjust Routes'::TEXT, total_adjust_routes::TEXT, NOW();
    RETURN QUERY SELECT 'Total Actual Routes'::TEXT, total_actual_routes::TEXT, NOW();
    RETURN QUERY SELECT 'Oldest Record'::TEXT, COALESCE(oldest_record::TEXT, 'No data'), NOW();
    RETURN QUERY SELECT 'Newest Record'::TEXT, COALESCE(newest_record::TEXT, 'No data'), NOW();
    RETURN QUERY SELECT 'Active Partitions'::TEXT, active_partitions::TEXT, NOW();

END;
$$;

-- Example usage:
-- SELECT * FROM planning_data_summary_report();

-- ================================================================================
-- AUTOMATED MAINTENANCE PROCEDURES
-- ================================================================================

-- Create a procedure to run regular maintenance
CREATE OR REPLACE FUNCTION run_planning_maintenance()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    result_text TEXT := 'Planning Service Maintenance Results:' || E'\n';
    maintenance_result TEXT;
BEGIN
    -- Add future partitions
    SELECT add_monthly_planning_partitions(3) INTO maintenance_result;
    result_text := result_text || E'\n=== PARTITION CREATION ===' || E'\n' || maintenance_result || E'\n';

    -- Run health check
    result_text := result_text || E'\n=== HEALTH CHECK ===' || E'\n';
    FOR maintenance_result IN
        SELECT check_name || ': ' || status || ' - ' || details
        FROM planning_partition_health_check()
    LOOP
        result_text := result_text || maintenance_result || E'\n';
    END LOOP;

    -- Log maintenance run
    result_text := result_text || E'\n=== MAINTENANCE COMPLETED ===' || E'\n' ||
                   'Maintenance completed at: ' || NOW()::TEXT;

    RETURN result_text;
END;
$$;

-- Example usage:
-- SELECT run_planning_maintenance();

-- ================================================================================
-- MAINTENANCE SCHEDULING SUGGESTIONS
-- ================================================================================
-- Schedule monthly partition creation (example using pg_cron extension)
-- SELECT cron.schedule('planning-partitions', '0 0 1 * *', 'SELECT add_monthly_planning_partitions(3);');

-- Schedule quarterly partition archival (example using pg_cron extension)
-- SELECT cron.schedule('planning-archive', '0 2 1 */3 *', 'SELECT archive_old_planning_partitions(12);');

-- Schedule weekly health checks (example using pg_cron extension)
-- SELECT cron.schedule('planning-health', '0 6 * * 1', 'SELECT planning_partition_health_check();');

-- Or use external cron jobs:
-- 0 0 1 * * psql -d planning_db -c "SELECT add_monthly_planning_partitions(3);"
-- 0 2 1 */3 * psql -d planning_db -c "SELECT archive_old_planning_partitions(12);"
-- 0 6 * * 1 psql -d planning_db -c "SELECT * FROM planning_partition_health_check();"

-- ================================================================================
-- MONITORING VIEWS FOR OPERATIONAL INSIGHT
-- ================================================================================

-- Current month partition sizes
CREATE OR REPLACE VIEW current_month_partition_sizes AS
SELECT
    CASE
        WHEN c.relname LIKE 'mailer_route_masters_%' THEN 'mailer_route_masters'
        WHEN c.relname LIKE 'mailer_plan_routes_%' THEN 'mailer_plan_routes'
        WHEN c.relname LIKE 'mailer_adjust_routes_%' THEN 'mailer_adjust_routes'
        WHEN c.relname LIKE 'mailer_actual_routes_%' THEN 'mailer_actual_routes'
    END AS parent_table,
    c.relname AS partition_name,
    pg_size_pretty(pg_total_relation_size(c.oid)) AS size,
    pg_stat_get_live_tuples(c.oid) AS live_tuples
FROM pg_class c
JOIN pg_namespace n ON c.relnamespace = n.oid
WHERE (c.relname LIKE 'mailer_route_masters_%'
    OR c.relname LIKE 'mailer_plan_routes_%'
    OR c.relname LIKE 'mailer_adjust_routes_%'
    OR c.relname LIKE 'mailer_actual_routes_%')
    AND c.relkind = 'r'
    AND n.nspname = current_schema()
    AND c.relname LIKE '%' || to_char(CURRENT_DATE, 'YYYY_MM')
ORDER BY parent_table, c.relname;

-- Partition growth trend (last 6 months)
CREATE OR REPLACE VIEW partition_growth_trend AS
SELECT
    CASE
        WHEN c.relname LIKE 'mailer_route_masters_%' THEN 'mailer_route_masters'
        WHEN c.relname LIKE 'mailer_plan_routes_%' THEN 'mailer_plan_routes'
        WHEN c.relname LIKE 'mailer_adjust_routes_%' THEN 'mailer_adjust_routes'
        WHEN c.relname LIKE 'mailer_actual_routes_%' THEN 'mailer_actual_routes'
    END AS parent_table,
    m[1] || '_' || m[2] AS year_month,
    pg_total_relation_size(c.oid) AS size_bytes,
    pg_size_pretty(pg_total_relation_size(c.oid)) AS size_pretty,
    pg_stat_get_live_tuples(c.oid) AS live_tuples
FROM pg_class c
JOIN pg_namespace n ON c.relnamespace = n.oid
CROSS JOIN LATERAL regexp_matches(c.relname, '_(\d{4})_(\d{2})$') m
WHERE (c.relname LIKE 'mailer_route_masters_%'
    OR c.relname LIKE 'mailer_plan_routes_%'
    OR c.relname LIKE 'mailer_adjust_routes_%'
    OR c.relname LIKE 'mailer_actual_routes_%')
    AND c.relkind = 'r'
    AND n.nspname = current_schema()
    AND c.relname NOT LIKE '%_default'
    AND to_date(m[1] || '-' || m[2] || '-01', 'YYYY-MM-DD') >= CURRENT_DATE - INTERVAL '6 months'
ORDER BY parent_table, year_month;


-- ================================================================================
-- NOTES AND BEST PRACTICES
-- ================================================================================
/*
PLANNING SERVICE PARTITION MAINTENANCE BEST PRACTICES:

1. REGULAR MONITORING:
   - Run planning_partition_health_check() weekly
   - Monitor partition sizes with planning_partition_statistics view
   - Check partition_growth_trend monthly for capacity planning

2. AUTOMATED MAINTENANCE:
   - Schedule add_monthly_planning_partitions() to run monthly
   - Archive old partitions quarterly or based on retention policy
   - Use run_planning_maintenance() for comprehensive maintenance

3. PERFORMANCE OPTIMIZATION:
   - Ensure queries use created_date in WHERE clauses for partition pruning
   - Monitor for queries that scan all partitions
   - Consider partition-wise joins for large analytical queries

4. BACKUP CONSIDERATIONS:
   - Backup detached partitions before dropping them
   - Consider partition-level backup strategies for large datasets
   - Test restore procedures on partition level

5. ALERTING:
   - Set up alerts for partition health check warnings
   - Monitor partition sizes approaching storage limits
   - Alert on failed partition creation attempts

6. MAINTENANCE WINDOWS:
   - Schedule maintenance during low-traffic periods
   - Test maintenance procedures on staging environment first
   - Keep maintenance logs for troubleshooting

-- Updated for snake_case naming convention compatibility
-- All functions now work with mailer_route_masters, mailer_plan_routes, mailer_adjust_routes, mailer_actual_routes
*/