﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.PriorityRouteAggregation.Queries.GetPriorityRouteAggregationByKey;
using TMS.PlanningService.Application.Features.PriorityRouteAggregation.Queries.GetPriorityRouteAggregations;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/priority-planning-aggregates")]
[Produces("application/json")]
public class PriorityPlanningAggregateController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<PriorityPlanningAggregateController> _logger;

    public PriorityPlanningAggregateController(
        IMediator mediator,
        ILogger<PriorityPlanningAggregateController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all priority planning aggregations (DE service type only) with optional filters and pagination
    /// Returns express delivery routes sorted by priority
    /// </summary>
    /// <param name="request">Filter and pagination parameters</param>
    /// <returns>Paginated list of priority planning aggregations</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(PagedResult<RouteAggregationDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResult<RouteAggregationDto>>> GetPriorityPlanningAggregates([FromBody] GetPriorityPlanningAggregationsRequest request)
    {
        _logger.LogInformation(
            "Getting priority planning aggregates (DE only) - Page: {Page}, PageSize: {PageSize}",
            request.Page,
            request.PageSize);

        var query = new GetPriorityRouteAggregationsQuery(request);
        var result = await _mediator.Send(query);

        return Ok(result);
    }

    /// <summary>
    /// Get priority planning aggregation by routeKey (DE service type only)
    /// Returns detailed aggregation for a specific express route with paginated order details
    /// </summary>
    /// <param name="routeKey">Route identifier (FromTime:ToTime:FromOffice:ToOffice)</param>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Page size (default: 20)</param>
    /// <param name="searchTerm">Optional search term to filter orders by MailerId</param>
    /// <returns>Priority planning aggregation details with paginated orders or 404 if not found</returns>
    [HttpGet("{routeKey}")]
    [ProducesResponseType(typeof(PriorityRouteAggregationDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<PriorityRouteAggregationDto>> GetPriorityPlanningAggregateByRoute(
        string routeKey,
        [FromQuery] int page = 1,
        [FromQuery] int pageSize = 20,
        [FromQuery] string? searchTerm = null)
    {
        _logger.LogInformation(
            "Getting priority planning aggregate for route {RouteKey} - Page: {Page}, PageSize: {PageSize}, SearchTerm: {SearchTerm}",
            routeKey,
            page,
            pageSize,
            searchTerm);

        var query = new GetPriorityRouteAggregationByKeyQuery(routeKey, page, pageSize, searchTerm);
        var result = await _mediator.Send(query);

        if (result == null)
        {
            _logger.LogWarning(
                "Priority planning aggregate not found for route {RouteKey}",
                routeKey);
            return NotFound();
        }

        return Ok(result);
    }
}
