﻿using FluentValidation;
using TMS.SharedKernel.Constants;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.DeletePriorityPlan;

public class DeletePlanningTemplateValidator : AbstractValidator<DeletePriorityPlanCommand>
{
    public DeletePlanningTemplateValidator()
    {
        RuleFor(c => c.Id)
           .NotEmpty()
           .WithMessage(string.Format(ValidationMessages.Required, "Id"))
           .WithErrorCode("Id");
    }
}
