﻿using Mapster;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Orders;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Common.Mappings;

public  class MappingConfig : IRegister
{
    public void Register(TypeAdapterConfig config)
    { 
        config.NewConfig<OrderStatus, OrderStatusOptionsResponse>()
            .Map(dest => dest.Code, src => src.StatusId) // id = Code
            .Map(dest => dest.Name, src => src.StatusName);

    }
}
