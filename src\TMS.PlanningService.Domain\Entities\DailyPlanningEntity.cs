﻿using System.ComponentModel.DataAnnotations.Schema;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Domain.Entities;

/// <summary>
/// Daily planning generated from PlanningTemplate
/// Represents the actual plan for a specific date
/// </summary>
[Table("real_plans", Schema = "public")]
public class DailyPlanningEntity : AuditableSoftDeleteEntity
{
    [Column("company_id")]
    public Guid CompanyId { get; set; }

    [Column("planning_template_id")]
    public Guid PlanningTemplateId { get; set; }

    [Column("route_id")]
    public Guid RouteId { get; set; }

    [Column("execution_date")]
    public DateOnly ExecutionDate { get; set; }

    [Column("code")]
    public string Code { get; set; } = string.Empty;

    [Column("name")]
    public string Name { get; set; } = string.Empty;

    [Column("total_distance")]
    public double TotalDistance { get; set; }

    [Column("total_duration")]
    public double TotalDuration { get; set; }

    [Column("office_count")]
    public int OfficeCount { get; set; }

    [Column("vehicle_type_id")]
    public Guid VehicleTypeId { get; set; }

    [Column("route_code")]
    public string RouteCode { get; set; } = string.Empty;

    [Column("post_office_codes")]
    public string PostOfficeCodes { get; set; } = string.Empty;

    [Column("priority_number")]
    public int PriorityNumber { get; set; } = 1;

    [Column("status")]
    public string Status { get; set; } = "PENDING"; // PENDING, IN_PROGRESS, COMPLETED, CANCELLED

    [Column("actual_start_time")]
    public DateTime? ActualStartTime { get; set; }

    [Column("actual_end_time")]
    public DateTime? ActualEndTime { get; set; }

    [Column("is_active")]
    public bool IsActive { get; set; } = true;

    [Column("total_weight")]
    public decimal TotalWeight { get; set; }

    [Column("total_real_weight")]
    public decimal TotalRealWeight { get; set; }

    [Column("total_diff_weight")]
    public decimal TotalDiffWeight { get; set; }

    // Navigation properties
    public PlanningTemplateEntity? PlanningTemplate { get; set; }

    /// <summary>
    /// Route aggregations for this daily plan (uses unified RouteAggregationEntity)
    /// All aggregations should have AggregationType='daily' and DailyPlanningId=this.Id
    /// </summary>
    public ICollection<RouteAggregationEntity> RouteAggregations { get; set; } = new List<RouteAggregationEntity>();
}
