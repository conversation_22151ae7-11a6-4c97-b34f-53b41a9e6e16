﻿using System.Reflection;
using FluentValidation;
using Mapster;
using MapsterMapper;
using MediatR;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Common.Behaviors;
using TMS.PlanningService.Application.Common.Mappings;
using TMS.PlanningService.Application.Services.Implements;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Application.Services.Orders;
using TMS.PlanningService.Application.Services.RouteOptimization;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Application.Services.Step2.PriorityPlan;
using TMS.PlanningService.Application.Services.Step3;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.SharedKernal.Caching;
using TMS.SharedKernal.SmoothRedis;
using ExtraService = TMS.PlanningService.Domain.Entities.Metadata.ExtraService;

namespace TMS.PlanningService.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var assembly = Assembly.GetExecutingAssembly();

        // MediatR
        services.AddMediatR(cfg =>
        {
            cfg.RegisterServicesFromAssembly(assembly);
            cfg.AddBehavior(typeof(IPipelineBehavior<,>), typeof(ValidationBehavior<,>));
        });

        // FluentValidation
        services.AddValidatorsFromAssembly(assembly);

        // Mapster
        // Use the global instance of TypeAdapterConfig. Creating a 'new TypeAdapterConfig()'
        // here would result in an empty configuration for the mapper.
        var config = TypeAdapterConfig.GlobalSettings;
        // This is a more robust way to register all your mappings that implement IRegister
        config.Scan(assembly);
        services.AddSingleton(config);
        services.AddScoped<IMapper, ServiceMapper>();
         
        // Register Kafka-based planning queue service as singleton
        // For in-memory mode, replace with: services.AddSingleton<IPlanningQueueService, InMemoryPlanningQueueService>();
        services.AddSingleton<IPlanningQueueService, KafkaPlanningQueueService>();

        // Register PlanningTemplateCalculateService
        services.AddScoped<IPlanningTemplateCalculateService, PlanningTemplateCalculateService>();

        // Register ExternalDataService
        services.AddScoped<IExternalDataService, ExternalDataService>();
        // HASH-BASED METADATA OPTIMIZATION - Factory registrations
        // Creates RouteMetadataManager and RouteChangeTracker instances with different Redis key prefixes
        services.AddSingleton<Func<string, IRouteMetadataManager>>(sp => (keyPrefix) =>
        {
            var redis = sp.GetRequiredService<ISmoothRedis>();
            var loggerFactory = sp.GetRequiredService<ILoggerFactory>();
            return new RouteMetadataManager(
                redis,
                loggerFactory.CreateLogger<RouteMetadataManager>(),
                $"{keyPrefix}:route-metadata"
            );
        });

        // Register Order Aggregation Planning Service
        services.AddScoped<IOrderAggregationPlanningService, OrderAggregationPlanningService>();

        // Register Planning Aggregation Service as singleton for real-time updates via RabbitMQ
        services.AddSingleton<IPlanningAggregationService, PlanningAggregationService>(sp =>
        {
            var metadataManagerFactory = sp.GetRequiredService<Func<string, IRouteMetadataManager>>();

            return new PlanningAggregationService(
                sp.GetRequiredService<ILogger<PlanningAggregationService>>(),
                sp.GetRequiredService<ISmoothRedis>(),
                sp.GetRequiredService<IOrderDataService>(),
                metadataManagerFactory("planning")
            );
        });

        // Register Priority Planning Aggregation Service (DE service type only) as singleton
        services.AddSingleton<IPriorityPlanningAggregationService, PriorityPlanningAggregationService>(sp =>
        {
            var metadataManagerFactory = sp.GetRequiredService<Func<string, IRouteMetadataManager>>();

            return new PriorityPlanningAggregationService(
                sp.GetRequiredService<ILogger<PriorityPlanningAggregationService>>(),
                sp.GetRequiredService<ISmoothRedis>(),
                sp.GetRequiredService<IOrderDataService>(),
                sp.GetRequiredService<IMetadataCacheService>(),
                metadataManagerFactory("planning:priority")
            );
        });

        // Register Daily Planning Aggregation Service for daily execution plans as singleton
        // Real-time aggregation uses Redis only. Database operations (persist, update totals)
        // are called by jobs that pass scoped repositories as parameters
        services.AddSingleton<IDailyPlanningAggregationService, DailyPlanningAggregationService>(sp =>
        {
            var metadataManagerFactory = sp.GetRequiredService<Func<string, IRouteMetadataManager>>();

            return new DailyPlanningAggregationService(
                sp.GetRequiredService<ILogger<DailyPlanningAggregationService>>(),
                sp.GetRequiredService<ISmoothRedis>(),
                sp.GetRequiredService<IOrderDataService>(),
                sp.GetRequiredService<IServiceProvider>(),
                metadataManagerFactory("daily-planning")
            );
        });

        // Register Order Data Service for enriching route aggregations with order metrics
        services.AddSingleton<IOrderDataService, OrderDataService>();

        // Register Remove Order On Route Service for removing orders from routes (reusable across handlers)
        services.AddScoped<IRemoveOrderOnRoute, RemoveOrderOnRouteService>();

        // Register Daily Execution Plan Generator Service for daily plan creation from templates
        services.AddScoped<IDailyPlanningGeneratorService, DailyPlanningGeneratorService>();

        // Note: DailyExecutionPlanAggregateService has been removed - replaced by DailyPlanningAggregationService
        // Real-time aggregation is now handled by RabbitMQ events via DailyPlanningAggregationService
        // Persistence to DB is handled by DailyPlanRouteAggregationSnapshotJob

        // Register Planning Pull service
        services.AddScoped<IPmsPullingService, PmsPullingService>();

        // Register Metadata Caching Service with entity type configurations
        services.AddMetadataCaching(config =>
        {
            config.RegisterEntityType<ServiceType>("metadata:servicetypes")
                  .RegisterEntityType<ExtraService>("metadata:extraservices")
                  .RegisterEntityType<OrderStatus>("metadata:orderstatuses")
                  .RegisterEntityType<PriorityPlan>("metadata:priority-plans")
                  .RegisterEntityType<LeadTimeType>("metadata:lead-time-types")
                  .RegisterEntityType<TransportMethodType>("metadata:transport-method-types")
                  .RegisterEntityType<TransportVehicleType>("metadata:transport-vehicle-types");
        });

        return services;
    }
}
