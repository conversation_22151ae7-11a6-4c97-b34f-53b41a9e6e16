using System.Linq;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NPOI.SS.Formula.Functions;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Common.Queries;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Application.Services.Step2.PriorityPlan;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Features.PriorityRouteAggregation.Queries.GetPriorityRouteAggregations;

/// <summary>
/// Handler for getting priority route aggregations (DE service type only) using hybrid Redis + PostgreSQL pagination
/// Returns aggregated planning data for express delivery routes
///
/// PATTERN USAGE:
/// - Inherits from RouteAggregationQueryHandlerBase (which provides common helper methods)
/// - Implements IRequestHandler for MediatR registration
/// - PERFORMANCE OPTIMIZATION: Filters/paginates on metadata first, then fetches only needed aggregations
/// - Falls back to PostgreSQL for historical priority aggregations
/// </summary>
public class GetPriorityRouteAggregationsQueryHandler
    : RouteAggregationQueryHandlerBase<
        GetPriorityRouteAggregationsQuery,
        PriorityRouteAggregationDto>,
      IRequestHandler<GetPriorityRouteAggregationsQuery, PagedResult<PriorityRouteAggregationDto>>
{
    private readonly IPriorityPlanningAggregationService _priorityAggregationService;
    private readonly ILogger<GetPriorityRouteAggregationsQueryHandler> _logger;
    private readonly IRouteServiceApi _routeServiceApi;
    private readonly IBaseRepository<RouteAggregationEntity> _routeAggregationRepository;
    private readonly IMetadataCacheService _metadataCacheService;

    // Override: Configure Redis page coverage (priority data typically has good Redis coverage)
    protected override int RedisExpectedPageCoverage => 3;

    // Override: Provide logger for base class
    protected override ILogger Logger => _logger;

    // Override: Provide route service API for base class
    protected override IRouteServiceApi RouteServiceApi => _routeServiceApi;

    // Override: Provide metadata cache service for base class
    protected override TMS.SharedKernal.Caching.IMetadataCacheService MetadataCacheService => _metadataCacheService;

    public GetPriorityRouteAggregationsQueryHandler(
        IPriorityPlanningAggregationService priorityAggregationService,
        ILogger<GetPriorityRouteAggregationsQueryHandler> logger,
        IRouteServiceApi routeServiceApi,
        IBaseRepository<RouteAggregationEntity> routeAggregationRepository,
        IMetadataCacheService metadataCacheService)
    {
        _priorityAggregationService = priorityAggregationService;
        _logger = logger;
        _routeServiceApi = routeServiceApi;
        _routeAggregationRepository = routeAggregationRepository;
        _metadataCacheService = metadataCacheService;
    }

    /// <summary>
    /// MediatR Handle method - delegates to base class ExecuteHybridPaginationAsync
    /// </summary>
    public async Task<PagedResult<PriorityRouteAggregationDto>> Handle(
        GetPriorityRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        var pageNumber = request.ParamRequest.Page;
        var pageSize = request.ParamRequest.PageSize;

        // Use base class hybrid pagination logic
        return await ExecuteHybridPaginationAsync(
            request,
            pageNumber,
            pageSize,
            cancellationToken);
    }

    /// <summary>
    /// STEP 1: Fetch hot priority data from Redis (DE service type only)
    /// PERFORMANCE OPTIMIZATION: Filter and paginate on metadata FIRST, then fetch only needed aggregations
    /// </summary>
    protected override async Task<List<RouteAggregationSummary>> FetchFromRedisAsync(
        GetPriorityRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Fetching priority route aggregations (DE only) from Redis cache");

            var priorityConfigs = await _metadataCacheService.GetAsync<Domain.Entities.Metadata.PriorityPlan>();
            if (priorityConfigs == null || !priorityConfigs.Any())
            {
                _logger.LogInformation("No priority plan configurations found in metadata cache");
                return new List<RouteAggregationSummary>();
            }

            // Get active priority plan IDs
            ActivePriorityPlans = priorityConfigs
                                .Where(pc => pc.IsActive == true && pc.IsDeleted != true)
                                .ToDictionary(x => x.Id);

            var pageNumber = request.ParamRequest.Page;
            var pageSize = request.ParamRequest.PageSize;

            // STEP 1: Get priority route metadata (lightweight - only keys and timestamps)
            var allMetadata = await _priorityAggregationService.GetRouteMetadataListAsync(cancellationToken);

            if (!allMetadata.Any())
            {
                _logger.LogInformation("No priority route metadata found in Redis");
                return new List<RouteAggregationSummary>();
            }

            _logger.LogInformation("Fetched {Count} metadata items from Redis", allMetadata.Count);

            // STEP 2: Apply filters on metadata (very fast - no heavy data yet)
            var filteredMetadata = ApplyMetadataFilters(allMetadata, request.ParamRequest);

            // STEP 3: Sort metadata by RouteKey (chronological by FromTime:ToTime)
            // RouteKey format: "FromTime:ToTime:FromOfficeId:ToOfficeId"
            // Sorting by RouteKey automatically sorts by FromTime descending (most recent first)
            var sortedMetadata = filteredMetadata.OrderByDescending(m => m.RouteKey).ToList();

            _logger.LogInformation("After filtering: {Count} metadata items", sortedMetadata.Count);

            // IMPORTANT: Set estimated total count for accurate pagination
            // This tells the base class the ACTUAL total count from filtered metadata
            // Without this, pagination would only see the current page's items
            EstimatedTotalCount = sortedMetadata.Count;

            // STEP 4: Paginate metadata to determine which aggregations we need
            var pagedMetadata = sortedMetadata
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            if (!pagedMetadata.Any())
            {
                _logger.LogInformation("No metadata items for page {Page}", pageNumber);
                return new List<RouteAggregationSummary>();
            }

            // STEP 5: Extract route keys for ONLY the current page
            var routeKeysToFetch = pagedMetadata.Select(m => m.RouteKey).ToList();

            _logger.LogInformation(
                "Fetching {Count} aggregations for page {Page} (out of {Total} total after filter)",
                routeKeysToFetch.Count,
                pageNumber,
                sortedMetadata.Count);

            // STEP 6: Fetch ONLY the aggregations needed for this page (batch fetch from Redis)
            var aggregations = await _priorityAggregationService.GetRouteAggregationsAsync(
                routeKeysToFetch,
                cancellationToken);

            // IMPORTANT: Tell base class we've already paginated the data
            // This prevents double pagination (which would cause page 2+ to be empty)
            IsPaginationAlreadyDone = true;

            return aggregations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching priority aggregations from Redis");
            return new List<RouteAggregationSummary>();
        }
    }
    /// <summary>
    /// STEP 2: Fetch cold/historical data from PostgreSQL
    /// Uses estimated count strategy to avoid expensive COUNT queries on large datasets
    /// </summary>
    protected override async Task<List<RouteAggregationSummary>> FetchFromDatabaseAsync(
        GetPriorityRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            var paramRequest = request.ParamRequest;

            // Start with base query
            var query = _routeAggregationRepository.GetQueryable()
                .Where(ra => ra.AggregationType == "priority");  // Filter by priority aggregation type

            // Extract dictionary keys to list for EF Core translation
            if (ActivePriorityPlans != null && ActivePriorityPlans.Any())
            {
                var activePriorityPlanIds = ActivePriorityPlans.Keys.ToList();
                query = query.Where(ra => activePriorityPlanIds.Contains(ra.PriorityPlanId ?? Guid.Empty));
            }
            else
            {
                //ActivePriorityPlans = null, meaning have not priority setup
                query = query.Where(ra => ra.PriorityPlanId == Guid.Empty);
            }

            // Apply filters at DB level
            if (paramRequest.FromOfficeIds != null && paramRequest.FromOfficeIds.Any())
            {
                query = query.Where(ra => paramRequest.FromOfficeIds.Contains(ra.FromOfficeId));
            }

            if (paramRequest.ToOfficeIds != null && paramRequest.ToOfficeIds.Any())
            {
                query = query.Where(ra => paramRequest.ToOfficeIds.Contains(ra.ToOfficeId));
            }

            if (paramRequest.PriorityPlanIds != null && paramRequest.PriorityPlanIds.Any())
            {
                query = query.Where(ra => paramRequest.PriorityPlanIds.Contains(ra.PriorityPlanId ?? Guid.Empty));
            }

            // Filter by SearchTerm at DB level
            if (!string.IsNullOrEmpty(paramRequest.SearchTerm))
            {
                var searchTerm = paramRequest.SearchTerm.ToLower();
                query = query.Where(ra =>
                    ra.RouteKey.ToLower().Contains(searchTerm) ||
                    ra.FromOfficeId.ToLower().Contains(searchTerm) ||
                    ra.ToOfficeId.ToLower().Contains(searchTerm));
            }

            // Filter out routes without orders (optimization - avoid fetching empty routes)
            if (paramRequest.ExcludeEmptyRoutes)
            {
                query = query.Where(ra => ra.TotalOrders > 0);
                _logger.LogInformation("Excluding routes without orders at database level (ExcludeEmptyRoutes=true)");
            }

            // Apply sorting - use RouteKey for chronological order (same as metadata path)
            // RouteKey format: FromTime:ToTime:FromOfficeId:ToOfficeId (ISO 8601)
            // Uses existing unique index: uq_route_aggregations_unique_with_coalesce
            query = query.OrderByDescending(ra => ra.RouteKey);

            // OPTIMIZATION: Fetch pageSize + 1 to detect if there are more pages
            // This avoids expensive COUNT(*) query on large datasets
            var entitiesToFetch = paramRequest.PageSize + 1;
            var entities = await query
                .Skip((paramRequest.Page - 1) * paramRequest.PageSize)
                .Take(entitiesToFetch)
                .ToListAsync(cancellationToken);

            // Calculate estimated total count based on results
            bool hasMorePages = entities.Count > paramRequest.PageSize;
            int estimatedTotal;

            if (hasMorePages)
            {
                // We got pageSize + 1 results, so there are definitely more pages
                // Estimate: current page items + at least 1 more page
                estimatedTotal = (paramRequest.Page * paramRequest.PageSize) + 1;

                // Remove the extra item we fetched for detection
                entities = entities.Take(paramRequest.PageSize).ToList();
            }
            else
            {
                // We got less than pageSize + 1, so this is the last page
                // We can calculate exact count
                estimatedTotal = ((paramRequest.Page - 1) * paramRequest.PageSize) + entities.Count;
            }

            // Set estimated total count (avoids expensive COUNT query)
            EstimatedTotalCount = estimatedTotal;

            // Tell base class we've already filtered, sorted, and paginated
            IsPaginationAlreadyDone = true;

            // Map to RouteAggregationSummary
            var aggregations = entities.Select(MapEntityToSummary).ToList();

            _logger.LogInformation(
                "Fetched {Count} priority aggregations from database (page {Page}, estimated total ~{Total}) - no COUNT query",
                aggregations.Count,
                paramRequest.Page,
                estimatedTotal);

            return aggregations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching priority aggregations from database");
            return new List<RouteAggregationSummary>();
        }
    }

    /// <summary>
    /// STEP 3: Apply in-memory filters to the merged data
    /// IMPORTANT: This method is NOT called when IsPaginationAlreadyDone = true (database fallback mode)
    /// All filtering is done at DB level in FetchFromDatabaseAsync
    /// This is only used for hybrid modes when Redis data needs filtering
    /// </summary>
    protected override List<RouteAggregationSummary> ApplyFilters(
        List<RouteAggregationSummary> aggregations,
        GetPriorityRouteAggregationsQuery request)
    {
        var paramRequest = request.ParamRequest;

        // When IsPaginationAlreadyDone = true, this method is skipped by base class
        // All filtering already done at DB level in FetchFromDatabaseAsync

        var query = aggregations.AsEnumerable();

        // Search term (searches in route key, office IDs)
        if (!string.IsNullOrEmpty(paramRequest.SearchTerm))
        {
            var searchTerm = paramRequest.SearchTerm.ToLowerInvariant();
            query = query.Where(a =>
                a.RouteKey.ToLowerInvariant().Contains(searchTerm) ||
                a.FromOfficeId.ToLowerInvariant().Contains(searchTerm) ||
                a.ToOfficeId.ToLowerInvariant().Contains(searchTerm));
        }

        return query.ToList();
    }

    /// <summary>
    /// STEP 4: Apply sorting to the filtered data
    /// Note: Most sorting already done in FetchFromRedisAsync on metadata
    /// </summary>
    protected override List<RouteAggregationSummary> ApplySorting(
        List<RouteAggregationSummary> aggregations,
        GetPriorityRouteAggregationsQuery request)
    {
        // Sorting already done in FetchFromRedisAsync on metadata
        // Just maintain order
        return aggregations.OrderByDescending(a => a.RouteKey).ToList();
    }

    /// <summary>
    /// STEP 5: Map summary to DTO with enrichment (post office names, order details)
    /// </summary>
    protected override async Task<PriorityRouteAggregationDto> MapToDtoAsync(
        RouteAggregationSummary summary,
        string dataSource,
        CancellationToken cancellationToken)
    {
        // Fetch post office data for this summary
        var postOffices = await FetchPostOfficeDataAsync(new List<RouteAggregationSummary> { summary });
        var priorityPlanName = ActivePriorityPlans?.GetValueOrDefault(summary.PriorityPlanId ?? Guid.Empty)?.PriorityPlanName ?? "";

        var dto = new PriorityRouteAggregationDto
        {
            RouteKey = summary.RouteKey,
            FromOfficeId = summary.FromOfficeId,
            FromOfficeName = postOffices.FirstOrDefault(po => po.PostOfficeCode == summary.FromOfficeId)?.PostOfficeName ?? string.Empty,
            ToOfficeId = summary.ToOfficeId,
            ToOfficeName = postOffices.FirstOrDefault(po => po.PostOfficeCode == summary.ToOfficeId)?.PostOfficeName ?? string.Empty,
            PlanFromTime = summary.FromTime,
            PlanToTime = summary.ToTime,
            ActualFromTime = summary.ActualFromTime,
            ActualToTime = summary.ActualToTime,
            PriorityPlanId = summary.PriorityPlanId,
            PriorityPlanName = priorityPlanName ?? string.Empty,
            TotalOrders = summary.TotalOrders,
            TotalAlmostDueOrders = 0,
            TotalOverdueOrders = 0,
            TotalWeight = summary.TotalWeight,
            Details = null // List view doesn't include order details - use GetByKey endpoint for paginated details
            // Note: dataSource parameter available for debugging/monitoring
        };

        // Calculate derived values from OrderDetails if available (for summary statistics only)
        if (summary.OrderDetails != null && summary.OrderDetails.Any())
        {
            // Flatten all items from all orders to calculate status counts
            var allItems = summary.OrderDetails.Values
                .Where(order => order.Items != null && order.Items.Any(i => i.IsDeleted != true))
                .SelectMany(order => order.Items.Select(item => new PriorityRouteAggregationDetailDto
                {
                    MailerId = item.MailerId,
                    CurrentParentId = item.CurrentParentType != nameof(OrderParentType.CTTAIKIEN)
                        ? (item.CurrentParentId ?? "")
                        : string.Empty,
                    CurrentPackingListId = item.CurrentPackingListId ?? string.Empty,
                    ServiceTypeId = item.ServiceTypeId ?? "",
                    ActualFromTime = item.ActualFromTime,
                    PlanFromTime = summary.FromTime,
                    ActualToTime = item.ActualToTime,
                    PlanToTime = summary.ToTime,
                    Weight = item.Weight,
                    RealWeight = item.RealWeight,
                    CalWeight = item.CalWeight
                }))
                .ToList();

            dto.TotalAlmostDueOrders = allItems.Count(d => d.Status == PriorityRouteAggregationOrderStatus.AlmostDue);
            dto.TotalOverdueOrders = allItems.Count(d => d.Status == PriorityRouteAggregationOrderStatus.Overdue);
        }

        return dto;
    }

    // ============================================================================
    // HELPER METHODS (specific to this handler)
    // ============================================================================
    // Note: Common methods (ParseJsonBreakdown, FetchPostOfficeDataAsync, MapEntityToSummary, GetUniqueKey)
    //       are now in RouteAggregationQueryHandlerBase to avoid duplication

    /// <summary>
    /// Applies filters to lightweight metadata (BEFORE fetching full aggregations)
    /// This is the key performance optimization - filter on small objects first
    /// </summary>
    private List<RouteMetadata> ApplyMetadataFilters(
        List<RouteMetadata> metadata,
        GetPriorityPlanningAggregationsRequest request)
    {
        var query = metadata.AsEnumerable();

        // Get active config only (extract from RouteKey)
        if (ActivePriorityPlans != null && ActivePriorityPlans.Any())
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                if (parts.Length >= 5 && !string.IsNullOrEmpty(parts[4]))
                {
                    Guid? priorityPlanId = null;
                    string input = parts[4];
                    if (Guid.TryParse(input, out var parsedGuid))
                    {
                        priorityPlanId = parsedGuid;
                    }
                    return ActivePriorityPlans.ContainsKey(priorityPlanId ?? Guid.Empty);
                }
                return false;
            });
        }

        // Filter by FromOfficeId (extract from RouteKey)
        if (request.FromOfficeIds != null && request.FromOfficeIds.Any())
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                return parts.Length >= 5 && request.FromOfficeIds.Contains(parts[2]);
            });
        }

        // Filter by ToOfficeId (extract from RouteKey)
        if (request.ToOfficeIds != null && request.ToOfficeIds.Any())
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                return parts.Length >= 5 && request.ToOfficeIds.Contains(parts[3]);
            });
        }

        // Filter by PriorityPlanIds (extract from RouteKey)
        if (request.PriorityPlanIds != null && request.PriorityPlanIds.Any())
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                if (parts.Length >= 5 && !string.IsNullOrEmpty(parts[4]))
                {
                    Guid? priorityPlanId = null;
                    string input = parts[4];
                    if (Guid.TryParse(input, out var parsedGuid))
                    {
                        priorityPlanId = parsedGuid;
                    }

                    return request.PriorityPlanIds.Contains(priorityPlanId ?? Guid.Empty);
                }
                return false;
            });
        }

        // Note: PriorityPlanIds cannot be filtered on metadata (not in RouteKey)
        // Will be filtered later when full aggregations are loaded

        // Search term on route key
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLowerInvariant();
            query = query.Where(m => m.RouteKey.ToLowerInvariant().Contains(searchTerm)).ToList();
        }

        // Filter out routes without orders (optimization - avoid fetching empty route aggregations)
        if (request.ExcludeEmptyRoutes)
        {
            query = query.Where(m => m.TotalOrders > 0);
            _logger.LogInformation("Excluding routes without orders (ExcludeEmptyRoutes=true)");
        }

        // Sort by RouteKey (chronological order - FromTime embedded in RouteKey format)
        // RouteKey format: FromTime:ToTime:FromOfficeId:ToOfficeId (ISO 8601)
        // Lexicographic sort = chronological sort (newest routes first)
        var orderedQuery = query.OrderByDescending(m => m.RouteKey);

        return orderedQuery.ToList();
    }
}
