﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.TransportMethodType.Queries.GetTransportMethodTypes;
using TMS.PlanningService.Contracts.Dto;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/transport-method-types")]
[Produces("application/json")]
public class TransportMethodTypeController : ControllerBase
{
    private readonly IMediator _mediator;

    public TransportMethodTypeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    ///// <summary>
    ///// Get all lead time types
    ///// </summary> 
    ///// <returns>List of lead time type</returns>
    [HttpGet("")]
    [ProducesResponseType(typeof(List<TransportMethodTypeDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<TransportMethodTypeDto>>> GetTransportMethodTypes()
    {
        var query = new GetTransportMethodTypesQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }
}
