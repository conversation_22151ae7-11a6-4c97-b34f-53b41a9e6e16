﻿using MediatR;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Utilities;

namespace TMS.PlanningService.Application.Features.DailyPlanning.Queries.GetDailyPlanById;

/// <summary>
/// Handler for getting daily plan detail by ID
/// Returns plan information with all route aggregations from Redis
/// </summary>
public class GetDailyPlanByIdQueryHandler
    : IRequestHandler<GetDailyPlanByIdQuery, DailyPlanDetailDto?>
{
    private readonly IBaseRepository<DailyPlanningEntity> _dailyPlanRepository;
    private readonly IBaseRepository<RouteAggregationEntity> _routeAggregationRepository;
    private readonly IDailyPlanningAggregationService _dailyPlanningAggregationService;
    private readonly ILogger<GetDailyPlanByIdQueryHandler> _logger;
    private readonly IExternalDataService _externalDataService;

    public GetDailyPlanByIdQueryHandler(
        IBaseRepository<DailyPlanningEntity> dailyPlanRepository,
        IBaseRepository<RouteAggregationEntity> routeAggregationRepository,
        IDailyPlanningAggregationService dailyPlanningAggregationService,
        ILogger<GetDailyPlanByIdQueryHandler> logger,
         IExternalDataService externalDataService)
    {
        _dailyPlanRepository = dailyPlanRepository;
        _routeAggregationRepository = routeAggregationRepository;
        _dailyPlanningAggregationService = dailyPlanningAggregationService;
        _logger = logger;
        _externalDataService = externalDataService;
    }

    public async Task<DailyPlanDetailDto?> Handle(
        GetDailyPlanByIdQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation(
            "Getting daily plan detail for ID: {DailyPlanId}",
            request.DailyPlanId);

        // Get daily plan entity (without RouteAggregations navigation - it's ignored due to composite PK)
        var dailyPlanEntity = await _dailyPlanRepository.GetByIdAsync(request.DailyPlanId);

        if (dailyPlanEntity == null)
        {
            _logger.LogWarning("Daily plan not found: {DailyPlanId}", request.DailyPlanId);
            return null;
        }

        // HYBRID APPROACH: Try Redis first, fallback to database if needed
        List<RouteAggregationSummary> routeAggregations;
        string dataSource;

        try
        {
            // STEP 1: Try to get plan totals first (contains RouteKeys for optimization)
            var planTotalsDict = await _dailyPlanningAggregationService
                .GetPlanTotalsBatchAsync(new List<Guid> { request.DailyPlanId }, cancellationToken);

            if (planTotalsDict.TryGetValue(request.DailyPlanId, out var planTotals) &&
                planTotals != null &&
                planTotals.RouteKeys.Any())
            {
                // STEP 2: Fetch route aggregations from Redis using RouteKeys (optimized)
                _logger.LogInformation(
                    "Fetching {Count} route aggregations from Redis for daily plan {DailyPlanId}",
                    planTotals.RouteKeys.Count,
                    request.DailyPlanId);

                routeAggregations = await _dailyPlanningAggregationService
                    .GetRouteAggregationsAsync(planTotals.RouteKeys, cancellationToken);

                dataSource = "Redis";
            }
            else
            {
                // STEP 3: Fallback to database if no Redis data
                // Query RouteAggregationEntity separately since navigation is ignored
                _logger.LogWarning(
                    "No route aggregations found in Redis for daily plan {DailyPlanId}, falling back to database",
                    request.DailyPlanId);

                var dbEntities = await _routeAggregationRepository.FindAsync(
                    x => x.DailyPlanningId == request.DailyPlanId && x.AggregationType == "daily");

                routeAggregations = MapDatabaseEntitiesToSummaries(dbEntities);
                dataSource = "Database (Fallback)";
            }
        }
        catch (Exception ex)
        {
            // STEP 4: Exception fallback to database
            _logger.LogError(
                ex,
                "Error fetching route aggregations from Redis for daily plan {DailyPlanId}, falling back to database",
                request.DailyPlanId);

            var dbEntities = await _routeAggregationRepository.FindAsync(
                x => x.DailyPlanningId == request.DailyPlanId && x.AggregationType == "daily");

            routeAggregations = MapDatabaseEntitiesToSummaries(dbEntities);
            dataSource = "Database (Error Fallback)";
        }

        // planning should be ordered by sequency
        routeAggregations = routeAggregations.OrderBy(x => x.RouteKey).ToList();

        // Map to detail DTO
        var dto = MapToDetailDto(dailyPlanEntity, routeAggregations);

        _logger.LogInformation(
            "Retrieved daily plan {Code} with {RouteCount} routes from {DataSource}",
            dailyPlanEntity.Code,
            routeAggregations.Count,
            dataSource);

        await _externalDataService.EnrichWithDailyPlanDetailAsync(dto);

        return dto;
    }

    private DailyPlanDetailDto MapToDetailDto(
        DailyPlanningEntity plan,
        List<RouteAggregationSummary> routeAggregations)
    {
        // Route aggregations are already filtered by DailyPlanningId in GetRouteAggregationsAsync
        // So we can use them directly without additional filtering
        var relevantAggregations = routeAggregations;

        return new DailyPlanDetailDto
        {
            Id = plan.Id,
            CompanyId = plan.CompanyId,
            PlanningTemplateId = plan.PlanningTemplateId,
            RouteId = plan.RouteId,
            ExecutionDate = plan.ExecutionDate,
            Code = plan.Code,
            Name = plan.Name,
            TotalDistance = plan.TotalDistance,
            TotalDuration = plan.TotalDuration,
            OfficeCount = plan.OfficeCount,
            VehicleTypeId = plan.VehicleTypeId,
            RouteCode = plan.RouteCode,
            PostOfficeCodes = plan.PostOfficeCodes,
            PriorityNumber = plan.PriorityNumber,
            Status = plan.Status,
            ActualStartTime = plan.ActualStartTime,
            ActualEndTime = plan.ActualEndTime,
            IsActive = plan.IsActive,
            TotalEstimateWeight = plan.TotalWeight / 1000,
            TotalOnVehicleWeight = 0, // need implement later when having US loading mailer to vehicle
            Routes = relevantAggregations.Select(MapRouteToDto).ToList(),
            CreatedAt = plan.CreatedAt,
            UpdatedAt = plan.UpdatedAt
        };
    }

    private RouteAggregationDto MapRouteToDto(RouteAggregationSummary summary)
    {

        return new RouteAggregationDto
        {
            RouteKey = summary.RouteKey,
            FromOfficeId = summary.FromOfficeId,
            ToOfficeId = summary.ToOfficeId,
            ActualFromTime = summary.ActualFromTime,
            ActualToTime = summary.ActualToTime,
            PlanFromTime = summary.FromTime,
            PlanToTime = summary.ToTime,
            TotalDurationMinutes = summary.TotalDurationMinutes,
            AverageDurationMinutes = summary.AverageDurationMinutes,
            EarliestStartTime = summary.EarliestStartTime,
            LatestEndTime = summary.LatestEndTime,
            TotalOrders = summary.TotalOrders,
            TotalItems = summary.TotalItems,
            TotalEstimateWeight = summary.TotalWeight / 1000,
            TotalOnVehicleWeight= 0, // need implement later
            TransportProviderBreakdown = summary.TransportProviderBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            VehicleTypeBreakdown = summary.VehicleTypeBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            TransportMethodBreakdown = summary.TransportMethodBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            PriorityScore = summary.PriorityScore,
            NeedsOptimization = summary.NeedsOptimization,
            AggregatedAt = summary.AggregatedAt,
            ToBusinessOperation = summary.ToBusinessOperation,
            ToBusinessOperationName = summary.ToBusinessOperation.GetDescription(),
            FromBusinessOperation = summary.FromBusinessOperation,
            FromBusinessOperationName = summary.FromBusinessOperation.GetDescription()
        };
    }

    /// <summary>
    /// Maps database RouteAggregationEntity to in-memory RouteAggregationSummary
    /// Used for fallback when Redis data is not available
    /// </summary>
    private List<RouteAggregationSummary> MapDatabaseEntitiesToSummaries(
        IEnumerable<RouteAggregationEntity> entities)
    {
        if (entities == null || !entities.Any())
        {
            return new List<RouteAggregationSummary>();
        }

        return entities.Select(entity => new RouteAggregationSummary
        {
            RouteKey = entity.RouteKey,
            FromOfficeId = entity.FromOfficeId,
            ToOfficeId = entity.ToOfficeId,
            FromTime = entity.FromTime,
            ToTime = entity.ToTime,
            ActualFromTime = entity.ActualFromTime,
            ActualToTime = entity.ActualToTime,
            TotalDurationMinutes = entity.TotalDurationMinutes,
            AverageDurationMinutes = entity.AverageDurationMinutes,
            EarliestStartTime = entity.EarliestStartTime,
            LatestEndTime = entity.LatestEndTime,
            TotalOrders = entity.TotalOrders,
            TotalItems = entity.TotalItems,
            TotalWeight = entity.TotalWeight,
            TotalRealWeight = entity.TotalRealWeight,
            TotalCalWeight = entity.TotalCalWeight,
            TotalDiffWeight = entity.TotalDiffWeight,
            PriorityScore = entity.PriorityScore,
            NeedsOptimization = entity.NeedsOptimization,
            TransportProviderBreakdown = ParseJsonBreakdown(entity.TransportProviderBreakdownJson),
            VehicleTypeBreakdown = ParseJsonBreakdown(entity.VehicleTypeBreakdownJson),
            TransportMethodBreakdown = ParseJsonBreakdown(entity.TransportMethodBreakdownJson),
            CreatedAt = entity.CreatedAt,       // Original creation time from database
            AggregatedAt = entity.AggregatedAt,
            LastPersistedAt = entity.SnapshotAt,
            DailyPlanningId = entity.DailyPlanningId != Guid.Empty ? entity.DailyPlanningId : null,
            OrderDetails = new Dictionary<string, OrderOnRoute>(), // Database doesn't store detailed order info
            FromBusinessOperation = entity.FromBusinessOperation,
            ToBusinessOperation = entity.ToBusinessOperation
        }).ToList();
    }

    /// <summary>
    /// Parses JSON breakdown strings from database
    /// </summary>
    private List<OptionsCountDto> ParseJsonBreakdown(string? json)
    {
        if (string.IsNullOrEmpty(json))
        {
            return new List<OptionsCountDto>();
        }

        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<OptionsCountDto>>(json)
                ?? new List<OptionsCountDto>();
        }
        catch
        {
            return new List<OptionsCountDto>();
        }
    }
}
