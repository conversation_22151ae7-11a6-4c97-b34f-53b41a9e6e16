﻿namespace TMS.PlanningService.Contracts.Planning;

public class PlanningWebhookResponse
{
    public bool Success { get; set; }

    public string Message { get; set; } = string.Empty;

    public string? ProcessedMailerId { get; set; }

    public string? ProcessedChildMailerId { get; set; }

    public DateTime ProcessedAt { get; set; }

    public Dictionary<string, object>? AdditionalData { get; set; }
}

public class BatchPlanningWebhookRequest
{
    public string BatchId { get; set; } = string.Empty;

    public string Source { get; set; } = string.Empty;

    public List<PlanningWebhookRequest> PlanningData { get; set; } = new();

    public DateTime Timestamp { get; set; }

    public Dictionary<string, object>? Metadata { get; set; }
}

public class BatchPlanningWebhookResponse
{
    public bool Success { get; set; }

    public string Message { get; set; } = string.Empty;

    public string BatchId { get; set; } = string.Empty;

    public int QueuedPlanningData { get; set; }

    public int SkippedPlanningData { get; set; }

    public DateTime ProcessedAt { get; set; }

    public Dictionary<string, object>? AdditionalData { get; set; }
}
