﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class TransportMethodTypeConfiguration : IEntityTypeConfiguration<TransportMethodType>
{
    public void Configure(EntityTypeBuilder<TransportMethodType> builder)
    {
        builder.ToTable("transport_method_type");

        // Primary Key
        builder.HasKey(x => x.Id)
               .HasName("pk_transport_method_type");

        builder.Property(x => x.Id)
            .HasColumnName("id")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.Name)
            .HasColumnName("name")
            .HasMaxLength(500);
    }
}
