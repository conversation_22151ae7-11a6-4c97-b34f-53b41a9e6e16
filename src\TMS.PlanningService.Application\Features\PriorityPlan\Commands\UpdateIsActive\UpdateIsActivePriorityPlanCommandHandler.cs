﻿using MediatR;
using StackExchange.Redis;
using TMS.PlanningService.Domain.IRepository;
using TMS.SharedKernal.Caching;
using TMS.SharedKernal.SmoothRedis;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;
using Entities = TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdateIsActive;

public class UpdateIsActivePriorityPlanCommandHandler : IRequestHandler<UpdateIsActivePriorityPlanCommand, Unit>
{
    private readonly IPriorityPlanRepository _priorityPlanRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMetadataCacheService _metadataCacheService;

    public UpdateIsActivePriorityPlanCommandHandler(
        IPriorityPlanRepository priorityPlanRepository,
        IUnitOfWork unitOfWork,
        IMetadataCacheService metadataCacheService)
    {
        _priorityPlanRepository = priorityPlanRepository;
        _unitOfWork = unitOfWork;
        _metadataCacheService = metadataCacheService;
    }

    public async Task<Unit> Handle(UpdateIsActivePriorityPlanCommand request, CancellationToken cancellationToken)
    {
        var entity = await _priorityPlanRepository.GetByIdAsync(request.requestParam.Id, cancellationToken);
        if (entity is null)
            throw new BusinessRuleValidationException(nameof(Entities.Metadata.PriorityPlan.Id), CommonErrorCodes.MS018);

        entity.IsActive = request.requestParam.IsActive;
        _priorityPlanRepository.Update(entity);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        //====Redis cache update
        var priorityPlans = await _priorityPlanRepository.GetActivePriorityPlanAsync(cancellationToken);
        await _metadataCacheService.SetPriorityPlansAsync(priorityPlans);
        return Unit.Value;
    }
}
