﻿using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// Request parameters for getting daily plans with filtering and pagination
/// </summary>
public record GetDailyPlansRequest(
    string? SearchTerm,
    int Page = 1,
    int PageSize = 20,
    DateOnly? ExecutionDateFrom = null,
    DateOnly? ExecutionDateTo = null,
    string? Status = null,
    bool? IsActive = null,
    Guid? PlanningTemplateId = null,
    List<Guid>? VehicleTypeIds = null,
    SortOrderDailyPlan? SortOrder = null
);
