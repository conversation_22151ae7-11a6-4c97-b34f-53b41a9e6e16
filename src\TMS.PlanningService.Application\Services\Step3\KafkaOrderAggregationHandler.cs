﻿using Microsoft.Extensions.Logging;
using TMS.PlanningService.Contracts.Orders;
using TMS.SharedKernal.Kafka.Abstractions;

namespace TMS.PlanningService.Application.Services.Step3;

// this not use currently
/// <summary>
/// Kafka consumer handler for order aggregation events from OrderService
/// Processes batched order summaries every 5 minutes
/// </summary>
public class KafkaOrderAggregationHandler : IMessageHandler<OrderAggregationEvent>
{
    private readonly ILogger<KafkaOrderAggregationHandler> _logger;
    private readonly IOrderAggregationPlanningService _planningService;

    public KafkaOrderAggregationHandler(
        ILogger<KafkaOrderAggregationHandler> logger,
        IOrderAggregationPlanningService planningService)
    {
        _logger = logger;
        _planningService = planningService;
    }

    public async Task HandleAsync(
        OrderAggregationEvent message,
        MessageContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation(
                "Received order aggregation event - Event ID: {EventId}, Offices: {OfficeCount}, Total Orders: {TotalOrders}",
                message.Id,
                message.OfficeSummaries.Count,
                message.OfficeSummaries.Sum(s => s.TotalOrders));

            // Process the aggregation and trigger planning operations
            await _planningService.ProcessOrderAggregationAsync(message, cancellationToken);

            _logger.LogInformation(
                "Successfully processed order aggregation event - Event ID: {EventId}",
                message.Id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "Failed to process order aggregation event - Event ID: {EventId}",
                message.Id);

            // Re-throw to trigger Kafka retry/dead letter queue
            throw;
        }
    }
}
