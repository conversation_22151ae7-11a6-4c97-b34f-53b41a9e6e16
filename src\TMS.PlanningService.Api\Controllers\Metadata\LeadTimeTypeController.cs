﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.LeadTime.Queries.GetLeadTimeById;
using TMS.PlanningService.Application.Features.LeadTime.Queries.GetLeadTimes;
using TMS.PlanningService.Application.Features.LeadTimePartner.CreateLeadTimePartner;
using TMS.PlanningService.Application.Features.LeadTimePartner.UpdateLeadTimePartner;
using TMS.PlanningService.Application.Features.LeadTimeType.Queries.GetLeadTimeTypes;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.CreatePriorityPlan;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdatePriorityPlan;
using TMS.PlanningService.Application.Features.PriorityPlan.Queries.GetPriorityPlanById;
using TMS.PlanningService.Application.Features.PriorityRoute.Queries.GetPriorityPlans;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.LeadTimePartner;
using TMS.PlanningService.Contracts.PmsSync;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/lead-time-types")]
[Produces("application/json")]
public class LeadTimeTypeController : ControllerBase
{
    private readonly IMediator _mediator;

    public LeadTimeTypeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    ///// <summary>
    ///// Get all lead time types
    ///// </summary> 
    ///// <returns>List of lead time type</returns>
    [HttpGet("")]
    [ProducesResponseType(typeof(List<LeadTimeConfigDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<LeadTimeTypeDto>>> GetLeadTimeTypes()
    {
        var query = new GetLeadTimeTypesQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }
}
