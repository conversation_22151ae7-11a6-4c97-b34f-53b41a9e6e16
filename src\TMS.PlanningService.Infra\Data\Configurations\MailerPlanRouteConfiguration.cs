﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Infra.Data.Configurations;

public class MailerPlanRouteConfiguration : IEntityTypeConfiguration<MailerPlanRouteEntity>
{
    public void Configure(EntityTypeBuilder<MailerPlanRouteEntity> builder)
    {
        builder.ToTable("mailer_plan_routes");

        builder.HasKey(x => new { x.MailerId, x.ChildMailerId, x.Step, x.MasterCreatedDate });
         
        builder.Property(x => x.MailerId)
            .HasColumnName("mailer_id")
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.ChildMailerId)
            .HasColumnName("child_mailer_id")
            .HasMaxLength(50);

        builder.Property(x => x.Step)
            .HasColumnName("step")
            .IsRequired();

        builder.Property(x => x.FromPostOfficeId)
            .HasColumnName("from_post_office_id")
            .HasMaxLength(10);

        builder.Property(x => x.ToPostOfficeId)
            .HasColumnName("to_post_office_id")
            .HasMaxLength(10);

        builder.Property(x => x.LeadTimeId)
            .HasColumnName("lead_time_id")
            .HasMaxLength(50);

        builder.Property(x => x.TransportProviderType)
            .HasColumnName("transport_provider_type")
            .HasMaxLength(50);

        builder.Property(x => x.TransportProviderTypeName)
            .HasColumnName("transport_provider_type_name")
            .HasMaxLength(100);

        builder.Property(x => x.TransportVehicleType)
            .HasColumnName("transport_vehicle_type")
            .HasMaxLength(50);

        builder.Property(x => x.TransportVehicleTypeName)
            .HasColumnName("transport_vehicle_type_name")
            .HasMaxLength(100);

        builder.Property(x => x.TransportMethodId)
            .HasColumnName("transport_method_id")
            .HasMaxLength(10);

        builder.Property(x => x.TransportMethodName)
            .HasColumnName("transport_method_name")
            .HasMaxLength(100);

        builder.Property(x => x.FromTime)
            .HasColumnName("from_time");

        builder.Property(x => x.FromTimeDelay)
            .HasColumnName("from_time_delay");

        builder.Property(x => x.ToTime)
            .HasColumnName("to_time");

        builder.Property(x => x.ToTimeDelay)
            .HasColumnName("to_time_delay");

        builder.Property(x => x.AddDays)
            .HasColumnName("add_days");

        builder.Property(x => x.Type)
            .HasColumnName("type")
            .HasMaxLength(50);

        builder.Property(x => x.TypeName)
            .HasColumnName("type_name")
            .HasMaxLength(100);

        builder.Property(x => x.HistoryId)
            .HasColumnName("history_id");

        builder.Property(x => x.PreviousStep)
            .HasColumnName("previous_step");

        builder.Property(x => x.LeadTimeTypeId)
           .HasColumnName("lead_time_type_id")
           .HasMaxLength(50);

        builder.Property(x => x.LeadTimeTypeName)
           .HasColumnName("lead_time_type_name")
           .HasMaxLength(200);

        builder.Property(x => x.ServiceTypeId)
           .HasColumnName("service_type_id")
           .HasMaxLength(200);

        builder.Property(x => x.ServiceTypeName)
           .HasColumnName("service_type_name")
           .HasMaxLength(500);

        builder.Property(x => x.ExtraService)
           .HasColumnName("extra_service")
           .HasMaxLength(150);

        builder.Property(x => x.ExtraServiceName)
           .HasColumnName("extra_service_name")
           .HasMaxLength(500);

        builder.Property(x => x.MasterCreatedDate)
            .HasColumnName("master_created_date")
            .IsRequired();
    }
}
