﻿using System.ComponentModel.DataAnnotations.Schema;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Domain.Entities;

public class PlanningTemplateDetailEntity : EntityBase
{
    [Column("planning_template_id")]
    public Guid PlanningTemplateId { get; set; }

    [Column("post_office_id")]
    public Guid PostOfficeId { get; set; }

    [Column("post_office_code")]
    public string PostOfficeCode { get; set; }

    [Column("from_time")]
    public TimeSpan FromTime { get; set; }

    [Column("from_add_days")]
    public int FromAddDays { get; set; } = 0;

    [Column("to_time")]
    public TimeSpan ToTime { get; set; }

    [Column("to_add_days")]
    public int ToTimeAddDays { get; set; } = 0;

    [Column("business_operation")]
    public BusinessOperation BusinessOperation { get; set; }

    [Column("distance_between_points")]
    public float DistanceBetweenPoints { get; set; }

    [Column("step_number")]
    public int StepNumber { get; set; }

    // Navigation property
    public PlanningTemplateEntity? PlanningTemplate { get; set; } = null!;
}
