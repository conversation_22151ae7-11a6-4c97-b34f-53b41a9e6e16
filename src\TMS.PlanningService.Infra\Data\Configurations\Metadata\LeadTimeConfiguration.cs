﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class LeadTimeConfiguration : IEntityTypeConfiguration<LeadTime>
{
    public void Configure(EntityTypeBuilder<LeadTime> builder)
    {
        // Table
        builder.ToTable("lead_time");

        // Primary Key
        builder.HasKey(x => x.LeadTimeId)
               .HasName("pk_lead_time");

        builder.Property(x => x.LeadTimeId)
            .HasColumnName("lead_time_id")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.LeadTimeTypeId)
            .HasColumnName("lead_time_type_id")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.LeadTimeTypeName)
            .HasColumnName("lead_time_type_name")
            .HasMaxLength(500);

        builder.Property(x => x.FromOfficeId)
            .HasColumnName("from_office_id")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.FromOfficeProvinceId)
            .HasColumnName("from_office_province_id")
            .HasMaxLength(50);

        builder.Property(x => x.ToOfficeId)
            .HasColumnName("to_office_id")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.ToOfficeProvinceId)
            .HasColumnName("to_office_province_id")
            .HasMaxLength(500);

        builder.Property(x => x.ToProvinceId)
            .HasColumnName("to_province_id")
            .HasMaxLength(50);
         
        builder.Property(x => x.StartTime)
            .HasColumnName("start_time")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(x => x.EndTime)
            .HasColumnName("end_time")
            .HasMaxLength(20)
            .IsRequired();

        builder.Property(x => x.CutOffTime)
            .HasColumnName("cut_off_time")
            .HasMaxLength(20);

        builder.Property(x => x.AddDays)
            .HasColumnName("add_days")
            .HasDefaultValue(0);

        builder.Property(x => x.Description)
            .HasColumnName("description")
            .HasMaxLength(500);

        builder.Property(x => x.CreatedDate)
            .HasColumnName("created_date")
            .HasDefaultValueSql("NOW()");

        builder.Property(x => x.CreatedUserId)
            .HasColumnName("created_user_id")
            .HasMaxLength(50);

        builder.Property(x => x.LastUpdateUser)
            .HasColumnName("last_update_user")
            .HasMaxLength(50);

        builder.Property(x => x.LastUpdateDate)
            .HasColumnName("last_update_date")
            .HasDefaultValueSql("NOW()");

        builder.Property(x => x.IsActive)
            .HasColumnName("is_active")
            .HasDefaultValue(0);

        builder.Property(x => x.ServiceTypeId)
            .HasColumnName("service_type_id")
            .HasMaxLength(50);

        builder.Property(x => x.MailerTypeId)
            .HasColumnName("mailer_type_id")
            .HasMaxLength(50);

        builder.Property(x => x.ExtraService)
            .HasColumnName("extra_service")
            .HasMaxLength(50);

        builder.Property(x => x.FromTimeDelay)
            .HasColumnName("from_time_delay")
            .HasDefaultValue(0);

        builder.Property(x => x.ToTimeDelay)
            .HasColumnName("to_time_delay")
            .HasDefaultValue(0);

        builder.Property(x => x.FromWeight)
            .HasColumnName("from_weight")
            .HasColumnType("decimal(18,2)");

        builder.Property(x => x.ToWeight)
            .HasColumnName("to_weight")
            .HasColumnType("decimal(18,2)");

        builder.Property(x => x.IsConnectPickRequest)
            .HasColumnName("is_connect_pick_request")
            .HasDefaultValue(false);

        builder.Property(x => x.IsExpress)
            .HasColumnName("is_express")
            .HasDefaultValue(false);

        builder.Property(x => x.IsInternationalTrip)
            .HasColumnName("is_international_trip")
            .HasDefaultValue(false);

        builder.Property(x => x.ConnectionType)
            .HasColumnName("connection_type")
            .HasMaxLength(10);

        builder.Property(x => x.FromZoneId)
            .HasColumnName("from_zone_id")
            .HasMaxLength(50);

        builder.Property(x => x.ToZoneId)
            .HasColumnName("to_zone_id")
            .HasMaxLength(50);

        builder.Property(x => x.FromPostOfficeType)
            .HasColumnName("from_post_office_type")
            .HasDefaultValue(0);

        builder.Property(x => x.FromPostOfficeTypeName)
            .HasColumnName("from_post_office_type_name")
            .HasMaxLength(500);

        builder.Property(x => x.ToPostOfficeType)
            .HasColumnName("to_post_office_type")
            .HasDefaultValue(0);

        builder.Property(x => x.ToPostOfficeTypeName)
            .HasColumnName("to_post_office_type_name")
            .HasMaxLength(500);

        builder.Property(x => x.TransportProviderType)
            .HasColumnName("transport_provider_type")
            .HasMaxLength(50);

        builder.Property(x => x.TransportProviderTypeName)
            .HasColumnName("transport_provider_type_name")
            .HasMaxLength(500);

        builder.Property(x => x.TransportVehicleType)
            .HasColumnName("transport_vehicle_type")
            .HasMaxLength(50);

        builder.Property(x => x.TransportVehicleTypeName)
            .HasColumnName("transport_vehicle_type_name")
            .HasMaxLength(500);

        builder.Property(x => x.TransportMethodId)
            .HasColumnName("transport_method_id")
            .HasMaxLength(50);

        builder.Property(x => x.TransportMethodName)
            .HasColumnName("transport_method_name")
            .HasMaxLength(500);
    }
}
