﻿using System.ComponentModel.DataAnnotations;
using TMS.PlanningService.Contracts.Orders;
using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Application.Services.Orders;

/// <summary>
/// Service for fetching order data to enrich route aggregations
/// Maps orders to routes based on MailerId (OrderId) and ChildMailerId (OrderItemId)
/// </summary>
public interface IOrderDataService
{
    /// <summary>
    /// Gets order metrics for a list of mailer IDs (order IDs)
    /// Used to enrich route aggregations with order weight and item counts
    /// If metrics are not found and originalPlanningEvent is provided, sends request to OrderService for recalculation
    /// </summary>
    /// <param name="mailerIds">List of mailer IDs (which are order IDs)</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <param name="originalPlanningEvent">Optional original planning event to send to OrderService if metrics not found</param>
    /// <returns>Dictionary mapping MailerId to order metrics</returns>
    Task<Dictionary<string, OrderMetrics>> GetOrderMetricsByOrderIdsAsync(
        List<string> mailerIds,
        CancellationToken cancellationToken = default,
        object? originalPlanningEvent = null);
}
