﻿namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// Request parameters for querying MailerAdjustRoutes
/// </summary>
public record GetCargoTrackingRoutesRequest(
    /// <summary>
    /// Search term for filtering (MailerId, FromOfficeId, ToOfficeId)
    /// </summary>
    string? SearchTerm = null,

    /// <summary>
    /// Page number (1-based)
    /// </summary>
    int Page = 1,

    /// <summary>
    /// Page size (items per page)
    /// </summary>
    int PageSize = 20,

    /// <summary>
    /// Filter by specific MailerId
    /// </summary>
    string? MailerId = null,

    /// <summary>
    /// Filter by specific ChildMailerId
    /// </summary>
    string? ChildMailerId = null,

    /// <summary>
    /// Filter by source office ID
    /// </summary>
    string? FromOfficeId = null,

    /// <summary>
    /// Filter by destination office ID
    /// </summary>
    string? ToOfficeId = null,

    /// <summary>
    /// Filter by service type (e.g., "DE" for Express)
    /// </summary>
    string? ServiceTypeId = null,

    /// <summary>
    /// Filter by transport provider type
    /// </summary>
    string? TransportProviderType = null,

    /// <summary>
    /// Filter by transport vehicle type
    /// </summary>
    string? TransportVehicleType = null,

    /// <summary>
    /// Filter by created date from
    /// </summary>
    DateTime? CreatedDateFrom = null,

    /// <summary>
    /// Filter by created date to
    /// </summary>
    DateTime? CreatedDateTo = null,

    /// <summary>
    /// Sort order: 0 = Step ASC, 1 = Step DESC, 2 = FromTime ASC, 3 = FromTime DESC
    /// </summary>
    int SortOrder = 0
);
