﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TMS.PlanningService.Domain.Enum;

public enum PriorityType
{
    /// <summary>
    /// D<PERSON><PERSON> vụ cơ bản
    /// </summary>
    [Description("Dịch vụ cơ bản")]
    BasicService,
    /// <summary>
    /// Dịch vụ gia tăng
    /// </summary>
    [Description("Dịch vụ gia tăng")]
    ExtraService,
    /// <summary>
    /// Trọng lượng tính cước
    /// </summary>
    [Description("Trọng lượng tính cước")]
    ChargeableWeight,
    /// <summary>
    /// Nơi đi
    /// </summary>
    [Description("Nơi đi")]
    Origin,
    /// <summary>
    /// Nơi tới
    /// </summary>
    [Description("Nơi tới")]
    Destination,
}

public enum LogicalOperator
{
    And,
    Or
}

public enum PriorityTypeOperator
{

    /**
     * Nếu khác ChargeableWeight
     **/
    Include,
    Exclude,

    /**
     * Nếu là ChargeableWeight
     **/
    Equal,                
    GreaterThan,        
    LessThan,          
    GreaterThanOrEqual,   
    LessThanOrEqual      
}

public enum LocationType
{
    /// <summary>
    /// Tỉnh/Thành phố
    /// </summary>
    [Description("Tỉnh/Thành phố")]
    Province = 1,

    /// <summary>
    /// Phường/Xã
    /// </summary>
    [Description("Phường/Xã")]
    Ward = 2,

    /// <summary>
    /// Vùng
    /// </summary>
    [Description("Vùng")]
    Region = 3,

    /// <summary>
    /// Bưu cục
    /// </summary>
    [Description("Bưu cục")]
    PostOffice = 4,
}

