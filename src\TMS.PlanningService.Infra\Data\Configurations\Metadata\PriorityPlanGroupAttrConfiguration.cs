﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;
namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class PriorityPlanGroupAttrConfiguration : IEntityTypeConfiguration<PriorityPlanGroupAttr>
{
    public void Configure(EntityTypeBuilder<PriorityPlanGroupAttr> builder)
    {
        builder.ToTable("priority_plan_group_attr");

        builder.HasKey(o => new { o.Id });

        builder.Property(x => x.Id)
            .HasColumnName("id");

        builder.Property(o => o.PriorityPlanGroupId)
            .IsRequired()
            .HasColumnName("priority_plan_group_id");

        builder.Property(o => o.PropertyType)
            .IsRequired()
            .HasColumnName("property_type");

        builder.Property(o => o.LocationType)
            .HasColumnName("location_type");

        builder.Property(o => o.PropertyOperator)
            .IsRequired()
            .HasColumnName("property_operator");

        builder.Property(o => o.Values)
            .IsRequired()
            .HasColumnName("values");

        builder.Property(o => o.LogicOperator)
            .HasColumnName("logic_operator");


    }
}
