﻿using System.ComponentModel.DataAnnotations.Schema;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Domain.Entities.Metadata;
public class PriorityPlanGroupAttr : EntityBase
{
    [Column("priority_plan_group_id")]
    public Guid PriorityPlanGroupId { get; set; }

    [Column("property_type")]
    public PriorityType PropertyType { get; set; }

    [Column("location_type")]
    public LocationType? LocationType { get; set; }

    [Column("property_operator")]
    public PriorityTypeOperator PropertyOperator { get; set; }

    [Column("values")]
    public string? Values { get; set; }

    [Column("logic_operator")]
    public LogicalOperator? LogicOperator { get; set; }

    [Column("step_number")]
    public int StepNumber { get; set; }
}
