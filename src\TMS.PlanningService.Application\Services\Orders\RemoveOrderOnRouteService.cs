using Mapster;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Application.Services.Step2.PriorityPlan;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Services.Orders;

/// <summary>
/// Service for removing orders from routes across all aggregation types (normal, daily, priority)
/// Encapsulates the logic for finding routes by MailerId and delegating removal to aggregation services
/// </summary>
public class RemoveOrderOnRouteService : IRemoveOrderOnRoute
{
    private readonly ILogger<RemoveOrderOnRouteService> _logger;
    private readonly IBaseRepository<MailerPlanRouteEntity> _planRouteRepository;
    private readonly IPlanningAggregationService _planningAggregationService;
    private readonly IDailyPlanningAggregationService _dailyPlanningAggregationService;
    private readonly IPriorityPlanningAggregationService _priorityPlanningAggregationService;

    public RemoveOrderOnRouteService(
        ILogger<RemoveOrderOnRouteService> logger,
        IBaseRepository<MailerPlanRouteEntity> planRouteRepository,
        IPlanningAggregationService planningAggregationService,
        IDailyPlanningAggregationService dailyPlanningAggregationService,
        IPriorityPlanningAggregationService priorityPlanningAggregationService)
    {
        _logger = logger;
        _planRouteRepository = planRouteRepository;
        _planningAggregationService = planningAggregationService;
        _dailyPlanningAggregationService = dailyPlanningAggregationService;
        _priorityPlanningAggregationService = priorityPlanningAggregationService;
    }

    /// <summary>
    /// Removes order from all route aggregations (normal, daily, priority) by MailerId
    /// </summary>
    /// <param name="mailerId">Mailer ID (Order ID) to remove from routes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>True if routes were found and order removed successfully, false if no routes found</returns>
    public async Task<bool> RemoveOrderAsync(string mailerId, CancellationToken cancellationToken = default)
    {
        // Log receipt of message for debugging/tracking
        _logger.LogInformation(
            "Processing order removal - MailerId: {MailerId}",
            mailerId);

        // Query all plan routes related to this MailerId
        var planRoutes = await _planRouteRepository.FindAsync(p => p.MailerId == mailerId);

        // If no routes found, skip this ID but continue with others
        if (planRoutes == null || !planRoutes.Any())
        {
            _logger.LogInformation(
                "No plan routes found for MailerId: {MailerId}. Skipping.",
                mailerId);
            return false;
        }

        var data = planRoutes.Adapt<List<MailerPlanRoute>>();

        // Remove orders from route (delegate to domain service)
        // Handle all three aggregation types: normal, daily, priority
        await _planningAggregationService.HandleRemoveOrderOnRouteAsync(data, cancellationToken);

        await _dailyPlanningAggregationService.HandleRemoveOrderOnRouteAsync(data, cancellationToken);

        await _priorityPlanningAggregationService.HandleRemoveOrderOnRouteAsync(data, cancellationToken);

        _logger.LogInformation(
            "Successfully removed order(s) for MailerId: {MailerId}.",
            mailerId);

        return true;
    }
}
