﻿using MediatR;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.IRepository;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Constants.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdatePriorityPlan;

public class UpdatePriorityPlanCommandHandler : IRequestHandler<UpdatePriorityPlanCommand, Guid>
{
    private readonly IPriorityPlanRepository _priorityPlanRepository;
    private readonly IBaseRepository<PriorityPlanGroup> _priorityPlanGroupPropertyRepository;
    private readonly IBaseRepository<PriorityPlanGroupAttr> _priorityPlanGroupAttrRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMetadataCacheService _metadataCacheService;
    private readonly ICurrentFactorProvider _currentFactorProvider;

    public UpdatePriorityPlanCommandHandler(
        IPriorityPlanRepository priorityPlanRepository,
        IBaseRepository<PriorityPlanGroup> priorityGroupPropertyRepository,
        IBaseRepository<PriorityPlanGroupAttr> priorityPropertyRepository,
        IUnitOfWork unitOfWork,
        IMetadataCacheService metadataCacheService,
        ICurrentFactorProvider currentFactorProvider)
    {
        _priorityPlanRepository = priorityPlanRepository;
        _priorityPlanGroupPropertyRepository = priorityGroupPropertyRepository;
        _priorityPlanGroupAttrRepository = priorityPropertyRepository;
        _unitOfWork = unitOfWork;
        _metadataCacheService = metadataCacheService;
        _currentFactorProvider = currentFactorProvider;
    }

    public async Task<Guid> Handle(UpdatePriorityPlanCommand request, CancellationToken cancellationToken)
    {
        var updateDto = request.ParamRequest;
        var companyId = _currentFactorProvider.CompanyId;
        //Check if entity exists
        var entity = await _priorityPlanRepository.GetByIdAsync(updateDto.Id, cancellationToken);
        if (entity == null || entity.IsDeleted)
            throw new Exception($"Priority Route with id {updateDto.Id} not found");

        //Check if name is already taken
        var isExist = await _priorityPlanRepository.ExistsAsync(x => x.PriorityPlanName == updateDto.PriorityPlanName
                                                                    && x.Id != entity.Id
                                                                    && !x.IsDeleted, cancellationToken);
        if (isExist)
            throw new BusinessRuleValidationException(nameof(Domain.Entities.Metadata.PriorityPlan.PriorityPlanName), MessageFormatter.FormatAlreadyExists(nameof(PriorityPlan), updateDto.PriorityPlanName), CommonErrorCodes.MS002);


        // Update main entity
        entity.PriorityPlanName = updateDto.PriorityPlanName;
        entity.Description = updateDto.Description;
        entity.IsActive = updateDto.IsActive;
        _priorityPlanRepository.Update(entity);

        // Get all plan groups related to this priority route
        // Get all attr related to these groups
        var groupProperties = _priorityPlanGroupPropertyRepository.GetQueryable().Where(gp => gp.PriorityPlanId == entity.Id).ToList();
        var groupPropertyIds = groupProperties.Select(gp => gp.Id).ToHashSet();
        var properties = _priorityPlanGroupAttrRepository.GetQueryable().Where(p => groupPropertyIds.Contains(p.PriorityPlanGroupId)).ToList();

        // Dictionaries for easy access
        var groupPropertiesDictionary = groupProperties.ToDictionary(gp => gp.Id, gp => gp);
        var propertiesDictionary = properties.ToDictionary(p => p.Id, p => p);

        // Delete groups that are not in the update request
        var groupToDelete = groupProperties.Where(gp => !updateDto.PriorityPlanGroups.Any(ugp => ugp.Id == gp.Id)).ToList();
        var groupToDeleteIds = groupToDelete.Select(gp => gp.Id).ToHashSet();
        var propertiesToDelete = properties.Where(p => groupToDeleteIds.Contains(p.PriorityPlanGroupId)).ToList();
        if (propertiesToDelete != null && propertiesToDelete.Any())
            _priorityPlanGroupAttrRepository.RemoveRange(propertiesToDelete);

        if (groupToDelete != null && groupToDelete.Any())
            _priorityPlanGroupPropertyRepository.RemoveRange(groupToDelete);

        //Upsert group
        foreach (var (group, grpIndex) in updateDto.PriorityPlanGroups.Select((value, i) => (value, i)))
        {
            PriorityPlanGroup groupProperty;
            if (groupPropertiesDictionary.TryGetValue(group.Id, out var updateGroup))
            {
                updateGroup.LogicOperator = group.LogicOperator;
                _priorityPlanGroupPropertyRepository.Update(updateGroup);

                if (group.PriorityPlanGroupAttributes != null)
                {
                    //============Handle property here===========//
                    // Lấy property hiện tại thuộc group
                    var currentProperties = propertiesDictionary.Values.Where(p => p.PriorityPlanGroupId == group.Id).ToList();

                    // Xoá property không còn trong DTO
                    var deletedProperties = currentProperties.Where(p => !group.PriorityPlanGroupAttributes.Any(dto => dto.Id == p.Id)).ToList();
                    _priorityPlanGroupAttrRepository.RemoveRange(deletedProperties);

                    //Upsert property
                    foreach (var (property, attrIndex) in group.PriorityPlanGroupAttributes.Select((value, i) => (value, i)))
                    {
                        if (propertiesDictionary.TryGetValue(property.Id, out var updateProperties))
                        {
                            updateProperties.PropertyType = property.PropertyType;
                            updateProperties.LocationType = property.LocationType;
                            updateProperties.PropertyOperator = property.PropertyOperator;
                            updateProperties.Values = property.Values;
                            updateProperties.LogicOperator = property.LogicOperator;
                            _priorityPlanGroupAttrRepository.Update(updateProperties);
                        }
                        else
                        {
                            var newProperty = new PriorityPlanGroupAttr
                            {
                                LocationType = property.LocationType,
                                LogicOperator = property.LogicOperator,
                                PropertyOperator = property.PropertyOperator,
                                PropertyType = property.PropertyType,
                                Values = property.Values,
                                StepNumber = attrIndex
                            };
                            updateGroup.PriorityAttributes.Add(newProperty);
                            await _priorityPlanGroupAttrRepository.AddAsync(newProperty);
                        }
                    }
                }
            }
            else
            {
                groupProperty = new PriorityPlanGroup
                {
                    PriorityPlanId = entity.Id,
                    LogicOperator = group.LogicOperator,
                    StepNumber = grpIndex,
                    PriorityAttributes = group.PriorityPlanGroupAttributes?.Select((p, index) => new PriorityPlanGroupAttr
                    {
                        LocationType = p.LocationType,
                        LogicOperator = p.LogicOperator,
                        PropertyOperator = p.PropertyOperator,
                        PropertyType = p.PropertyType,
                        Values = p.Values,
                        StepNumber = index
                    }).ToList() ?? new List<PriorityPlanGroupAttr>()
                };
                await _priorityPlanGroupPropertyRepository.AddAsync(groupProperty);
            }
        }

        await _unitOfWork.SaveChangesAsync(cancellationToken);

        //====Redis cache update
        var priorityPlans = await _priorityPlanRepository.GetActivePriorityPlanAsync(cancellationToken);
        await _metadataCacheService.SetPriorityPlansAsync(priorityPlans);
        return entity.Id;
    }
}
