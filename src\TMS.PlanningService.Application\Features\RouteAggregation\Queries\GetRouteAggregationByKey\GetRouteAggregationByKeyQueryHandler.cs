﻿using MediatR;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Common.Queries;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.PlanningService.Application.Features.RouteAggregation.Queries.GetRouteAggregationByKey;

/// <summary>
/// Handler for getting a single route aggregation by FromTime:ToTime:FromOffice:ToOffice key
/// Inherits common Redis-first + DB fallback logic from base class
/// </summary>
public class GetRouteAggregationByKeyQueryHandler
    : RouteAggregationByKeyQueryHandlerBase<GetRouteAggregationByKeyQuery, RouteAggregationDto>,
      IRequestHandler<GetRouteAggregationByKeyQuery, RouteAggregationDto?>
{
    private readonly ILogger<GetRouteAggregationByKeyQueryHandler> _logger;
    private readonly IPlanningAggregationService _planningAggregationService;
    private readonly IBaseRepository<RouteAggregationEntity> _routeAggregationRepository;
    private readonly IBaseRepository<RouteAggregationOrderEntity> _routeAggregationOrderRepository;
    private readonly IMetadataCacheService _metadataCacheService;
    private readonly IRouteServiceApi _routeServiceApi;

    public GetRouteAggregationByKeyQueryHandler(
        IBaseRepository<RouteAggregationEntity> routeAggregationRepository,
        IBaseRepository<RouteAggregationOrderEntity> routeAggregationOrderRepository,
        ILogger<GetRouteAggregationByKeyQueryHandler> logger,
        IPlanningAggregationService planningAggregationService,
        IMetadataCacheService metadataCacheService,
        IRouteServiceApi routeServiceApi)
    {
        _planningAggregationService = planningAggregationService;
        _logger = logger;
        _routeAggregationRepository = routeAggregationRepository;
        _routeAggregationOrderRepository = routeAggregationOrderRepository;
        _metadataCacheService = metadataCacheService;
        _routeServiceApi = routeServiceApi;
    }

    // Base class property implementations
    protected override ILogger Logger => _logger;
    protected override IRouteServiceApi RouteServiceApi => _routeServiceApi;
    protected override IMetadataCacheService MetadataCacheService => _metadataCacheService;
    protected override IBaseRepository<RouteAggregationEntity> RouteAggregationRepository => _routeAggregationRepository;
    protected override IBaseRepository<RouteAggregationOrderEntity> RouteAggregationOrderRepository => _routeAggregationOrderRepository;
    protected override string AggregationType => "normal";


    public async Task<RouteAggregationDto?> Handle(GetRouteAggregationByKeyQuery request, CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            request.ParamRequest.RouteKey,
            request.ParamRequest.Page,
            request.ParamRequest.PageSize,
            request.ParamRequest.SearchTerm,
            cancellationToken);
    }

    /// <summary>
    /// Fetches route aggregation from Redis using IPlanningAggregationService
    /// </summary>
    protected override async Task<RouteAggregationSummary?> GetRouteAggregationFromRedisAsync(
        string routeKey,
        CancellationToken cancellationToken)
    {
        return await _planningAggregationService.GetRouteAggregationAsync(routeKey, cancellationToken);
    }

    /// <summary>
    /// Maps RouteAggregationSummary and related data to RouteAggregationDto.
    /// Uses dictionary for O(1) post office name lookup.
    /// </summary>
    protected override Task<RouteAggregationDto> MapToDtoAsync(
        RouteAggregationSummary summary,
        Dictionary<string, string> postOfficeNames,
        PagedResult<OrderItemDetail>? orderDetails,
        CancellationToken cancellationToken)
    {
        // O(1) lookup using dictionary
        string GetOfficeName(string? code) =>
            code != null && postOfficeNames.TryGetValue(code, out var name) ? name : string.Empty;

        List<OptionsCountDto> MapBreakdown(IEnumerable<OptionsCountDto> source) =>
            source?.Select(t => new OptionsCountDto { Id = t.Id, Name = t.Name, Count = t.Count }).ToList() ?? new();

        var dto = new RouteAggregationDto
        {
            RouteKey = summary.RouteKey,
            FromOfficeId = summary.FromOfficeId,
            FromOfficeName = GetOfficeName(summary.FromOfficeId),
            ToOfficeId = summary.ToOfficeId,
            ToOfficeName = GetOfficeName(summary.ToOfficeId),

            PlanFromTime = summary.FromTime,
            PlanToTime = summary.ToTime,
            ActualFromTime = summary.ActualFromTime,
            ActualToTime = summary.ActualToTime,

            TotalDurationMinutes = summary.TotalDurationMinutes,
            AverageDurationMinutes = summary.AverageDurationMinutes,
            EarliestStartTime = summary.EarliestStartTime,
            LatestEndTime = summary.LatestEndTime,

            TotalOrders = summary.TotalOrders,
            TotalItems = summary.TotalItems,
            TotalWeight = summary.TotalWeight,
            TotalRealWeight = summary.TotalRealWeight,
            TotalCalWeight = summary.TotalCalWeight,

            TransportProviderBreakdown = MapBreakdown(summary.TransportProviderBreakdown),
            VehicleTypeBreakdown = MapBreakdown(summary.VehicleTypeBreakdown),
            TransportMethodBreakdown = MapBreakdown(summary.TransportMethodBreakdown),

            PriorityScore = summary.PriorityScore,
            NeedsOptimization = summary.NeedsOptimization,
            AggregatedAt = summary.AggregatedAt,
            OrderDetails = orderDetails
        };

        return Task.FromResult(dto);
    }
}
