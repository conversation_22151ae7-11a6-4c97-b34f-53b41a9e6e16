﻿using MediatR;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Application.Features.Planning.Commands.ReceivePlanning;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernal.Kafka.Abstractions;

namespace TMS.PlanningService.Application.Services.Step1;

public class KafkaPlanningMessageHandler : IMessageHandler<KafkaPlanningWebhookMessage>
{
    private readonly ILogger<KafkaPlanningMessageHandler> _logger;
    private readonly IMediator _mediator;

    public KafkaPlanningMessageHandler(
        ILogger<KafkaPlanningMessageHandler> logger,
        IMediator mediator)
    {
        _logger = logger;
        _mediator = mediator;
    }

    public async Task HandleAsync(
        KafkaPlanningWebhookMessage message,
        MessageContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("PlanningMessageHandler received message - Message ID: {MessageId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                message.Id, message.PlanningRequest.MailerRouteMaster.MailerId, message.PlanningRequest.MailerRouteMaster.ChildMailerId);

            var planningRequest = message.PlanningRequest;

            // Create the receive planning command
            var command = new ReceivePlanningCommand(
                planningRequest.MailerRouteMaster,
                planningRequest.MailerPlanRoutes,
                planningRequest.MailerAdjustRoutes,
                planningRequest.MailerActualRoutes
            );

            // Process the planning
            await _mediator.Send(command, cancellationToken);

            _logger.LogInformation("Successfully processed planning from Kafka - Message ID: {MessageId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                message.Id, planningRequest.MailerRouteMaster.MailerId, planningRequest.MailerRouteMaster.ChildMailerId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process planning from Kafka - Message ID: {MessageId}, MailerId: {MailerId}, ChildMailerId: {ChildMailerId}",
                message.Id, message.PlanningRequest?.MailerRouteMaster?.MailerId, message.PlanningRequest?.MailerRouteMaster?.ChildMailerId);

            // Re-throw to trigger Kafka retry/dead letter queue
            throw;
        }
    }
}
