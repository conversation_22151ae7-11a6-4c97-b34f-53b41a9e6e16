﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.ExtraService.Queries.GetExtraServices;
using TMS.PlanningService.Application.Features.OrderStatus.Queries.GetOrderStatusOptionsQuery;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Orders;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/order-status")]
[Produces("application/json")]
public class OrderStatusController : ControllerBase
{
    private readonly IMediator _mediator;

    public OrderStatusController(IMediator mediator)
    {
        _mediator = mediator;
    }

    ///// <summary>
    ///// Get all order status
    ///// </summary> 
    ///// <returns>List order status</returns>
    //[HttpGet("order-status-options")]
    //[ProducesResponseType(typeof(List<OrderStatusOptionsResponse>), StatusCodes.Status200OK)]
    //public async Task<ActionResult<PagedResult<OrderStatusOptionsResponse>>> OrderStatusOptions()
    //{
    //    var query = new GetOrderStatusOptionsQuery();
    //    var result = await _mediator.Send(query);
    //    return Ok(result);
    //}
}
