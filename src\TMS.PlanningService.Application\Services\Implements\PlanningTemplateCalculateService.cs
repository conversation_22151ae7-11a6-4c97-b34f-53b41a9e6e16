﻿using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Utilities.Logistics;

namespace TMS.PlanningService.Application.Services.Implements;

public class PlanningTemplateCalculateService : IPlanningTemplateCalculateService
{
    private readonly IRouteServiceApi _routeServiceApi;
    public PlanningTemplateCalculateService(IRouteServiceApi routeServiceApi)
    {
        _routeServiceApi = routeServiceApi;
    }

    public async Task CalculateAsync(PlanningTemplateEntity entity, RouteDto? route = null)
    {
        if (route is null) // auto callback when route not provided
        {
            route = await _routeServiceApi.GetRoutesByIdAsync(entity.RouteId);
        }

        if (route is null) return;

        List<(double Latitude, double Longitude)> routePoints = new();
        var totalDuration = TimeSpan.Zero;

        foreach (var detail in entity.Details)
        {
            var postOffice = route.PostOffices.FirstOrDefault(p => p.PostOfficeId == detail.PostOfficeId);
            if (postOffice is not null && postOffice.Latitude != 0 && postOffice.Longitude != 0)
            {
                // Add route point
                routePoints.Add((postOffice.Latitude, postOffice.Longitude));
            }

            // Calculate total duration
            var fromDateTime = DateTime.Today
                .AddDays(detail.FromAddDays)
                .Add(detail.FromTime);

            var toDateTime = DateTime.Today
                .AddDays(detail.ToTimeAddDays)
                .Add(detail.ToTime);

            totalDuration += toDateTime - fromDateTime;
        }

        entity.TotalDistance = CommonFuncs.CalculateRouteDistance(routePoints);
        entity.TotalDuration = totalDuration.TotalHours;
    }
}
