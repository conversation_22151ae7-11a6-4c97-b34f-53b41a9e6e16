﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Common.Queries;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.PlanningService.Application.Features.RouteAggregation.Queries.GetRouteAggregations;

/// <summary>
/// Handler for getting route aggregations using hybrid Redis + PostgreSQL pagination
/// Returns aggregated planning data enriched with order metrics
///
/// PATTERN USAGE EXAMPLE:
/// - Inherits from RouteAggregationQueryHandlerBase (which provides common helper methods)
/// - Implements IRequestHandler for MediatR registration
/// - Implements Redis fetching (hot data)
/// - Implements DB fetching (cold/historical data)
/// - Implements filtering, sorting, and DTO mapping
/// </summary>
public class GetRouteAggregationsQueryHandler
    : RouteAggregationQueryHandlerBase<
        GetRouteAggregationsQuery,
        RouteAggregationDto>,
      IRequestHandler<GetRouteAggregationsQuery, PagedResult<RouteAggregationDto>>
{
    private readonly IPlanningAggregationService _planningAggregationService;
    private readonly ILogger<GetRouteAggregationsQueryHandler> _logger;
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IRouteServiceApi _routeServiceApi;
    private readonly IBaseRepository<RouteAggregationEntity> _routeAggregationRepository;
    private readonly IMetadataCacheService _metadataCacheService;

    // Override: Configure how many pages should be fully covered by Redis
    protected override int RedisExpectedPageCoverage => 3;

    // Override: Provide logger for base class
    protected override ILogger Logger => _logger;

    // Override: Provide route service API for base class
    protected override IRouteServiceApi RouteServiceApi => _routeServiceApi;

    // Override: Provide metadata cache service for base class
    protected override IMetadataCacheService MetadataCacheService => _metadataCacheService;

    public GetRouteAggregationsQueryHandler(
        IPlanningAggregationService planningAggregationService,
        ILogger<GetRouteAggregationsQueryHandler> logger,
        ICurrentFactorProvider currentFactorProvider,
        IRouteServiceApi routeServiceApi,
        IBaseRepository<RouteAggregationEntity> routeAggregationRepository,
        IMetadataCacheService metadataCacheService)
    {
        _planningAggregationService = planningAggregationService;
        _logger = logger;
        _currentFactorProvider = currentFactorProvider;
        _routeServiceApi = routeServiceApi;
        _routeAggregationRepository = routeAggregationRepository;
        _metadataCacheService = metadataCacheService;
    }

    /// <summary>
    /// MediatR Handle method - delegates to base class ExecuteHybridPaginationAsync
    /// </summary>
    public async Task<PagedResult<RouteAggregationDto>> Handle(
        GetRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        var pageNumber = request.ParamRequest.Page;
        var pageSize = request.ParamRequest.PageSize;

        // Filter by DepartmentCode from CurrentFactorProvider
        var departmentCode = _currentFactorProvider.DepartmentCode;

        // TODO: apply later.
        //if (string.IsNullOrEmpty(departmentCode))
        //{
        //    return new PagedResult<RouteAggregationDto>(
        //        new List<RouteAggregationDto>(),
        //        0,
        //        pageNumber,
        //        pageSize);
        //}

        // Use base class hybrid pagination logic
        return await ExecuteHybridPaginationAsync(
            request,
            pageNumber,
            pageSize,
            cancellationToken);
    }

    /// <summary>
    /// STEP 1: Fetch hot data from Redis (typically last 7 days)
    /// PERFORMANCE OPTIMIZATION: Filter and paginate on metadata FIRST, then fetch only needed aggregations
    /// </summary>
    protected override async Task<List<RouteAggregationSummary>> FetchFromRedisAsync(
        GetRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Fetching route aggregations from Redis cache");

            var pageNumber = request.ParamRequest.Page;
            var pageSize = request.ParamRequest.PageSize;

            // STEP 1: Get route metadata (lightweight - only keys and timestamps)
            var allMetadata = await _planningAggregationService.GetRouteMetadataListAsync(cancellationToken);

            if (!allMetadata.Any())
            {
                _logger.LogInformation("No route metadata found in Redis");
                return new List<RouteAggregationSummary>();
            }

            _logger.LogInformation("Fetched {Count} metadata items from Redis", allMetadata.Count);

            // STEP 2: Apply filters on metadata (very fast - no heavy data yet)
            var filteredMetadata = ApplyMetadataFilters(allMetadata, request.ParamRequest);

            // STEP 3: Sort metadata by RouteKey (chronological by FromTime:ToTime)
            // RouteKey format: "FromTime:ToTime:FromOfficeId:ToOfficeId"
            // Sorting by RouteKey automatically sorts by FromTime descending (most recent first)
            var sortedMetadata = filteredMetadata.OrderByDescending(m => m.RouteKey).ToList();

            _logger.LogInformation("After filtering: {Count} metadata items", sortedMetadata.Count);

            // IMPORTANT: Set estimated total count for accurate pagination
            // This tells the base class the ACTUAL total count from filtered metadata
            // Without this, pagination would only see the current page's items
            EstimatedTotalCount = sortedMetadata.Count;

            // STEP 4: Paginate metadata to determine which aggregations we need
            var pagedMetadata = sortedMetadata
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            if (!pagedMetadata.Any())
            {
                _logger.LogInformation("No metadata items for page {Page}", pageNumber);
                return new List<RouteAggregationSummary>();
            }

            // STEP 5: Extract route keys for ONLY the current page
            var routeKeysToFetch = pagedMetadata.Select(m => m.RouteKey).ToList();

            _logger.LogInformation(
                "Fetching {Count} aggregations for page {Page} (out of {Total} total after filter)",
                routeKeysToFetch.Count,
                pageNumber,
                sortedMetadata.Count);

            // STEP 6: Fetch ONLY the aggregations needed for this page (batch fetch from Redis)
            var aggregations = await _planningAggregationService.GetRouteAggregationsAsync(
                routeKeysToFetch,
                cancellationToken);

            // IMPORTANT: Tell base class we've already paginated the data
            // This prevents double pagination (which would cause page 2+ to be empty)
            IsPaginationAlreadyDone = true;

            return aggregations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching aggregations from Redis");
            return new List<RouteAggregationSummary>();
        }
    }

    /// <summary>
    /// STEP 2: Fetch cold/historical data from PostgreSQL
    /// Applies filters at DB level for performance
    /// </summary>
    protected override async Task<List<RouteAggregationSummary>> FetchFromDatabaseAsync(
        GetRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            var query = _routeAggregationRepository.GetQueryable()
                .Where(ra => ra.AggregationType == "normal");  // Filter by aggregation type

            // For data without orders, it should not be displayed.
            //query = query.Where(ra => ra.TotalOrders > 0);
             
            // Apply filters at DB level for performance
            var paramRequest = request.ParamRequest;

            if (paramRequest.FromOfficeIds != null && paramRequest.FromOfficeIds.Any())
            {
                query = query.Where(ra => paramRequest.FromOfficeIds.Contains(ra.FromOfficeId));
            }

            if (paramRequest.ToOfficeIds != null && paramRequest.ToOfficeIds.Any())
            {
                query = query.Where(ra => paramRequest.ToOfficeIds.Contains(ra.ToOfficeId));
            }

            if (paramRequest.StartPlanFromDate.HasValue)
            {
                query = query.Where(ra => ra.FromTime >= paramRequest.StartPlanFromDate);
            }

            if (paramRequest.EndPlanFromDate.HasValue)
            {
                query = query.Where(ra => ra.FromTime <= paramRequest.EndPlanFromDate);
            }

            if (paramRequest.StartPlanToDate.HasValue)
            {
                query = query.Where(ra => ra.ToTime >= paramRequest.StartPlanToDate);
            }

            if (paramRequest.EndPlanToDate.HasValue)
            {
                query = query.Where(ra => ra.ToTime <= paramRequest.EndPlanToDate);
            }

            // Order by FromTime descending (most recent first) and take more than requested
            // to account for filtering and pagination
            var entities = await query
                .OrderByDescending(ra => ra.FromTime)
                .Skip((request.ParamRequest.Page - 1) * request.ParamRequest.PageSize)
                .Take(request.ParamRequest.PageSize * RedisExpectedPageCoverage)  // Fetch extra to account for filters
                .ToListAsync(cancellationToken);

            // Map to RouteAggregationSummary
            var aggregations = entities.Select(MapEntityToSummary).ToList();

            _logger.LogInformation("Fetched {Count} aggregations from database", aggregations.Count);

            return aggregations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching aggregations from database");
            return new List<RouteAggregationSummary>();
        }
    }

    /// <summary>
    /// STEP 3: Apply in-memory filters to the merged data
    /// </summary>
    protected override List<RouteAggregationSummary> ApplyFilters(
        List<RouteAggregationSummary> aggregations,
        GetRouteAggregationsQuery request)
    {
        var paramRequest = request.ParamRequest;
        var query = aggregations.AsEnumerable();

        // For data without orders, it should not be displayed.
        //query = query.Where(ra => ra.TotalOrders > 0);

        // Filter by FromOfficeId
        if (paramRequest.FromOfficeIds != null && paramRequest.FromOfficeIds.Any())
        {
            query = query.Where(a => paramRequest.FromOfficeIds.Contains(a.FromOfficeId));
        }

        // Filter by ToOfficeId
        if (paramRequest.ToOfficeIds != null && paramRequest.ToOfficeIds.Any())
        {
            query = query.Where(a => paramRequest.ToOfficeIds.Contains(a.ToOfficeId));
        }

        // Filter by VehicleTypeIds
        if (paramRequest.VehicleTypeIds != null && paramRequest.VehicleTypeIds.Any())
        {
            query = query.Where(a => a.VehicleTypeBreakdown.Any(v => paramRequest.VehicleTypeIds.Contains(v.Id)));
        }

        // Filter by PlanFromDate
        if (paramRequest.StartPlanFromDate.HasValue)
        {
            query = query.Where(a => a.FromTime >= paramRequest.StartPlanFromDate);
        }
        if (paramRequest.EndPlanFromDate.HasValue)
        {
            query = query.Where(a => a.FromTime <= paramRequest.EndPlanFromDate);
        }

        // Filter by PlanToDate
        if (paramRequest.StartPlanToDate.HasValue)
        {
            query = query.Where(a => a.ToTime >= paramRequest.StartPlanToDate);
        }
        if (paramRequest.EndPlanToDate.HasValue)
        {
            query = query.Where(a => a.ToTime <= paramRequest.EndPlanToDate);
        }

        // Search term (searches in route key, office IDs)
        if (!string.IsNullOrEmpty(paramRequest.SearchTerm))
        {
            var searchTerm = paramRequest.SearchTerm.ToLowerInvariant();
            query = query.Where(a =>
                a.OrderDetails.Keys.Any(x => x.ToLowerInvariant().Contains(searchTerm)) ||
                a.FromOfficeId.ToLowerInvariant().Contains(searchTerm) ||
                a.ToOfficeId.ToLowerInvariant().Contains(searchTerm));
        }

        return query.ToList();
    }

    /// <summary>
    /// STEP 4: Apply sorting to the filtered data
    /// </summary>
    protected override List<RouteAggregationSummary> ApplySorting(
        List<RouteAggregationSummary> aggregations,
        GetRouteAggregationsQuery request)
    {
        return aggregations.OrderByDescending(a => a.RouteKey).ToList();
    }

    /// <summary>
    /// STEP 5: Map summary to DTO with enrichment (post office names)
    /// </summary>
    protected override async Task<RouteAggregationDto> MapToDtoAsync(
        RouteAggregationSummary summary,
        string dataSource,
        CancellationToken cancellationToken)
    {
        // Note: We fetch post office data once for the entire page in a batch
        // This is not ideal for the base class pattern, but works for this specific case
        // For true per-item mapping, you'd need to fetch post offices in bulk before mapping
        var postOffices = await FetchPostOfficeDataAsync(new List<RouteAggregationSummary> { summary });

        return new RouteAggregationDto
        {
            RouteKey = summary.RouteKey,
            FromOfficeId = summary.FromOfficeId,
            FromOfficeName = postOffices.FirstOrDefault(po => po.PostOfficeCode == summary.FromOfficeId)?.PostOfficeName ?? string.Empty,
            ToOfficeId = summary.ToOfficeId,
            ToOfficeName = postOffices.FirstOrDefault(po => po.PostOfficeCode == summary.ToOfficeId)?.PostOfficeName ?? string.Empty,
            ActualFromTime = summary.ActualFromTime,
            ActualToTime = summary.ActualToTime,
            PlanFromTime = summary.FromTime,
            PlanToTime = summary.ToTime,
            TotalDurationMinutes = summary.TotalDurationMinutes,
            AverageDurationMinutes = summary.AverageDurationMinutes,
            EarliestStartTime = summary.EarliestStartTime,
            LatestEndTime = summary.LatestEndTime,
            TotalOrders = summary.TotalOrders,
            TotalItems = summary.TotalItems,
            TotalWeight = summary.TotalWeight,
            TotalRealWeight = summary.TotalRealWeight,
            TotalCalWeight = summary.TotalCalWeight,
            TotalDiffWeight = summary.TotalDiffWeight,
            TransportProviderBreakdown = summary.TransportProviderBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            VehicleTypeBreakdown = summary.VehicleTypeBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            TransportMethodBreakdown = summary.TransportMethodBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            PriorityScore = summary.PriorityScore,
            NeedsOptimization = summary.NeedsOptimization,
            AggregatedAt = summary.AggregatedAt
            // Note: dataSource parameter available for debugging/monitoring
        };
    }

    // ============================================================================
    // HELPER METHODS (specific to this handler)
    // ============================================================================
    // Note: Common methods (ParseJsonBreakdown, FetchPostOfficeDataAsync, MapEntityToSummary, GetUniqueKey)
    //       are now in RouteAggregationQueryHandlerBase to avoid duplication

    /// <summary>
    /// Applies filters to lightweight metadata (BEFORE fetching full aggregations)
    /// This is the key performance optimization - filter on small objects first
    /// </summary>
    private List<RouteMetadata> ApplyMetadataFilters(
        List<RouteMetadata> metadata,
        GetRouteAggregationsRequest request)
    {
        var query = metadata.AsEnumerable();

        // Filter by FromOfficeId (extract from RouteKey)
        if (request.FromOfficeIds != null && request.FromOfficeIds.Any())
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                return parts.Length >= 4 && request.FromOfficeIds.Contains(parts[2]);
            });
        }

        // Filter by ToOfficeId (extract from RouteKey)
        if (request.ToOfficeIds != null && request.ToOfficeIds.Any())
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                return parts.Length >= 4 && request.ToOfficeIds.Contains(parts[3]);
            });
        }

        // Filter by date range on FromTime (extract from RouteKey)
        // RouteKey format: "yyyyMMddHHmm:yyyyMMddHHmm:FromOfficeId:ToOfficeId"
        // Example: "202501151030:202501151200:HN001:HCM001"
        // Compare by date only (yyyyMMdd)
        if (request.StartPlanFromDate.HasValue || request.EndPlanFromDate.HasValue)
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                if (parts.Length < 4 || parts[0].Length < 8)
                    return false;

                // Extract date portion (yyyyMMdd) from datetime (yyyyMMddHHmm)
                var fromDateStr = parts[0].Substring(0, 8);
                if (!DateTime.TryParseExact(fromDateStr, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var fromDate))
                    return false;

                if (request.StartPlanFromDate.HasValue && fromDate.Date < request.StartPlanFromDate.Value.Date)
                    return false;

                if (request.EndPlanFromDate.HasValue && fromDate.Date > request.EndPlanFromDate.Value.Date)
                    return false;

                return true;
            });
        }

        // Filter by date range on ToTime (extract from RouteKey)
        if (request.StartPlanToDate.HasValue || request.EndPlanToDate.HasValue)
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                if (parts.Length < 4 || parts[1].Length < 8)
                    return false;

                // Extract date portion (yyyyMMdd) from datetime (yyyyMMddHHmm)
                var toDateStr = parts[1].Substring(0, 8);
                if (!DateTime.TryParseExact(toDateStr, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var toDate))
                    return false;

                if (request.StartPlanToDate.HasValue && toDate.Date < request.StartPlanToDate.Value.Date)
                    return false;

                if (request.EndPlanToDate.HasValue && toDate.Date > request.EndPlanToDate.Value.Date)
                    return false;

                return true;
            });
        }

        // Can not to search by order code.
        // Search term on route key
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLowerInvariant();
            query = query.Where(m => m.RouteKey.ToLowerInvariant().Contains(searchTerm));
        }

        return query.ToList();
    }
}
