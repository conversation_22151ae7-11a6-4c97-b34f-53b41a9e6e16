using System.Linq.Expressions;
using System.Threading.Tasks;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Common.Queries;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.PlanningService.Application.Features.RouteAggregation.Queries.GetRouteAggregations;

/// <summary>
/// Handler for getting route aggregations using hybrid Redis + PostgreSQL pagination
/// Returns aggregated planning data enriched with order metrics
///
/// PATTERN USAGE EXAMPLE:
/// - Inherits from RouteAggregationQueryHandlerBase (which provides common helper methods)
/// - Implements IRequestHandler for MediatR registration
/// - Implements Redis fetching (hot data)
/// - Implements DB fetching (cold/historical data)
/// - Implements filtering, sorting, and DTO mapping
/// </summary>
public class GetRouteAggregationsQueryHandler
    : RouteAggregationQueryHandlerBase<
        GetRouteAggregationsQuery,
        RouteAggregationDto>,
      IRequestHandler<GetRouteAggregationsQuery, PagedResult<RouteAggregationDto>>
{
    private readonly IPlanningAggregationService _planningAggregationService;
    private readonly ILogger<GetRouteAggregationsQueryHandler> _logger;
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IRouteServiceApi _routeServiceApi;
    private readonly IBaseRepository<RouteAggregationEntity> _routeAggregationRepository;
    private readonly IMetadataCacheService _metadataCacheService;
    private readonly IBaseRepository<MailerRouteMasterEntity> _routeMasterRepository;

    // Override: Configure how many pages should be fully covered by Redis
    protected override int RedisExpectedPageCoverage => 3;

    // Override: Provide logger for base class
    protected override ILogger Logger => _logger;

    // Override: Provide route service API for base class
    protected override IRouteServiceApi RouteServiceApi => _routeServiceApi;

    // Override: Provide metadata cache service for base class
    protected override IMetadataCacheService MetadataCacheService => _metadataCacheService;

    public GetRouteAggregationsQueryHandler(
        IPlanningAggregationService planningAggregationService,
        ILogger<GetRouteAggregationsQueryHandler> logger,
        ICurrentFactorProvider currentFactorProvider,
        IRouteServiceApi routeServiceApi,
        IBaseRepository<RouteAggregationEntity> routeAggregationRepository,
        IMetadataCacheService metadataCacheService,
        IBaseRepository<MailerRouteMasterEntity> routeMasterRepository)
    {
        _planningAggregationService = planningAggregationService;
        _logger = logger;
        _currentFactorProvider = currentFactorProvider;
        _routeServiceApi = routeServiceApi;
        _routeAggregationRepository = routeAggregationRepository;
        _metadataCacheService = metadataCacheService;
        _routeMasterRepository = routeMasterRepository;

    }

    // Cache for post office data to avoid multiple API calls per page
    private Dictionary<string, string>? _postOfficeNamesCache;

    /// <summary>
    /// MediatR Handle method - OPTIMIZED to fetch post office data in bulk
    /// This eliminates N+1 API calls by fetching all post office data once per page
    /// </summary>
    public async Task<PagedResult<RouteAggregationDto>> Handle(
        GetRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        var pageNumber = request.ParamRequest.Page;
        var pageSize = request.ParamRequest.PageSize;

        // Filter by DepartmentCode from CurrentFactorProvider
        var departmentCode = _currentFactorProvider.DepartmentCode;

        // TODO: apply later.
        //if (string.IsNullOrEmpty(departmentCode))
        //{
        //    return new PagedResult<RouteAggregationDto>(
        //        new List<RouteAggregationDto>(),
        //        0,
        //        pageNumber,
        //        pageSize);
        //}

        // Clear cache for new request
        _postOfficeNamesCache = null;

        // Use optimized hybrid pagination with bulk post office fetching
        return await ExecuteOptimizedHybridPaginationAsync(
            request,
            pageNumber,
            pageSize,
            cancellationToken);
    }

    /// <summary>
    /// OPTIMIZED: Custom pagination that fetches post office data in bulk before mapping
    /// This eliminates N+1 API calls by fetching all post office data once per page
    /// </summary>
    private async Task<PagedResult<RouteAggregationDto>> ExecuteOptimizedHybridPaginationAsync(
        GetRouteAggregationsQuery request,
        int pageNumber,
        int pageSize,
        CancellationToken cancellationToken)
    {
        // Reset flags for each request
        EstimatedTotalCount = null;
        IsPaginationAlreadyDone = false;

        // STEP 1: Try Redis first (hot data - recent aggregations)
        Logger.LogInformation("Fetching data - Page {Page}, Size {Size}", pageNumber, pageSize);

        var redisData = await FetchFromRedisAsync(request, cancellationToken);

        // STEP 2: Check if Redis has sufficient data
        var isRedisDataSufficient = IsPaginationAlreadyDone || redisData.Count >= pageSize;

        List<RouteAggregationSummary> finalData;
        string dataSource;

        if (isRedisDataSufficient && redisData.Any())
        {
            // Redis has enough data - use it (fast path)
            finalData = redisData;
            dataSource = "Redis";
            Logger.LogInformation("Using Redis data only - {Count} items found", redisData.Count);
        }
        else
        {
            // STEP 3: Fetch from database (cold/historical data)
            Logger.LogInformation("Redis insufficient ({Count} items) - fetching from database", redisData.Count);

            // Reset metadata-first optimization flags for hybrid mode
            IsPaginationAlreadyDone = false;
            EstimatedTotalCount = null;
            Logger.LogInformation("Reset metadata-first optimization flags for hybrid mode");

            var dbData = await FetchFromDatabaseAsync(request, cancellationToken);

            // STEP 4: Merge Redis + DB data (deduplicate by unique key)
            finalData = MergeAndDeduplicate(redisData, dbData);
            dataSource = redisData.Any() ? "Redis+DB (Hybrid)" : "DB";

            Logger.LogInformation("Merged data: Redis={RedisCount}, DB={DbCount}, Final={FinalCount}",
                redisData.Count, dbData.Count, finalData.Count);
        }

        // STEP 5: Apply filters and sorting (skip if already done in FetchFromRedisAsync)
        List<RouteAggregationSummary> filteredData;
        List<RouteAggregationSummary> sortedData;

        if (IsPaginationAlreadyDone)
        {
            // Metadata-first optimization: data is already filtered, sorted, and paginated
            filteredData = finalData;
            sortedData = finalData;
            Logger.LogInformation("Filters and sorting already done in FetchFromRedisAsync - using pre-processed data");
        }
        else
        {
            // Standard mode: apply filters and sorting in-memory
            filteredData = ApplyFilters(finalData, request);
            sortedData = ApplySorting(filteredData, request);
        }

        // STEP 6: Paginate the final results (skip if already done in FetchFromRedisAsync)
        List<RouteAggregationSummary> pagedData;
        if (IsPaginationAlreadyDone)
        {
            // Metadata-first optimization: data is already paginated for current page
            pagedData = sortedData;
            Logger.LogInformation("Pagination already done in FetchFromRedisAsync - using pre-paginated data");
        }
        else
        {
            // Standard pagination: skip previous pages and take current page
            pagedData = sortedData
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();
        }

        // STEP 7: OPTIMIZATION - Fetch post office data in bulk for the entire page
        if (pagedData.Any())
        {
            Logger.LogInformation("Fetching post office data in bulk for {Count} route aggregations", pagedData.Count);
            var postOffices = await FetchPostOfficeDataAsync(pagedData);

            // Cache the post office names for O(1) lookup during mapping
            _postOfficeNamesCache = postOffices.ToDictionary(
                po => po.PostOfficeCode,
                po => po.PostOfficeName ?? string.Empty);

            Logger.LogInformation("Cached {Count} post office names for efficient mapping", _postOfficeNamesCache.Count);
        }

        // STEP 8: Map to DTOs (with cached post office data)
        var dtos = new List<RouteAggregationDto>();
        foreach (var item in pagedData)
        {
            var dto = await MapToDtoAsync(item, dataSource, cancellationToken);
            dtos.Add(dto);
        }

        // STEP 9: Calculate total count
        int totalCount;
        if (EstimatedTotalCount.HasValue)
        {
            // Use estimated count from metadata-first optimization
            totalCount = EstimatedTotalCount.Value;
        }
        else
        {
            // Calculate from filtered data count
            totalCount = filteredData.Count;
        }

        Logger.LogInformation(
            "Retrieved {Count} items from {Source} (Total: {Total}{EstimatedFlag}, Page: {Page}/{TotalPages})",
            dtos.Count,
            dataSource,
            totalCount,
            EstimatedTotalCount.HasValue ? " (estimated from metadata)" : "",
            pageNumber,
            (totalCount + pageSize - 1) / pageSize);

        return new PagedResult<RouteAggregationDto>(
            dtos,
            totalCount,
            pageNumber,
            pageSize);
    }

    /// <summary>
    /// STEP 1: Fetch hot data from Redis (typically last 7 days)
    /// PERFORMANCE OPTIMIZATION: Filter and paginate on metadata FIRST, then fetch only needed aggregations
    /// </summary>
    protected override async Task<List<RouteAggregationSummary>> FetchFromRedisAsync(
        GetRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Fetching route aggregations from Redis cache");

            var pageNumber = request.ParamRequest.Page;
            var pageSize = request.ParamRequest.PageSize;

            // STEP 1: Get route metadata (lightweight - only keys, timestamps, and filter fields)
            // IMPORTANT: RouteMetadata must include ALL filterable fields:
            // - FromOfficeId, ToOfficeId (already in RouteKey)
            // - FromTime, ToTime (already in RouteKey)
            // - VehicleTypeIds (must be added to RouteMetadata structure)
            // - SearchTerm fields (already in RouteKey)
            var allMetadata = await _planningAggregationService.GetRouteMetadataListAsync(cancellationToken);

            if (!allMetadata.Any())
            {
                _logger.LogInformation("No route metadata found in Redis");
                return new List<RouteAggregationSummary>();
            }

            _logger.LogInformation("Fetched {Count} metadata items from Redis", allMetadata.Count);

            // STEP 2: Apply filters on metadata (very fast - no heavy data yet)
            // All filters MUST be applicable at metadata level for this optimization to work
            var filteredMetadata = await ApplyMetadataFilters(allMetadata, request.ParamRequest);

            // STEP 3: Sort metadata by RouteKey (chronological by FromTime:ToTime)
            // RouteKey format: "FromTime:ToTime:FromOfficeId:ToOfficeId"
            // Sorting by RouteKey automatically sorts by FromTime descending (most recent first)
            var sortedMetadata = filteredMetadata.OrderByDescending(m => m.RouteKey).ToList();

            _logger.LogInformation("After filtering: {Count} metadata items", sortedMetadata.Count);

            // IMPORTANT: Set estimated total count for accurate pagination
            // This tells the base class the ACTUAL total count from filtered metadata
            // Without this, pagination would only see the current page's items
            EstimatedTotalCount = sortedMetadata.Count;

            // STEP 4: Paginate metadata to determine which aggregations we need
            var pagedMetadata = sortedMetadata
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            if (!pagedMetadata.Any())
            {
                _logger.LogInformation("No metadata items for page {Page}", pageNumber);
                return new List<RouteAggregationSummary>();
            }

            // STEP 5: Extract route keys for ONLY the current page
            var routeKeysToFetch = pagedMetadata.Select(m => m.RouteKey).ToList();

            _logger.LogInformation(
                "Fetching {Count} aggregations for page {Page} (out of {Total} total after filter)",
                routeKeysToFetch.Count,
                pageNumber,
                sortedMetadata.Count);

            // STEP 6: Fetch ONLY the aggregations needed for this page (batch fetch from Redis)
            var aggregations = await _planningAggregationService.GetRouteAggregationsAsync(
                routeKeysToFetch,
                cancellationToken);

            // IMPORTANT: Tell base class we've already filtered, sorted, and paginated the data
            // Base class will skip ApplyFilters, ApplySorting, and pagination to avoid double-filtering
            IsPaginationAlreadyDone = true;

            return aggregations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching aggregations from Redis");
            return new List<RouteAggregationSummary>();
        }
    }

    /// <summary>
    /// STEP 2: Fetch cold/historical data from PostgreSQL
    /// Uses estimated count strategy to avoid expensive COUNT queries on large datasets
    /// Similar to Google's "About X results" approach for performance
    /// </summary>
    protected override async Task<List<RouteAggregationSummary>> FetchFromDatabaseAsync(
        GetRouteAggregationsQuery request,
        CancellationToken cancellationToken)
    {
        try
        {
            var paramRequest = request.ParamRequest;

            // Start with base query
            var query = _routeAggregationRepository.GetQueryable()
                .Where(ra => ra.AggregationType == "normal");

            // Apply filters at DB level
            if (paramRequest.FromOfficeIds != null && paramRequest.FromOfficeIds.Any())
            {
                query = query.Where(ra => paramRequest.FromOfficeIds.Contains(ra.FromOfficeId));
            }

            if (paramRequest.ToOfficeIds != null && paramRequest.ToOfficeIds.Any())
            {
                query = query.Where(ra => paramRequest.ToOfficeIds.Contains(ra.ToOfficeId));
            }

            if (paramRequest.StartPlanFromDate.HasValue)
            {
                query = query.Where(ra => ra.FromTime >= paramRequest.StartPlanFromDate);
            }

            if (paramRequest.EndPlanFromDate.HasValue)
            {
                query = query.Where(ra => ra.FromTime <= paramRequest.EndPlanFromDate);
            }

            if (paramRequest.StartPlanToDate.HasValue)
            {
                query = query.Where(ra => ra.ToTime >= paramRequest.StartPlanToDate);
            }

            if (paramRequest.EndPlanToDate.HasValue)
            {
                query = query.Where(ra => ra.ToTime <= paramRequest.EndPlanToDate);
            }

            // Filter by VehicleTypeIds at DB level using JSONB containment
            // PostgreSQL JSONB query: Check if VehicleTypeBreakdownJson contains any of the requested IDs
            if (paramRequest.VehicleTypeIds != null && paramRequest.VehicleTypeIds.Any())
            {
                foreach (var vtId in paramRequest.VehicleTypeIds)
                {
                    var searchPattern = $"{{\"id\":\"{vtId}\"}}";
                    query = query.Where(ra =>
                        EF.Functions.JsonContains(ra.VehicleTypeBreakdownJson, searchPattern));
                }
            }

            // Filter by SearchTerm at DB level
            if (!string.IsNullOrEmpty(paramRequest.SearchTerm))
            {
                var searchTerm = paramRequest.SearchTerm.ToLower();
                query = query.Where(ra =>
                    ra.RouteKey.ToLower().Contains(searchTerm) ||
                    ra.FromOfficeId.ToLower().Contains(searchTerm) ||
                    ra.ToOfficeId.ToLower().Contains(searchTerm));
            }

            // Filter out routes without orders (optimization - avoid fetching empty routes)
            if (paramRequest.ExcludeEmptyRoutes)
            {
                query = query.Where(ra => ra.TotalOrders > 0);
                _logger.LogInformation("Excluding routes without orders at database level (ExcludeEmptyRoutes=true)");
            }

            // Apply sorting - use RouteKey for chronological order (same as metadata path)
            // RouteKey format: FromTime:ToTime:FromOfficeId:ToOfficeId (ISO 8601)
            // Uses existing unique index: uq_route_aggregations_unique_with_coalesce
            query = query.OrderByDescending(ra => ra.RouteKey);

            // OPTIMIZATION: Fetch pageSize + 1 to detect if there are more pages
            // This avoids expensive COUNT(*) query on large datasets (3M rows)
            // Strategy similar to Google's "About X results" approach
            var entitiesToFetch = paramRequest.PageSize + 1;
            var entities = await query
                .Skip((paramRequest.Page - 1) * paramRequest.PageSize)
                .Take(entitiesToFetch)
                .ToListAsync(cancellationToken);

            // Calculate estimated total count based on results
            bool hasMorePages = entities.Count > paramRequest.PageSize;
            int estimatedTotal;

            if (hasMorePages)
            {
                // We got pageSize + 1 results, so there are definitely more pages
                // Estimate: current page items + at least 1 more page
                estimatedTotal = (paramRequest.Page * paramRequest.PageSize) + 1;

                // Remove the extra item we fetched for detection
                entities = entities.Take(paramRequest.PageSize).ToList();
            }
            else
            {
                // We got less than pageSize + 1, so this is the last page
                // We can calculate exact count
                estimatedTotal = ((paramRequest.Page - 1) * paramRequest.PageSize) + entities.Count;
            }

            // Set estimated total count (avoids expensive COUNT query)
            EstimatedTotalCount = estimatedTotal;

            // Tell base class we've already filtered, sorted, and paginated
            IsPaginationAlreadyDone = true;

            // Map to summaries
            var aggregations = entities
                .Select(MapEntityToSummary)
                .ToList();

            _logger.LogInformation(
                "Fetched {Count} aggregations from database (page {Page}, estimated total ~{Total}) with JSONB filtering - no COUNT query",
                aggregations.Count,
                paramRequest.Page,
                estimatedTotal);

            return aggregations;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching aggregations from database");
            return new List<RouteAggregationSummary>();
        }
    }

    /// <summary>
    /// STEP 3: Apply in-memory filters to the merged data
    /// IMPORTANT: This method is NOT called when IsPaginationAlreadyDone = true (metadata-first mode)
    /// Base class skips this to avoid double-filtering
    /// This is only used for hybrid/fallback modes when Redis doesn't have enough data
    /// </summary>
    protected override List<RouteAggregationSummary> ApplyFilters(
        List<RouteAggregationSummary> aggregations,
        GetRouteAggregationsQuery request)
    {
        var paramRequest = request.ParamRequest;
        var query = aggregations.AsEnumerable();

        // For data without orders, it should not be displayed.
        //query = query.Where(ra => ra.TotalOrders > 0);

        // Filter by FromOfficeId
        if (paramRequest.FromOfficeIds != null && paramRequest.FromOfficeIds.Any())
        {
            query = query.Where(a => paramRequest.FromOfficeIds.Contains(a.FromOfficeId));
        }

        // Filter by ToOfficeId
        if (paramRequest.ToOfficeIds != null && paramRequest.ToOfficeIds.Any())
        {
            query = query.Where(a => paramRequest.ToOfficeIds.Contains(a.ToOfficeId));
        }

        // Filter by VehicleTypeIds
        if (paramRequest.VehicleTypeIds != null && paramRequest.VehicleTypeIds.Any())
        {
            query = query.Where(a => a.VehicleTypeBreakdown.Any(v => paramRequest.VehicleTypeIds.Contains(v.Id)));
        }

        // Filter by PlanFromDate
        if (paramRequest.StartPlanFromDate.HasValue)
        {
            query = query.Where(a => a.FromTime >= paramRequest.StartPlanFromDate);
        }
        if (paramRequest.EndPlanFromDate.HasValue)
        {
            query = query.Where(a => a.FromTime <= paramRequest.EndPlanFromDate);
        }

        // Filter by PlanToDate
        if (paramRequest.StartPlanToDate.HasValue)
        {
            query = query.Where(a => a.ToTime >= paramRequest.StartPlanToDate);
        }
        if (paramRequest.EndPlanToDate.HasValue)
        {
            query = query.Where(a => a.ToTime <= paramRequest.EndPlanToDate);
        }

        // Search term (searches in route key, office IDs)
        if (!string.IsNullOrEmpty(paramRequest.SearchTerm))
        {
            var searchTerm = paramRequest.SearchTerm.ToLowerInvariant();
            query = query.Where(a =>
                a.OrderDetails.Keys.Any(x => x.ToLowerInvariant().Contains(searchTerm)) ||
                a.FromOfficeId.ToLowerInvariant().Contains(searchTerm) ||
                a.ToOfficeId.ToLowerInvariant().Contains(searchTerm));
        }

        return query.ToList();
    }

    /// <summary>
    /// STEP 4: Apply sorting to the filtered data
    /// </summary>
    protected override List<RouteAggregationSummary> ApplySorting(
        List<RouteAggregationSummary> aggregations,
        GetRouteAggregationsQuery request)
    {
        return aggregations.OrderByDescending(a => a.RouteKey).ToList();
    }

    /// <summary>
    /// STEP 5: Map summary to DTO with enrichment (post office names)
    /// OPTIMIZED: Uses cached post office data instead of fetching for each item
    /// </summary>
    protected override async Task<RouteAggregationDto> MapToDtoAsync(
        RouteAggregationSummary summary,
        string dataSource,
        CancellationToken cancellationToken)
    {
        // Use cached post office data (populated in bulk before mapping)
        // O(1) lookup using cached dictionary
        string GetOfficeName(string? code) =>
            code != null && _postOfficeNamesCache != null && _postOfficeNamesCache.TryGetValue(code, out var name)
                ? name : string.Empty;

        return new RouteAggregationDto
        {
            RouteKey = summary.RouteKey,
            FromOfficeId = summary.FromOfficeId,
            FromOfficeName = GetOfficeName(summary.FromOfficeId),
            ToOfficeId = summary.ToOfficeId,
            ToOfficeName = GetOfficeName(summary.ToOfficeId),
            ActualFromTime = summary.ActualFromTime,
            ActualToTime = summary.ActualToTime,
            PlanFromTime = summary.FromTime,
            PlanToTime = summary.ToTime,
            TotalDurationMinutes = summary.TotalDurationMinutes,
            AverageDurationMinutes = summary.AverageDurationMinutes,
            EarliestStartTime = summary.EarliestStartTime,
            LatestEndTime = summary.LatestEndTime,
            TotalOrders = summary.TotalOrders,
            TotalItems = summary.TotalItems,
            TotalWeight = summary.TotalWeight,
            TotalRealWeight = summary.TotalRealWeight,
            TotalCalWeight = summary.TotalCalWeight,
            TransportProviderBreakdown = summary.TransportProviderBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            VehicleTypeBreakdown = summary.VehicleTypeBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            TransportMethodBreakdown = summary.TransportMethodBreakdown
                .Select(t => new OptionsCountDto
                {
                    Id = t.Id,
                    Name = t.Name,
                    Count = t.Count
                })
                .ToList(),
            PriorityScore = summary.PriorityScore,
            NeedsOptimization = summary.NeedsOptimization,
            AggregatedAt = summary.AggregatedAt
            // Note: dataSource parameter available for debugging/monitoring
        };
    }

    // ============================================================================
    // HELPER METHODS (specific to this handler)
    // ============================================================================
    // Note: Common methods (ParseJsonBreakdown, FetchPostOfficeDataAsync, MapEntityToSummary, GetUniqueKey)
    //       are now in RouteAggregationQueryHandlerBase to avoid duplication

    /// <summary>
    /// Applies filters to lightweight metadata (BEFORE fetching full aggregations)
    /// This is the key performance optimization - filter on small objects first
    /// IMPORTANT: ALL filterable fields must be available in RouteMetadata for this to work
    /// </summary>
    private async Task<List<RouteMetadata>> ApplyMetadataFilters(
        List<RouteMetadata> metadata,
        GetRouteAggregationsRequest request)
    {
        var query = metadata.AsEnumerable();

        // Filter by FromOfficeId (extract from RouteKey)
        if (request.FromOfficeIds != null && request.FromOfficeIds.Any())
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                return parts.Length >= 4 && request.FromOfficeIds.Contains(parts[2]);
            });
        }

        // Filter by ToOfficeId (extract from RouteKey)
        if (request.ToOfficeIds != null && request.ToOfficeIds.Any())
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                return parts.Length >= 4 && request.ToOfficeIds.Contains(parts[3]);
            });
        }

        // Filter by date range on FromTime (extract from RouteKey)
        // RouteKey format: "yyyyMMddHHmm:yyyyMMddHHmm:FromOfficeId:ToOfficeId"
        // Example: "202501151030:202501151200:HN001:HCM001"
        // Compare by date only (yyyyMMdd)
        if (request.StartPlanFromDate.HasValue || request.EndPlanFromDate.HasValue)
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                if (parts.Length < 4 || parts[0].Length < 8)
                    return false;

                // Extract date portion (yyyyMMdd) from datetime (yyyyMMddHHmm)
                var fromDateStr = parts[0].Substring(0, 8);
                if (!DateTime.TryParseExact(fromDateStr, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var fromDate))
                    return false;

                if (request.StartPlanFromDate.HasValue && fromDate.Date < request.StartPlanFromDate.Value.Date)
                    return false;

                if (request.EndPlanFromDate.HasValue && fromDate.Date > request.EndPlanFromDate.Value.Date)
                    return false;

                return true;
            });
        }

        // Filter by date range on ToTime (extract from RouteKey)
        if (request.StartPlanToDate.HasValue || request.EndPlanToDate.HasValue)
        {
            query = query.Where(m =>
            {
                var parts = m.RouteKey.Split(':');
                if (parts.Length < 4 || parts[1].Length < 8)
                    return false;

                // Extract date portion (yyyyMMdd) from datetime (yyyyMMddHHmm)
                var toDateStr = parts[1].Substring(0, 8);
                if (!DateTime.TryParseExact(toDateStr, "yyyyMMdd", null, System.Globalization.DateTimeStyles.None, out var toDate))
                    return false;

                if (request.StartPlanToDate.HasValue && toDate.Date < request.StartPlanToDate.Value.Date)
                    return false;

                if (request.EndPlanToDate.HasValue && toDate.Date > request.EndPlanToDate.Value.Date)
                    return false;

                return true;
            });
        }

        // Filter by VehicleTypeIds (now available in RouteMetadata)
        if (request.VehicleTypeIds != null && request.VehicleTypeIds.Any())
        {
            query = query.Where(m =>
                m.VehicleTypeIds != null &&
                m.VehicleTypeIds.Any(vtId => request.VehicleTypeIds.Contains(vtId)));
        }

        // Can not to search by order code.
        // Search term on route key
        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            // ==============================
            // Handle filtering by MailerId/ChildMailerId
            // ==============================
            if (request.SearchTerm.Length >= 10)
            {
                // 1. Fetch route masters including related plan & adjust routes
                var mailerRouteMasters = await _routeMasterRepository.FindWithIncludeAsync(
                    predicate: x => x.MailerId == request.SearchTerm || x.ChildMailerId == request.SearchTerm,
                    includes: new Expression<Func<MailerRouteMasterEntity, object>>[]
                    {
                        o => o.PlanRoutes,
                        o => o.AdjustRoutes 
                    });

                // 2. Extract distinct RouteKeys from both PlanRoutes and AdjustRoutes
                var routeKeys = mailerRouteMasters
                    .SelectMany(master => CreateRouteKey(master))
                    .Where(key => !string.IsNullOrWhiteSpace(key))
                    .Distinct()
                    .ToList();

                // 3. Apply filtering
                query = query.Where(m => routeKeys.Any(k => m.RouteKey.Contains(k)));

            }
            else
            {
                var searchTerm = request.SearchTerm.ToLowerInvariant();
                query = query.Where(m => m.RouteKey.ToLowerInvariant().Contains(searchTerm));

            }
        }

        // Filter out routes without orders (optimization - avoid fetching empty route aggregations)
        if (request.ExcludeEmptyRoutes)
        {
            query = query.Where(m => m.TotalOrders > 0);
            _logger.LogInformation("Excluding routes without orders (ExcludeEmptyRoutes=true)");
        }

        // Sort by RouteKey (chronological order - FromTime embedded in RouteKey format)
        // RouteKey format: FromTime:ToTime:FromOfficeId:ToOfficeId (ISO 8601)
        // Lexicographic sort = chronological sort (newest routes first)
        var orderedQuery = query.OrderByDescending(m => m.RouteKey);

        return orderedQuery.ToList();
    }

    /// <summary>
    /// Extract all possible FromTime:ToTime:FromOffice:ToOffice keys from PlanRoutes or AdjustRoutes.
    /// </summary>
    private static IEnumerable<string?> CreateRouteKey(MailerRouteMasterEntity master)
    {
        var routes = master.AdjustRoutes?.Any() == true
        ? master.AdjustRoutes.Select(r => (r.FromPostOfficeId, r.ToPostOfficeId, r.FromTime, r.ToTime))
        : master.PlanRoutes.Select(r => (r.FromPostOfficeId, r.ToPostOfficeId, r.FromTime, r.ToTime));

        return routes.Select(r =>
        {
            // Convert from UTC to local machine time (e.g., UTC+7 for Vietnam)
            var fromTimeStr = r.FromTime.HasValue ? r.FromTime.Value.ToLocalTime().ToString("yyyyMMddHHmm") : "null";
            var toTimeStr = r.ToTime.HasValue ? r.ToTime.Value.ToLocalTime().ToString("yyyyMMddHHmm") : "null";

            if (string.IsNullOrWhiteSpace(r.FromPostOfficeId) ||
                string.IsNullOrWhiteSpace(r.ToPostOfficeId))
                return null;

            return $"{fromTimeStr}:{toTimeStr}:{r.FromPostOfficeId}:{r.ToPostOfficeId}";
        });
    }

}
