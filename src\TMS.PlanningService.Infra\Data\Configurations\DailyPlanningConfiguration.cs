﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Infra.Data.Configurations;

public class DailyPlanningConfiguration : IEntityTypeConfiguration<DailyPlanningEntity>
{
    public void Configure(EntityTypeBuilder<DailyPlanningEntity> builder)
    {
        builder.ToTable("real_plans", "public");

        builder.<PERSON>ey(e => e.Id);

        builder.Property(x => x.Id)
         .HasColumnName("id");

        // Unique constraint: One template can only generate one plan per execution date
        builder.HasIndex(e => new { e.PlanningTemplateId, e.ExecutionDate })
            .IsUnique()
            .HasDatabaseName("uq_real_plan_template_date");

        // Index for querying plans by execution date
        builder.HasIndex(e => new { e.ExecutionDate, e.CompanyId, e.IsActive })
            .HasDatabaseName("ix_real_plan_execution_date");

        // Index for querying plans by company and status
        builder.HasIndex(e => new { e.CompanyId, e.Status, e.ExecutionDate })
            .HasDatabaseName("ix_real_plan_company_status");

        // Index for route lookup
        builder.HasIndex(e => e.RouteId)
            .HasDatabaseName("ix_real_plan_route");

        // Column configurations
        builder.Property(e => e.Code)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(e => e.Name)
            .IsRequired()
            .HasMaxLength(255);

        builder.Property(e => e.RouteCode)
            .IsRequired()
            .HasMaxLength(150);

        builder.Property(e => e.PostOfficeCodes)
            .HasColumnType("text");

        builder.Property(e => e.Status)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue("PENDING");

        builder.Property(e => e.TotalDistance)
            .HasPrecision(18, 2);

        builder.Property(e => e.TotalDuration)
            .HasPrecision(18, 2);

        builder.Property(e => e.IsActive)
            .HasDefaultValue(true);

        builder.Property(e => e.TotalWeight)
            .HasColumnName("total_weight")
            .HasPrecision(18, 2);

        builder.Property(e => e.TotalRealWeight)
            .HasColumnName("total_real_weight")
            .HasPrecision(18, 2);

        builder.Property(e => e.TotalDiffWeight)
           .HasColumnName("total_diff_weight")
           .HasPrecision(18, 2);

        builder.Property(x => x.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(x => x.CreatedBy)
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(x => x.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        builder.Property(x => x.UpdatedBy)
            .HasColumnName("updated_by")
            .IsRequired();

        builder.Property(x => x.IsDeleted)
            .HasColumnName("is_deleted");

        // Relationships
        builder.HasOne(e => e.PlanningTemplate)
            .WithMany()
            .HasForeignKey(e => e.PlanningTemplateId)
            .OnDelete(DeleteBehavior.Restrict);

        // RouteAggregations relationship - uses unified RouteAggregationEntity
        // Only aggregations with AggregationType='daily' should reference DailyPlanning
        builder.HasMany(e => e.RouteAggregations)
            .WithOne(r => r.DailyPlanning)
            .HasForeignKey(r => r.DailyPlanningId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
