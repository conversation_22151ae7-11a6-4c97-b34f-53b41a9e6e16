﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.ExtraService.Queries.GetExtraServices;
using TMS.PlanningService.Contracts.Dto;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers.Metadata;

[ApiController]
[Route("api/v{version:apiVersion}/service-types")]
[Produces("application/json")]
public class ServiceTypeController : ControllerBase
{
    private readonly IMediator _mediator;

    public ServiceTypeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    ///// <summary>
    ///// Get all Serviece Types
    ///// </summary> 
    ///// <returns>List of Serviece Types</returns>
    [HttpGet("")]
    [ProducesResponseType(typeof(List<ServiceTypeDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<ServiceTypeDto>>> GetServiceTypes()
    {
        var query = new GetServiceTypesQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }
}
