﻿using MediatR;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.DeletePlanningTemplate;

public class DeletePlanningTemplateCommandHandler : IRequestHandler<DeletePlanningTemplateCommand, Unit>
{
    private readonly IBaseRepository<PlanningTemplateEntity> _repository;
    private readonly IUnitOfWork _unitOfWork;

    public DeletePlanningTemplateCommandHandler(
        IBaseRepository<PlanningTemplateEntity> repository,
        IUnitOfWork unitOfWork)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Unit> Handle(DeletePlanningTemplateCommand request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetByIdAsync(request.Id, cancellationToken);
        if (entity is null)
            throw new BusinessRuleValidationException(nameof(PlanningTemplateEntity.Id), CommonErrorCodes.MS018);

        _repository.Remove(entity);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return Unit.Value;
    }
}
