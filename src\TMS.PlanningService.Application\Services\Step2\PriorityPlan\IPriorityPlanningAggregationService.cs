﻿using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Planning;

namespace TMS.PlanningService.Application.Services.Step2.PriorityPlan;

/// <summary>
/// Service for calculating priority planning aggregations filtered by DE service type
/// Groups by FromOffice:ToOffice:FromTime routes for express delivery priority calculation
/// Inherits from IPlanningAggregationService for persistence and base functionality
/// </summary>
public interface IPriorityPlanningAggregationService : IPlanningAggregationService
{
     
    /// <summary>
    /// Gets all cached priority route aggregations (DE service type only) from Redis
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of cached priority route aggregation summaries</returns>
    Task<List<RouteAggregationSummary>> GetCurrentPriorityAggregationsAsync(
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets priority aggregation for a specific route (DE service type only)
    /// </summary>
    /// <param name="fromOfficeId">Source office ID</param>
    /// <param name="toOfficeId">Destination office ID</param>
    /// <param name="fromTime">Planned departure time from source office</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Priority route aggregation summary or null if not found</returns>
    Task<RouteAggregationSummary?> GetPriorityRouteAggregationAsync(
        string routeKey, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets lightweight metadata for all priority route aggregations
    /// Used by snapshot job for efficient change detection
    /// </summary>
    /// <returns>List of route metadata with LastPersistedAt</returns>
    Task<List<RouteMetadata>> GetAllRouteMetadataAsync();
}
