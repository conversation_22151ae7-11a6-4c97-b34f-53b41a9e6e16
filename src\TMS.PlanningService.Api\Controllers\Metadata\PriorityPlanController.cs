﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.CreatePriorityPlan;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.DeletePriorityPlan;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdateIsActive;
using TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdatePriorityPlan;
using TMS.PlanningService.Application.Features.PriorityPlan.Queries.GetPriorityPlanById;
using TMS.PlanningService.Application.Features.PriorityRoute.Queries.GetPriorityPlans;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers.Metadata;

[ApiController]
[Route("api/v{version:apiVersion}/priority-plan")]
[Produces("application/json")]
public class PriorityPlanController : ControllerBase
{
    private readonly IMediator _mediator;

    public PriorityPlanController(IMediator mediator)
    {
        _mediator = mediator;
    }

    ///// <summary>
    ///// Get all Priority plans with optional filters
    ///// </summary> 
    ///// <returns>List of Priority plans</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(PagedResult<PriorityPlanDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResult<PriorityPlanDto>>> GetPriorityRoutes([FromBody] GetPriorityPlansRequest request)
    {
        var query = new GetPriorityPlansQuery(request);
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    ///// <summary>
    ///// Get Priority plans by Id
    ///// </summary>
    ///// <param name="id">Priority plans Id</param>
    ///// <returns>Priority plans details</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(PriorityPlanDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<PriorityPlanDto>> GetPriorityRouteById(Guid id)
    {
        var query = new GetPriorityPlanByIdQuery(id);
        var result = await _mediator.Send(query);

        if (result == null)
            return NotFound();

        return Ok(result);
    }

    /// <summary>
    /// Create a new priority plan
    /// </summary>
    /// <param name="request">Priority plan data</param>
    /// <returns>Created priority plan Id</returns>
    [HttpPost]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Guid>> CreatePriorityRoute([FromBody] CreatePriorityPlanRequest request)
    {
        var command = new CreatePriorityPlanCommand(request);
        var result = await _mediator.Send(command);

        return CreatedAtAction(
            nameof(CreatePriorityRoute),
            new { id = result },
            result);
    }

    /// <summary>
    /// Update an existing priority plan
    /// </summary>
    /// <param name="id">Priority plan Id</param>
    /// <param name="request">Updated Priority plan data</param>
    /// <returns>No content</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdatePriorityRoute(Guid id, [FromBody] UpdatePriorityPlanRequest request)
    {
        if (id != request.Id)
            return BadRequest("ID mismatch between route and body");

        var command = new UpdatePriorityPlanCommand(request);
        await _mediator.Send(command);
        return NoContent();
    }

    ///// <summary>
    ///// Delete a priority plan (soft delete)
    ///// </summary>
    ///// <param name="id">Priority plan ID</param>
    ///// <returns>No content</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeletePriorityRoute(Guid id)
    {
        var command = new DeletePriorityPlanCommand(id);
        await _mediator.Send(command);

        return NoContent();
    }

    ///// <summary>
    ///// Update isactive status of a priority plan
    ///// </summary>
    ///// <param name="id">Priority plan ID</param>
    ///// <returns>No content</returns>
    [HttpPatch("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdateIsActive(Guid id, [FromBody] UpdateIsActivePriorityPlanRequest request)
    {
        if (id != request.Id)
            return BadRequest("ID mismatch between route and body");

        var command = new UpdateIsActivePriorityPlanCommand(request);
        await _mediator.Send(command);

        return NoContent();
    }
}
