﻿namespace TMS.PlanningService.Domain.Entities;

public class MailerActualRouteEntity
{
    public string MailerId { get; set; } = string.Empty;
    public string ChildMailerId { get; set; } = string.Empty;
    public string? FromPostOfficeId { get; set; }
    public string? ToPostOfficeId { get; set; }
    public string? LeadTimeId { get; set; }
    public string? TransportProviderType { get; set; }
    public string? TransportProviderTypeName { get; set; }
    public string? TransportVehicleType { get; set; }
    public string? TransportVehicleTypeName { get; set; }
    public string? TransportMethodId { get; set; }
    public string? TransportMethodName { get; set; }
    public DateTime? FromTime { get; set; }
    public int FromTimeDelay { get; set; }
    public DateTime? ToTime { get; set; }
    public int ToTimeDelay { get; set; }
    public int AddDays { get; set; }
    public int Step { get; set; }
    public string? Type { get; set; }
    public string? TypeName { get; set; }
    public int HistoryId { get; set; }
    public int? PreviousStep { get; set; }
    public string? LeadTimeTypeId { get; set; }
    public string? LeadTimeTypeName { get; set; }
    public string? ServiceTypeId { get; set; }
    public string? ServiceTypeName { get; set; }
    public string? ExtraService { get; set; }
    public string? ExtraServiceName { get; set; }

    // Partition alignment field (denormalized from parent)
    public DateTime MasterCreatedDate { get; set; }

    // Navigation property
    public MailerRouteMasterEntity? MailerRouteMaster { get; set; }
}
