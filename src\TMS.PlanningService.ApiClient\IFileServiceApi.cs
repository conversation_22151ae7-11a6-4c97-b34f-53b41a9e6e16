﻿using Refit;

namespace TMS.PlanningService.ApiClient;
public interface IFileServiceApi
{
    [Multipart]
    [Post("/api/v1/files/upload")]
    Task<UploadFileResponse> UploadFileAsync(
        [AliasAs("file")] StreamPart file
    );
}

public record UploadFileResponse(
    Guid FileId,
    string FileName,
    string ContentType,
    long FileSize,
    string Category,
    DateTime UploadedAt,
    string FileUrl
);
