﻿using System.Globalization;
using Google.Protobuf.WellKnownTypes;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using TMS.PlanningService.Application.Services.Orders;
using TMS.PlanningService.Contracts.Orders;
using TMS.PlanningService.Contracts.Planning;
using TMS.SharedKernal.SmoothRedis;

namespace TMS.PlanningService.Application.Services.Step2.Plan;

/// <summary>
/// Service for real-time aggregation of adjust plans by FromTime:ToTime:FromOffice:ToOffice routes
/// Route keys are formatted with time first for easy chronological sorting
/// Updates aggregations by recalculating totals from current orders on each route
/// Maintains full order history in OrderIds dictionary without removing orders when they move
/// </summary>
public class PlanningAggregationService : IPlanningAggregationService
{
    protected readonly ILogger<PlanningAggregationService> _logger;
    protected readonly ISmoothRedis _redis;
    protected readonly IOrderDataService _orderDataService;

    // Redis cache keys (virtual properties to allow override in derived classes)
    protected virtual string RouteAggregationKeyPrefix => "planning:route-aggregation:";
    protected virtual string AllRoutesKey => "planning:all-route-keys";
    protected virtual string LockPrefix => "route:";

    // Lock configuration
    // CRITICAL: Lock expiry MUST be >= lock timeout to prevent premature expiration
    // If lock expires before operation completes, another thread can acquire the same lock!
    // Rule: Lock Expiry >= 3 * Expected Operation Time OR >= Lock Timeout (whichever is larger)
    private const int ROUTE_LOCK_TIMEOUT_SECONDS = 10;      // Max time to wait for lock acquisition
    private const int ROUTE_LOCK_EXPIRY_SECONDS = 30;       // How long lock stays valid (3x timeout for safety)
    private const int ALL_ROUTES_LOCK_TIMEOUT_SECONDS = 5;  // Max time to wait for metadata lock
    private const int ALL_ROUTES_LOCK_EXPIRY_SECONDS = 15;  // How long metadata lock stays valid (3x timeout)

    // Cache expiration
    private const int CACHE_EXPIRATION_DAYS = 7;

    // Priority scoring thresholds
    private const int HIGH_VOLUME_THRESHOLD = 100;
    private const int MEDIUM_VOLUME_THRESHOLD = 50;
    private const int LOW_VOLUME_THRESHOLD = 20;
    private const int HIGH_VOLUME_BONUS = 200;
    private const int MEDIUM_VOLUME_BONUS = 100;
    private const int LOW_VOLUME_BONUS = 50;

    // Optimization thresholds (virtual to allow override in derived classes)
    protected virtual int OptimizationOrderThreshold => 10;
    protected virtual int OptimizationDurationMinutes => 1440; // 24 hours

    // Status Order
    private const string STATUS_DELIVERED = "6"; // DELIVERED


    public PlanningAggregationService(
        ILogger<PlanningAggregationService> logger,
        ISmoothRedis redis,
        IOrderDataService orderDataService)
    {
        _logger = logger;
        _redis = redis;
        _orderDataService = orderDataService;
    }

    /// <summary>
    /// Filters routes based on specific criteria (e.g., service type)
    /// Override in derived classes to implement custom filtering logic
    /// </summary>
    protected virtual bool ShouldIncludeRoute(MailerPlanRoute route)
    {
        // Base implementation: include all routes with valid FromOffice and ToOffice
        return !string.IsNullOrEmpty(route.FromPostOfficeId)
            && !string.IsNullOrEmpty(route.ToPostOfficeId);
    }

    /// <summary>
    /// Additional filtering for order metrics based on service type or other conditions
    /// Override in derived classes to implement custom filtering logic
    /// </summary>
    protected virtual bool ShouldIncludeOrderMetric(OrderMetrics orderMetric)
    {
        // Base implementation: include all valid orders
        return true;
    }

    public virtual async Task UpdateRouteAggregationsAsync(
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        List<MailerPlanRoute> planRoutes,
        bool isUsingAdjustRoutes,
        CancellationToken cancellationToken = default,
        RabbitMqPlanningEvent? originalPlanningEvent = null)
    {
        if (routes == null || !routes.Any())
        {
            _logger.LogWarning("No routes to aggregate");
            return;
        }

        try
        {
            // Remove orders from route segments when the segment time or assigned segment of the order changes
            await RemoveOrderOnRouteAsync(routes, isUsingAdjustRoutes, planRoutes, cancellationToken);

            // Group routes by FromTime:ToTime:FromOffice:ToOffice (using virtual filtering logic)
            var routeGroups = routes
                .Where(ShouldIncludeRoute)
                .GroupBy(r => CreateRouteKey(r.FromPostOfficeId!, r.ToPostOfficeId!, r.FromTime, r.ToTime))
                .ToList();

            if (!routeGroups.Any())
            {
                _logger.LogWarning("No valid routes with FromOffice and ToOffice");
                return;
            }

            _logger.LogInformation(
                "Updating route aggregations for {RouteCount} unique routes from {TotalRoutes} routes",
                routeGroups.Count,
                routes.Count);

            // Process all routes in parallel for better performance
            // Each route has its own distributed lock, so parallel processing is safe
            // Metadata is updated within each UpdateSingleRouteAggregationAsync call
            var updateTasks = routeGroups.Select(routeGroup =>
               PrepareRouteAggregationAsync(routeGroup.Key, routeGroup.ToList(), actualRoutes, isUsingAdjustRoutes, cancellationToken, originalPlanningEvent)
            );

            await Task.WhenAll(updateTasks);

            _logger.LogInformation(
                "Successfully updated {RouteCount} route aggregations",
                routeGroups.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update route aggregations");
            throw;
        }
    }

    public async Task<List<RouteMetadata>> GetRouteMetadataListAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get all route metadata (lightweight - just timestamps and keys)
            var metadataDict = await _redis.Cache.GetAsync<Dictionary<string, RouteMetadata>>(AllRoutesKey);

            if (metadataDict == null || !metadataDict.Any())
            {
                _logger.LogInformation("No route metadata found in cache");
                return new List<RouteMetadata>();
            }

            var metadataList = metadataDict.Values.OrderByDescending(x => x.RouteKey).ToList();

            _logger.LogWarning("Retrieved {RouteCount} route metadata entries from cache", metadataList.Count);

            return metadataList;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get route metadata list");
            return new List<RouteMetadata>();
        }
    }

    public async Task<List<RouteAggregationSummary>> GetCurrentAggregationsAsync(
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Get metadata first (fast)
            var metadata = await GetRouteMetadataListAsync(cancellationToken);

            if (!metadata.Any())
            {
                _logger.LogInformation("No route aggregations found in cache");
                return new List<RouteAggregationSummary>();
            }

            var routeKeys = metadata.Select(m => m.RouteKey).ToList();

            // Use batch operations for better performance - fetches all aggregations in a single Redis pipeline
            var cacheKeys = routeKeys.Select(routeKey => $"{RouteAggregationKeyPrefix}{routeKey}").ToArray();
            var results = await _redis.Batch.Cache.GetManyAsync<RouteAggregationSummary>(cacheKeys);
            var summaries = results.Values.Where(s => s != null).Cast<RouteAggregationSummary>().ToList();

            _logger.LogInformation("Retrieved {RouteCount} route aggregations from cache", summaries.Count);

            return summaries.OrderByDescending(s => s.RouteKey).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current route aggregations");
            return new List<RouteAggregationSummary>();
        }
    }

    public async Task<List<RouteAggregationSummary>> GetRouteAggregationsAsync(
        List<string> routeKeys,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Use batch operations for better performance - fetches all aggregations in a single Redis pipeline
            var cacheKeys = routeKeys.Select(routeKey => $"{RouteAggregationKeyPrefix}{routeKey}").ToArray();

            // Redis stores arrays of aggregations (multiple plans can share same route key)
            var results = await _redis.Batch.Cache.GetManyAsync<List<RouteAggregationSummary>>(cacheKeys);

            // Flatten all arrays into a single list
            var summaries = results.Values
                .Where(list => list != null && list.Any())
                .SelectMany(list => list!)
                .ToList();

            _logger.LogInformation("Retrieved {RouteCount} route aggregations from cache", summaries.Count);

            return summaries.OrderByDescending(s => s.RouteKey).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get current route aggregations");
            return new List<RouteAggregationSummary>();
        }
    }

    public async Task<RouteAggregationSummary?> GetRouteAggregationAsync(
        string routeKey,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var cacheKey = $"{RouteAggregationKeyPrefix}{routeKey}";

            // Redis stores arrays of aggregations (multiple plans can share same route key)
            var summaries = await _redis.Cache.GetAsync<List<RouteAggregationSummary>>(cacheKey);

            if (summaries != null && summaries.Any())
            {
                _logger.LogWarning("Retrieved {Count} route aggregation(s) from cache: {RouteKey}", summaries.Count, routeKey);
                // Return the first one (or you might want to add logic to select a specific one)
                return summaries.First();
            }
            else
            {
                _logger.LogWarning("No aggregation found for route: {RouteKey}", routeKey);
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get route aggregation for {RouteKey}", routeKey);
            return null;
        }
    }

    protected virtual async Task PrepareRouteAggregationAsync(string routeKey,
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        bool isUsingAdjustRoutes,
        CancellationToken cancellationToken,
        object? originalPlanningEvent = null)
    {
        await UpdateSingleRouteAggregationAsync(routeKey, routes, actualRoutes, isUsingAdjustRoutes, cancellationToken, originalPlanningEvent);
    }

    protected virtual async Task UpdateSingleRouteAggregationAsync(
        string routeKey,
        List<MailerPlanRoute> routes,
        List<MailerActualRoute> actualRoutes,
        bool isUsingAdjustRoutes,
        CancellationToken cancellationToken,
        object? originalPlanningEvent = null)
    {
        // Acquire distributed lock to prevent race conditions across multiple instances
        var lockResource = $"{LockPrefix}{routeKey}";
        await using var redisLock = await _redis.Locks.AcquireAsync(
            lockResource,
            expiry: TimeSpan.FromSeconds(ROUTE_LOCK_EXPIRY_SECONDS),
            timeout: TimeSpan.FromSeconds(ROUTE_LOCK_TIMEOUT_SECONDS));

        if (redisLock == null)
        {
            var errorMessage = $"Failed to acquire lock for route {routeKey} aggregation - Timeout after {ROUTE_LOCK_TIMEOUT_SECONDS}s";
            _logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        try
        {
            var cacheKey = $"{RouteAggregationKeyPrefix}{routeKey}";

            // Get existing aggregation or create new one
            var summary = await _redis.Cache.GetAsync<RouteAggregationSummary>(cacheKey)
                ?? CreateNewRouteAggregation(routeKey, routes);

            // Update aggregation with new data
            UpdateAggregationMetrics(summary, routes, actualRoutes);

            // === NEW SIMPLIFIED LOGIC ===
            // 1. Get all mailers on this route from planning data
            // 2. Fetch order metrics from OrderService
            // 3. Group by route and sum up totals
            // 4. Add orders to OrderIds (historical tracking)
            // 5. Mark orders that left as LeftAt = now

            // Group routes by (MailerId, ChildMailerId) to process each unique order item
            var mailerGroups = routes
                .GroupBy(r => new {
                    MailerId = r.MailerId,
                    ChildMailerId = string.IsNullOrEmpty(r.ChildMailerId) ? r.MailerId : r.ChildMailerId
                })
                .ToList();

            if (!mailerGroups.Any())
            {
                _logger.LogWarning("No mailers found for route {RouteKey}", routeKey);
            }
            else
            {
                // Get distinct mailer IDs for batch fetch
                var mailerIds = mailerGroups.Select(g => g.Key.MailerId).Distinct().ToList();

                // Fetch order metrics for all mailers on this route
                // Pass the original planning event for fallback to OrderService if metrics not found
                var orderMetrics = await _orderDataService.GetOrderMetricsByOrderIdsAsync(
                    mailerIds,
                    cancellationToken,
                    originalPlanningEvent);

                // Process ALL orders from planning data, even if delivered/deleted
                // Only count non-deleted, non-delivered orders in the totals
                foreach (var group in mailerGroups)
                {
                    var orderId = group.Key.MailerId;
                    var childMailerId = group.Key.ChildMailerId;

                    if (!orderMetrics.TryGetValue(orderId, out var metrics))
                    {
                        _logger.LogWarning("Order metrics not found for mailer {MailerId} on route {RouteKey}", orderId, routeKey);
                        continue;
                    }

                    // Determine if this order should be counted in aggregation totals
                    var shouldCountInTotals = !metrics.IsDeleted
                        && metrics.CurrentStatusId != STATUS_DELIVERED
                        && ShouldIncludeOrderMetric(metrics);

                    // Aggregate totals ONLY for non-deleted, non-delivered orders
                    if (shouldCountInTotals)
                    {
                        _logger.LogWarning("{RouteKey}: Info before update - Orders: {TotalOrders}, TotalItems: {Items}, TotalWeight: {Weight}kg",
                            routeKey,
                            summary.TotalOrders,
                            summary.TotalItems,
                            summary.TotalWeight);

                        _logger.LogWarning("{RouteKey}: OrderIds before update: {OrderIds}", routeKey, summary.OrderDetails);

                        // Find the specific order item for this child mailer
                        var orderItem = metrics.Items
                            .Where(i => i.OrderItemId == childMailerId)
                            .OrderBy(i => i.OrderItemId) // Sort for consistent ordering
                            .Select(item => new OrderItemDetail
                            {
                                ChildMailerId = item.OrderItemId,
                                MailerId = item.OrderId,
                                Weight = item.Weight,
                                RealWeight = item.RealWeight,
                                CalWeight = item.CalWeight,
                                Status = item.CurrentStatusId,
                                ServiceTypeId = metrics.ServiceTypeId,
                                CurrentParentId = metrics.CurrentParentId,
                                CurrentParentType = metrics.CurrentParentType,
                                CurrentPackingListId = metrics.CurrentPackingListId,
                            })
                            .FirstOrDefault();

                        // Guard against missing order item
                        if (orderItem == null)
                        {
                            _logger.LogWarning("OrderItem not found for ChildMailerId {ChildMailerId} in metrics for {MailerId}", childMailerId, orderId);
                            continue;
                        }

                        // Track ALL orders in OrderDetails, regardless of status
                        // OrderDetails maintains full historical record of orders assigned to this route
                        // Since route key includes time window (FromTime:ToTime), each route represents
                        // a specific time-bound plan. Orders are never removed - they stay as historical data.
                        // Key is just MailerId, Value contains ALL child mailer IDs for that order

                        if (summary.OrderDetails.TryGetValue(orderId, out var existingOrder))
                        {
                            var existingItem = existingOrder.Items.Where(i => i.MailerId == orderId && i.ChildMailerId == childMailerId).FirstOrDefault();

                            if (existingItem is null)
                            {
                                // New item: Add to order and update totals
                                existingOrder.Items.Add(orderItem);

                                //Recalculate order information
                                existingOrder.TotalItems++;
                                existingOrder.Weight += orderItem.Weight;
                                existingOrder.RealWeight += orderItem.RealWeight;
                                existingOrder.CalWeight += orderItem.CalWeight;

                                //Recalculate summary information
                                summary.TotalItems++;
                                summary.TotalWeight += orderItem.Weight;
                                summary.TotalRealWeight += orderItem.RealWeight;
                                summary.TotalCalWeight += orderItem.CalWeight;
                                summary.TotalDiffWeight += (orderItem.RealWeight - orderItem.CalWeight);
                            }
                            else if (existingItem.IsDeleted == true)
                            {
                                // Item was previously deleted, now being restored

                                // If ALL items in this order are currently deleted, restoring one means the order is back
                                // So we need to increment TotalOrders
                                if (!existingOrder.Items.Any(x => x.IsDeleted != true))
                                {
                                    summary.TotalOrders++;
                                }

                                // Restore the item with updated values
                                existingItem.Weight = orderItem.Weight;
                                existingItem.RealWeight = orderItem.RealWeight;
                                existingItem.CalWeight = orderItem.CalWeight;
                                existingItem.Status = orderItem.Status;
                                existingItem.IsDeleted = false;

                                //Recalculate order information
                                existingOrder.TotalItems++;
                                existingOrder.Weight += orderItem.Weight;
                                existingOrder.RealWeight += orderItem.RealWeight;
                                existingOrder.CalWeight += orderItem.CalWeight;

                                //Recalculate summary information
                                summary.TotalItems++;
                                summary.TotalWeight += orderItem.Weight;
                                summary.TotalRealWeight += orderItem.RealWeight;
                                summary.TotalCalWeight += orderItem.CalWeight;
                                summary.TotalDiffWeight += (orderItem.RealWeight - orderItem.CalWeight);
                            }
                            else
                            {
                                // Item exists and is active: Update weights if they changed
                                var weightDelta = orderItem.Weight - existingItem.Weight;
                                var realWeightDelta = orderItem.RealWeight - existingItem.RealWeight;
                                var calWeightDelta = orderItem.CalWeight - existingItem.CalWeight;

                                if (weightDelta != 0 || realWeightDelta != 0 || calWeightDelta != 0)
                                {
                                    // Update item weights
                                    existingItem.Weight = orderItem.Weight;
                                    existingItem.RealWeight = orderItem.RealWeight;
                                    existingItem.CalWeight = orderItem.CalWeight;
                                    existingItem.Status = orderItem.Status;

                                    // Update order totals
                                    existingOrder.Weight += weightDelta;
                                    existingOrder.RealWeight += realWeightDelta;
                                    existingOrder.CalWeight += calWeightDelta;

                                    // Update summary totals
                                    summary.TotalWeight += weightDelta;
                                    summary.TotalRealWeight += realWeightDelta;
                                    summary.TotalCalWeight += calWeightDelta;
                                    summary.TotalDiffWeight += (realWeightDelta - calWeightDelta);

                                    _logger.LogInformation(
                                        "Updated weights for {MailerId}/{ChildMailerId}: Weight Δ={WeightDelta}kg, RealWeight Δ={RealWeightDelta}kg, CalWeight Δ={CalWeightDelta}kg",
                                        orderId, childMailerId, weightDelta, realWeightDelta, calWeightDelta);
                                }
                            }
                        }
                        else
                        {
                            // New order: Create and add to aggregation
                            summary.TotalOrders++;
                            summary.OrderDetails[orderId] = new OrderOnRoute
                            {
                                MailerId = orderId,
                                Status = metrics.CurrentStatusId,
                                ExtraService = metrics.ExtraServices != null
                                    ? string.Join(",", metrics.ExtraServices.Select(e => e.ExtraServiceId))
                                    : null,
                                Weight = 0, // Will be calculated from items
                                RealWeight = 0,
                                CalWeight = 0,
                                ServiceTypeId = metrics.ServiceTypeId,
                                IsDeleted = metrics.IsDeleted
                            };

                            // Add the order item
                            summary.OrderDetails[orderId].Items.Add(orderItem);
                            summary.OrderDetails[orderId].TotalItems++;
                            summary.OrderDetails[orderId].Weight += orderItem.Weight;
                            summary.OrderDetails[orderId].RealWeight += orderItem.RealWeight;
                            summary.OrderDetails[orderId].CalWeight += orderItem.CalWeight;

                            // Update summary totals
                            summary.TotalItems++;
                            summary.TotalWeight += orderItem.Weight;
                            summary.TotalRealWeight += orderItem.RealWeight;
                            summary.TotalCalWeight += orderItem.CalWeight;
                            summary.TotalDiffWeight += (orderItem.RealWeight - orderItem.CalWeight);
                        }

                        _logger.LogWarning("{RouteKey}: Info after update - Orders: {TotalOrders}, TotalItems: {Items}, TotalWeight: {Weight}kg",
                            routeKey,
                            summary.TotalOrders,
                            summary.TotalItems,
                            summary.TotalWeight);

                        _logger.LogWarning("{RouteKey}: OrderIds after update: {OrderIds}", routeKey, summary.OrderDetails);
                    }
                }

                // NOTE: Orders are NEVER removed from OrderIds
                // Each route aggregation (with its specific time window) maintains a historical record
                // If an order moves to a different time slot, it will appear in a different route key
                // The old route key keeps the order as historical data

                // Recalculate derived metrics
                summary.PriorityScore = CalculatePriorityScore(summary);
                summary.NeedsOptimization = summary.TotalOrders > OptimizationOrderThreshold
                    || summary.TotalDurationMinutes > OptimizationDurationMinutes;

                _logger.LogWarning(
                    "Updated route {RouteKey} - Orders: {TotalOrders}, Items: {Items}, Weight: {Weight}kg",
                    routeKey,
                    summary.TotalOrders,
                    summary.TotalItems,
                    summary.TotalWeight);
            }


            if (summary.OrderDetails.Any())
            {
                // Cache with configurable expiration (longer than order aggregations since plan changes less frequently)
                await _redis.Cache.SetAsync(cacheKey, summary, TimeSpan.FromDays(CACHE_EXPIRATION_DAYS));

                // Update metadata in AllRoutesKey for fast filtering
                await UpdateAllRouteMetadataAsync(routeKey, summary, cancellationToken);

                _logger.LogWarning("Updated route aggregation - Route: {RouteKey}, Orders: {TotalOrders}, Items: {TotalItems}, Priority: {Priority}",
                    routeKey,
                    summary.TotalOrders,
                    summary.TotalItems,
                    summary.PriorityScore);
            }
            else
            {
                _logger.LogWarning("No orders found on route {RouteKey} after aggregation - skipping cache update", routeKey);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update route aggregation for {RouteKey}", routeKey);
            throw;
        }
        // Lock automatically released via DisposeAsync
    }

    protected virtual RouteAggregationSummary CreateNewRouteAggregation(string routeKey, List<MailerPlanRoute> routes)
    {
        var routeParts = routeKey.Split(':');

        // Parse FromTime from routeKey (format: "FromTime:ToTime:FromOfficeId:ToOfficeId")
        DateTime? fromTime = null;
        string[] formats = { "yyyyMMddHHmm", "yyyy-MM-dd HH:mm" };
        if (routeParts.Length > 0 && routeParts[0] != "null")
        {
            if (DateTime.TryParseExact(routeParts[0], formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedFromTime))
            {
                fromTime = parsedFromTime;
            }
        }

        // Parse ToTime from routeKey
        DateTime? toTime = null;
        if (routeParts.Length > 1 && routeParts[1] != "null")
        {
            if (DateTime.TryParseExact(routeParts[1], formats, CultureInfo.InvariantCulture, DateTimeStyles.None, out var parsedToTime))
            {
                toTime = parsedToTime;
            }
        }

        var fromOfficeId = routeParts.Length > 2 ? routeParts[2] : string.Empty;
        var toOfficeId = routeParts.Length > 3 ? routeParts[3] : string.Empty;
        var priorityPlanId = routeParts.Length > 4 ? routeParts[4] : string.Empty;

        var now = DateTime.UtcNow;
        return new RouteAggregationSummary
        {
            RouteKey = routeKey,
            FromOfficeId = fromOfficeId,
            ToOfficeId = toOfficeId,
            FromTime = fromTime,
            ToTime = toTime,
            TotalDurationMinutes = 0,
            AverageDurationMinutes = 0,
            TransportProviderBreakdown = new(),
            VehicleTypeBreakdown = new(),
            TransportMethodBreakdown = new(),
            OrderDetails = new(),
            CreatedAt = now,            // Set original creation time
            AggregatedAt = now,         // Set last update time
            PriorityPlanId = string.IsNullOrEmpty(priorityPlanId) ? null : Guid.Parse(priorityPlanId)
        };
    }

    protected virtual void UpdateAggregationMetrics(RouteAggregationSummary summary, List<MailerPlanRoute> routes, List<MailerActualRoute> actualRoutes)
    {
        // Note: TotalOrders will be populated from OrderDataService
        // routes.Count represents number of adjust routes, not the count we need

        // Calculate total and average duration
        var durations = routes.Select(r => r.ToTimeDelay - r.FromTimeDelay + r.AddDays * 24 * 60).ToList();
        summary.TotalDurationMinutes = durations.Sum();
        summary.AverageDurationMinutes = durations.Any() ? durations.Average() : 0;

        // Update time windows (safely handle empty collections)
        var routesWithFromTime = routes.Where(r => r.FromTime.HasValue).ToList();
        summary.EarliestStartTime = routesWithFromTime.Any() ? routesWithFromTime.Min(r => r.FromTime) : null;

        var routesWithToTime = routes.Where(r => r.ToTime.HasValue).ToList();
        summary.LatestEndTime = routesWithToTime.Any() ? routesWithToTime.Max(r => r.ToTime) : null;

        // Calculate actual times from MailerActualRoute (if any)
        // Only from routes with matching FromOfficeId and ToOfficeId
        // Only set if all matching actual routes have the same value, otherwise null
        actualRoutes = actualRoutes
            .Where(r => r.FromPostOfficeId == summary.FromOfficeId && r.ToPostOfficeId == summary.ToOfficeId)
            .ToList();

        if (actualRoutes.Any())
        {
            var actualRoutesWithFromTime = actualRoutes.Where(r => r.FromTime.HasValue).Select(r => r.FromTime!.Value).ToList();
            if (actualRoutesWithFromTime.Any())
            {
                var distinctFromTimes = actualRoutesWithFromTime.Distinct().ToList();
                summary.ActualFromTime = distinctFromTimes.Count == 1 ? distinctFromTimes.First() : null;
            }
            else
            {
                summary.ActualFromTime = null;
            }

            var actualRoutesWithToTime = actualRoutes.Where(r => r.ToTime.HasValue).Select(r => r.ToTime!.Value).ToList();
            if (actualRoutesWithToTime.Any())
            {
                var distinctToTimes = actualRoutesWithToTime.Distinct().ToList();
                summary.ActualToTime = distinctToTimes.Count == 1 ? distinctToTimes.First() : null;
            }
            else
            {
                summary.ActualToTime = null;
            }
        }
        else
        {
            summary.ActualFromTime = null;
            summary.ActualToTime = null;
        }

        // Update transport provider breakdown
        summary.TransportProviderBreakdown = routes
            .Where(r => !string.IsNullOrEmpty(r.TransportProviderType))
            .GroupBy(r => new { r.TransportProviderType, r.TransportProviderTypeName })
            .Select(g => new OptionsCountDto
            {
                Id = g.Key.TransportProviderType ?? string.Empty,
                Name = g.Key.TransportProviderTypeName ?? string.Empty,
                Count = g.Count()
            })
            .OrderByDescending(t => t.Count)
            .ToList();

        // Update vehicle type breakdown
        summary.VehicleTypeBreakdown = routes
            .Where(r => !string.IsNullOrEmpty(r.TransportVehicleType))
            .GroupBy(r => new { r.TransportVehicleType, r.TransportVehicleTypeName })
            .Select(g => new OptionsCountDto
            {
                Id = g.Key.TransportVehicleType ?? string.Empty,
                Name = g.Key.TransportVehicleTypeName ?? string.Empty,
                Count = g.Count()
            })
            .OrderByDescending(t => t.Count)
            .ToList();

        // Update transport method breakdown
        summary.TransportMethodBreakdown = routes
            .Where(r => !string.IsNullOrEmpty(r.TransportMethodId))
            .GroupBy(r => new { r.TransportMethodId, r.TransportMethodName })
            .Select(g => new OptionsCountDto
            {
                Id = g.Key.TransportMethodId ?? string.Empty,
                Name = g.Key.TransportMethodName ?? string.Empty,
                Count = g.Count()
            })
            .OrderByDescending(t => t.Count)
            .ToList();

        // Recalculate priority score
        summary.PriorityScore = CalculatePriorityScore(summary);
        summary.NeedsOptimization = summary.TotalOrders > OptimizationOrderThreshold
            || summary.TotalDurationMinutes > OptimizationDurationMinutes;

        // CreatedAt is immutable - never update it (preserves original creation time)
        // Only update the last aggregation timestamp
        summary.AggregatedAt = DateTime.UtcNow;
    }

    protected virtual int CalculatePriorityScore(RouteAggregationSummary summary)
    {
        var score = 0;

        // More orders = higher priority
        score += summary.TotalOrders * 20;

        // More items = higher priority
        score += summary.TotalItems * 3;

        // Heavier loads = higher priority (1 point per 100kg)
        score += (int)(summary.TotalWeight / 100);

        // Longer total duration = higher priority
        score += summary.TotalDurationMinutes / 60; // Convert to hours

        // Multiple transport types = higher complexity = higher priority
        score += summary.TransportProviderBreakdown.Count * 5;
        score += summary.VehicleTypeBreakdown.Count * 5;
        score += summary.TransportMethodBreakdown.Count * 5;

        // Bonus for high-volume routes
        if (summary.TotalOrders > HIGH_VOLUME_THRESHOLD)
            score += HIGH_VOLUME_BONUS;
        else if (summary.TotalOrders > MEDIUM_VOLUME_THRESHOLD)
            score += MEDIUM_VOLUME_BONUS;
        else if (summary.TotalOrders > LOW_VOLUME_THRESHOLD)
            score += LOW_VOLUME_BONUS;

        return score;
    }

    protected virtual async Task UpdateAllRouteMetadataAsync(string routeKey, RouteAggregationSummary summary, CancellationToken cancellationToken)
    {
        // Acquire distributed lock to prevent race conditions when updating the global route metadata
        await using var redisLock = await _redis.Locks.AcquireAsync(
            "all-routes",
            expiry: TimeSpan.FromSeconds(ALL_ROUTES_LOCK_EXPIRY_SECONDS),
            timeout: TimeSpan.FromSeconds(ALL_ROUTES_LOCK_TIMEOUT_SECONDS));

        if (redisLock == null)
        {
            _logger.LogWarning(
                "Failed to acquire lock for all-routes metadata update - Timeout after {Timeout}s (non-critical, skipping update)",
                ALL_ROUTES_LOCK_TIMEOUT_SECONDS);
            return;
        }

        try
        {
            // Get existing route metadata
            var existingMetadata = await _redis.Cache.GetAsync<Dictionary<string, RouteMetadata>>(AllRoutesKey)
                ?? new Dictionary<string, RouteMetadata>();

            // Update or add metadata for this route
            existingMetadata[routeKey] = new RouteMetadata
            {
                RouteKey = summary.RouteKey,
                AggregatedAt = summary.AggregatedAt,
                LastPersistedAt = summary.LastPersistedAt
            };

            // Store updated metadata with configurable expiration
            await _redis.Cache.SetAsync(AllRoutesKey, existingMetadata, TimeSpan.FromDays(CACHE_EXPIRATION_DAYS));

            _logger.LogWarning("Updated route metadata in all-routes list - Total routes: {TotalRoutes}", existingMetadata.Count);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update route metadata in all-routes list");
            // Non-critical - continue without updating the list
        }
        // Lock automatically released via DisposeAsync
    }

    protected static string CreateRouteKey(string fromOfficeId, string toOfficeId, DateTime? fromTime = null, DateTime? toTime = null)
    {
        var fromTimeStr = fromTime.HasValue ? fromTime.Value.ToString("yyyyMMddHHmm") : "null";
        var toTimeStr = toTime.HasValue ? toTime.Value.ToString("yyyyMMddHHmm") : "null";
        return $"{fromTimeStr}:{toTimeStr}:{fromOfficeId}:{toOfficeId}";
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="totalItems"></param>
    /// <returns></returns>
    protected static int NormalizeTotalItems(int? totalItems) => (totalItems ?? 0) == 0 ? 1 : totalItems.Value;

    /// <summary>
    /// Removes orders from route aggregations using distributed locks and batch operations
    /// Optimized for multi-instance environments with proper concurrency control
    /// </summary>
    /// <param name="isUsingAdjustRoutes">Whether to process adjust routes</param>
    /// <param name="planRoutes">List of plan routes containing orders to remove</param>
    /// <param name="cancellationToken">Cancellation token</param>
    protected virtual async Task RemoveOrderOnRouteAsync(
        List<MailerPlanRoute> adjustRoutes,
        bool isUsingAdjustRoutes,
        List<MailerPlanRoute> planRoutes,
        CancellationToken cancellationToken)
    { 
        if (!isUsingAdjustRoutes)
            return;

        // Step 1: Group plan routes by "From:To" key and collect all unique (MailerId, ChildMailerId) pairs
        // Using GroupBy instead of ToDictionary to handle multiple routes with same From:To
        var routeGroups = planRoutes
            .Where(ShouldIncludeRoute)
            .GroupBy(plan => $"{plan.FromPostOfficeId}:{plan.ToPostOfficeId}")
            .ToDictionary(
                g => g.Key,
                g => g.Select(plan => new
                {
                    MailerId = plan.MailerId,
                    ChildMailerId = string.IsNullOrEmpty(plan.ChildMailerId) ? plan.MailerId : plan.ChildMailerId,
                })
                .Distinct()
                .ToList()
            );

        if (!routeGroups.Any())
        {
            _logger.LogInformation("No routes to process after filtering");
            return;
        }

        // Step 2: Get lightweight metadata from Redis
        var allMetadata = await GetRouteMetadataListAsync(cancellationToken);
        if (allMetadata is null || allMetadata.Count == 0)
        {
            _logger.LogInformation("No route metadata found in Redis — skipping route aggregation fetch");
            return;
        }

        // Step 3: Extract distinct route keys for faster matching
        var routeKeys = allMetadata
            .Select(m => m.RouteKey)
            .Where(k => !string.IsNullOrEmpty(k))
            .ToHashSet();

        // Step 4: Match routeKeys with routeGroups ("EndsWith" comparison for flexible matching)
        // Flatten the list of tuples for each matched route
        // Example: RouteKey "202501071200:202501071300:OfficeA:OfficeB" matches "OfficeA:OfficeB"
        var matchedRoutes = routeKeys
            .SelectMany(rk => routeGroups
                .Where(rg => rk.EndsWith(rg.Key))
                .SelectMany(rg => rg.Value.Select(tuple => new { RouteKey = rk, tupleMailer = tuple })))
            .GroupBy(x => x.RouteKey)
            .ToDictionary(g => g.Key, g => g.Select(x => x.tupleMailer).Distinct().ToList());

        if (!matchedRoutes.Any())
        {
            _logger.LogWarning("No matching route keys found between metadata and plan routes");
            return;
        }

        // ===== OPTIMIZED: BATCH LOCK + BATCH OPERATIONS =====

        // Step 5: Acquire distributed locks for ALL routes in parallel
        var locks = new Dictionary<string, IAsyncDisposable>();
        var cacheKeys = new Dictionary<string, string>(); // routeKey -> cacheKey

        try
        {
            _logger.LogInformation("Acquiring distributed locks for {Count} routes", matchedRoutes.Count);

            // Acquire all locks in parallel for better performance
            var lockTasks = matchedRoutes.Keys.Select(async routeKey =>
            {
                var lockResource = $"{LockPrefix}{routeKey}";
                try
                {
                    var redisLock = await _redis.Locks.AcquireAsync(
                        lockResource,
                        expiry: TimeSpan.FromSeconds(ROUTE_LOCK_EXPIRY_SECONDS),
                        timeout: TimeSpan.FromSeconds(ROUTE_LOCK_TIMEOUT_SECONDS));

                    if (redisLock != null)
                    {
                        lock (locks)
                        {
                            locks[routeKey] = redisLock;
                            cacheKeys[routeKey] = $"{RouteAggregationKeyPrefix}{routeKey}";
                        }
                        return (routeKey, success: true);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to acquire lock for route {RouteKey} (timeout)", routeKey);
                        return (routeKey, success: false);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error acquiring lock for route {RouteKey}", routeKey);
                    return (routeKey, success: false);
                }
            });

            var lockResults = await Task.WhenAll(lockTasks);
            var failedLocks = lockResults.Where(r => !r.success).Select(r => r.routeKey).ToList();

            if (failedLocks.Any())
            {
                _logger.LogWarning(
                    "Failed to acquire locks for {Count}/{Total} routes: {Routes}",
                    failedLocks.Count,
                    matchedRoutes.Count,
                    string.Join(", ", failedLocks));

                // Remove failed routes from processing (graceful degradation)
                foreach (var failedRoute in failedLocks)
                {
                    matchedRoutes.Remove(failedRoute);
                }
            }

            if (!matchedRoutes.Any())
            {
                _logger.LogWarning("No routes left to process after lock acquisition failures");
                return;
            }

            _logger.LogInformation("Successfully acquired locks for {Count} routes", locks.Count);

            // Step 6: Batch fetch all route aggregations (AFTER acquiring locks for fresh data)
            var cacheKeyArray = cacheKeys.Values.ToArray();
            var batchResults = await _redis.Batch.Cache.GetManyAsync<RouteAggregationSummary>(cacheKeyArray);

            _logger.LogInformation("Fetched {Count} route aggregations from Redis using GetManyAsync", batchResults.Count);

            // Step 7: Modify all summaries in memory (no I/O, pure computation)
            var modifiedSummaries = new Dictionary<string, RouteAggregationSummary>();

            foreach (var kvp in matchedRoutes)
            {
                var routeKey = kvp.Key;
                var tupleMailer = kvp.Value;
                var cacheKey = cacheKeys[routeKey];

                if (!batchResults.TryGetValue(cacheKey, out var summary) || summary == null)
                {
                    _logger.LogWarning("RouteAggregationSummary not found in Redis for {RouteKey}", routeKey);
                    continue;
                }

                var removedCount = 0;

                foreach (var mailer in tupleMailer)
                {
                    var mailerId = mailer.MailerId;
                    var childMailerId = mailer.ChildMailerId;

                    if (summary.OrderDetails.TryGetValue(mailerId, out var modifiedOrder))
                    {
                        // Guard against missing Items collection
                        if (modifiedOrder.Items == null || !modifiedOrder.Items.Any())
                        {
                            _logger.LogDebug(
                                "{RouteKey}: Order {MailerId} has no items to remove",
                                routeKey,
                                mailerId);
                            continue;
                        }

                        _logger.LogDebug(
                            "{RouteKey}: Removing order {MailerId} with {ItemCount} child mailer(s)",
                            routeKey,
                            mailerId,
                            modifiedOrder.Items.Count);

                        var removeItem = modifiedOrder.Items.FirstOrDefault(i => i.ChildMailerId == childMailerId);
                        if (removeItem is not null && removeItem.IsDeleted == false)
                        {
                            removeItem.IsDeleted = true;

                            //Recalculate summary information
                            summary.TotalItems--;
                            summary.TotalWeight -= removeItem.Weight;
                            summary.TotalRealWeight -= removeItem.RealWeight;
                            summary.TotalCalWeight -= removeItem.CalWeight;
                            summary.TotalDiffWeight -= (removeItem.RealWeight - removeItem.CalWeight);

                            //Recalculate order information
                            modifiedOrder.TotalItems--;
                            modifiedOrder.Weight -= removeItem.Weight;
                            modifiedOrder.RealWeight -= removeItem.RealWeight;
                            modifiedOrder.CalWeight -= removeItem.CalWeight;

                            // If no non-deleted items remain, decrement TotalOrders (order is effectively removed)
                            var items = modifiedOrder.Items.Where(i => i.IsDeleted != true).ToList();
                            if (!items.Any())
                            {
                                summary.TotalOrders--;
                            }

                            removedCount++;
                        }
                    }
                    else
                    {
                        _logger.LogDebug(
                            "{RouteKey}: Order {MailerId} not found in OrderDetails (already removed or never existed)",
                            routeKey,
                            mailerId);
                    }
                }

                if (removedCount > 0)
                {
                    modifiedSummaries[cacheKey] = summary;

                    _logger.LogInformation(
                        "Modified route {RouteKey}: Removed {RemovedCount} order(s), Total Orders={TotalOrders}, Items={TotalItems}, Weight={TotalWeight}kg",
                        routeKey,
                        removedCount,
                        summary.TotalOrders,
                        summary.TotalItems,
                        summary.TotalWeight);
                }
            }

            // Step 8: Batch save all modified summaries (while still holding locks)
            if (modifiedSummaries.Any())
            {
                await _redis.Batch.Cache.SetManyAsync(modifiedSummaries, TimeSpan.FromDays(CACHE_EXPIRATION_DAYS));

                _logger.LogInformation(
                    "Successfully removed orders from {Count} routes using batch operations (GetMany + SetMany)",
                    modifiedSummaries.Count);
            }
            else
            {
                _logger.LogInformation("No routes were modified (orders already removed or not found)");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Critical error in RemoveOrderOnRouteAsync");
        }
        finally
        {
            // Step 9: Release all locks in parallel (ensures cleanup even on exceptions)
            if (locks.Any())
            {
                _logger.LogDebug("Releasing {Count} distributed locks", locks.Count);

                var releaseTasks = locks.Values.Select(async lockObj =>
                {
                    try
                    {
                        await lockObj.DisposeAsync();
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error releasing distributed lock");
                    }
                });

                await Task.WhenAll(releaseTasks);

                _logger.LogDebug("All locks released successfully");
            }
        }
    }

    public async Task UpdateLastPersistedAtAsync(
        List<string> routeKeys,
        DateTime persistedAt,
        CancellationToken cancellationToken = default)
    {
        if (routeKeys == null || !routeKeys.Any())
        {
            _logger.LogWarning("No route keys to update LastPersistedAt");
            return;
        }

        try
        {
            // Batch fetch all route aggregations first (optimized)
            var cacheKeys = routeKeys.Select(routeKey => $"{RouteAggregationKeyPrefix}{routeKey}").ToArray();
            var summaries = await _redis.Batch.Cache.GetManyAsync<RouteAggregationSummary>(cacheKeys);

            // Update LastPersistedAt for each route in parallel with distributed locks
            // Locks prevent race conditions with concurrent RabbitMQ event handlers
            var updateTasks = routeKeys.Select(async routeKey =>
            {
                var cacheKey = $"{RouteAggregationKeyPrefix}{routeKey}";

                // Skip if not found in batch fetch
                if (!summaries.TryGetValue(cacheKey, out var summary) || summary == null)
                {
                    _logger.LogWarning("Route aggregation not found in Redis for key {RouteKey} - skipping LastPersistedAt update", routeKey);
                    return;
                }

                // Acquire distributed lock to prevent race conditions with UpdateSingleRouteAggregationAsync
                var lockResource = $"{LockPrefix}{routeKey}";
                await using var redisLock = await _redis.Locks.AcquireAsync(
                    lockResource,
                    expiry: TimeSpan.FromSeconds(ROUTE_LOCK_EXPIRY_SECONDS),
                    timeout: TimeSpan.FromSeconds(ROUTE_LOCK_TIMEOUT_SECONDS));

                if (redisLock == null)
                {
                    _logger.LogWarning(
                        "Failed to acquire lock for route {RouteKey} when updating LastPersistedAt - Timeout after {Timeout}s (skipping this route)",
                        routeKey,
                        ROUTE_LOCK_TIMEOUT_SECONDS);
                    return;
                }

                try
                {
                    // Update LastPersistedAt timestamp
                    summary.LastPersistedAt = persistedAt;

                    // Save back to Redis with same expiration
                    await _redis.Cache.SetAsync(cacheKey, summary, TimeSpan.FromDays(CACHE_EXPIRATION_DAYS));

                    // Also update metadata in AllRoutesKey for fast filtering
                    await UpdateAllRouteMetadataAsync(routeKey, summary, cancellationToken);

                    _logger.LogWarning("Updated LastPersistedAt for route {RouteKey} to {PersistedAt}", routeKey, persistedAt);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to update LastPersistedAt for route {RouteKey}", routeKey);
                    // Continue with other routes - don't throw
                }
                // Lock automatically released via DisposeAsync
            });

            await Task.WhenAll(updateTasks);

            _logger.LogInformation(
                "Updated LastPersistedAt for {RouteCount} route aggregations to {PersistedAt}",
                routeKeys.Count,
                persistedAt);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update LastPersistedAt for route aggregations");
            throw;
        }
    }

    /// <summary>
    /// Persists route aggregations from Redis to database using LastPersistedAt for change detection
    /// Called by snapshot job that has access to scoped repositories
    /// Only persists aggregations that have changed since last snapshot
    /// Virtual method allows derived classes to customize AggregationType and DailyPlanningId
    /// </summary>
    /// <param name="routeAggregationRepository">Scoped repository for RouteAggregationEntity</param>
    /// <param name="unitOfWork">Unit of work for saving changes</param>
    /// <param name="snapshotTime">Snapshot timestamp</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Number of routes persisted</returns>
    public virtual async Task<int> PersistRouteAggregationsAsync(
        TMS.SharedKernel.Domain.IBaseRepository<TMS.PlanningService.Domain.Entities.RouteAggregationEntity> routeAggregationRepository,
        TMS.SharedKernel.Domain.IBaseRepository<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity> routeOrderRepository,
        TMS.SharedKernel.Domain.IUnitOfWork unitOfWork,
        DateTime snapshotTime,
        CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("Persisting route aggregations to database");

            // Get lightweight metadata first (fast - one Redis call)
            var allMetadata = await GetRouteMetadataListAsync(cancellationToken);

            if (allMetadata == null || !allMetadata.Any())
            {
                _logger.LogInformation("No route aggregations found in Redis - skipping persistence");
                return 0;
            }

            _logger.LogWarning("Found {Count} route metadata entries in Redis", allMetadata.Count);

            // Filter to only routes that have changed since last persistence
            // Pure in-memory filtering on lightweight metadata (no full aggregation fetches)
            var changedRouteKeys = allMetadata
                .Where(m => !m.LastPersistedAt.HasValue || m.AggregatedAt != m.LastPersistedAt.Value)
                .Select(m => m.RouteKey)
                .ToList();

            if (!changedRouteKeys.Any())
            {
                _logger.LogInformation("No route aggregations have changed - skipping persistence");
                return 0;
            }

            _logger.LogWarning(
                "Found {ChangedCount} changed routes out of {TotalCount} - fetching full aggregations",
                changedRouteKeys.Count,
                allMetadata.Count);

            // Fetch only changed aggregations using batch operations for better performance
            var cacheKeys = changedRouteKeys.Select(routeKey => $"{RouteAggregationKeyPrefix}{routeKey}").ToArray();
            var fetchedAggregations = await _redis.Batch.Cache.GetManyAsync<RouteAggregationSummary>(cacheKeys);
            var changedAggregations = fetchedAggregations.Values.Where(agg => agg != null).Cast<RouteAggregationSummary>().ToList();

            if (!changedAggregations.Any())
            {
                _logger.LogWarning("Failed to fetch any changed aggregations from Redis");
                return 0;
            }

            _logger.LogWarning("Successfully fetched {Count} changed aggregations", changedAggregations.Count);

            // Batch processing configuration
            const int BATCH_SIZE = 100; // Process 100 routes per batch
            var totalRoutesPersisted = 0;
            var totalOrdersPersisted = 0;
            var batchNumber = 0;

            // Process in batches to avoid huge transactions
            for (int i = 0; i < changedAggregations.Count; i += BATCH_SIZE)
            {
                batchNumber++;
                var batchAggregations = changedAggregations.Skip(i).Take(BATCH_SIZE).ToList();

                _logger.LogWarning(
                    "Processing batch {BatchNumber}/{TotalBatches} ({Count} routes)",
                    batchNumber,
                    (int)Math.Ceiling((double)changedAggregations.Count / BATCH_SIZE),
                    batchAggregations.Count);

                // Convert batch aggregations to entities
                var batchEntities = batchAggregations.Select(agg => CreateEntityFromAggregation(agg, snapshotTime)).ToList();

                // Upsert route aggregations for this batch
                // Key columns: Business keys + CreatedAt (required for partitioned tables)
                // PostgreSQL requires partition key (CreatedAt) in unique constraints
                // Each snapshot creates/updates record with specific CreatedAt timestamp
                var affectedRows = await routeAggregationRepository.UpsertRangeAsync(
                    batchEntities,
                    keyColumns: new System.Linq.Expressions.Expression<Func<TMS.PlanningService.Domain.Entities.RouteAggregationEntity, object>>[]
                    {
                        e => e.Id,
                        e => e.CreatedAt  // Required for partitioned table ON CONFLICT
                    },
                    updateColumns: new System.Linq.Expressions.Expression<Func<TMS.PlanningService.Domain.Entities.RouteAggregationEntity, object>>[]
                    {
                        e => e.SnapshotAt,
                        e => e.TotalOrders,
                        e => e.TotalItems,
                        e => e.TotalWeight,
                        e => e.TotalRealWeight,
                        e => e.TotalCalWeight,
                        e => e.TransportProviderBreakdownJson,
                        e => e.VehicleTypeBreakdownJson,
                        e => e.TransportMethodBreakdownJson,
                        e => e.AggregatedAt,
                        e => e.TotalDiffWeight
                    },
                    cancellationToken: cancellationToken
                );

                await unitOfWork.SaveChangesAsync(cancellationToken);
                totalRoutesPersisted += batchEntities.Count;

                _logger.LogWarning(
                    "Batch {BatchNumber}: Upserted {Count} route aggregations ({AffectedRows} rows affected)",
                    batchNumber,
                    batchEntities.Count,
                    affectedRows);

                // Persist order details for this batch of routes
                var batchOrderEntities = new List<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity>();

                foreach (var aggregation in batchAggregations)
                {
                    if (!aggregation.OrderDetails.Any())
                    {
                        continue;
                    }

                    // Find the persisted route aggregation entity ID
                    var routeEntity = batchEntities.FirstOrDefault(e => e.RouteKey == aggregation.RouteKey);
                    if (routeEntity == null)
                    {
                        _logger.LogWarning("Could not find route entity for {RouteKey}, skipping order persistence", aggregation.RouteKey);
                        continue;
                    }

                    // Create order entities for current snapshot
                    // CRITICAL: CreatedAt MUST match parent RouteAggregationEntity.CreatedAt for partition consistency
                    // Since OrderOnRoute now contains a list of Items (child mailers), we need to create one entity per item
                    var orderEntities = aggregation.OrderDetails.Values
                        .SelectMany(order => order.Items?.Select(item => new TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity
                        {
                            // Composite Primary Key (required for partitioned table)
                            // Id is auto-generated by EntityBase using SequentialGuidGenerator.NewPostgreSqlGuid()
                            CreatedAt = routeEntity.CreatedAt, // Take from parent to ensure same partition

                            // Composite Foreign Key to RouteAggregation (both parts required)
                            RouteAggregationId = routeEntity.Id,
                            RouteAggregationCreatedAt = routeEntity.CreatedAt,

                            MailerId = item.MailerId,
                            ChildMailerId = item.ChildMailerId, // From individual item
                            Status = item.Status,
                            ServiceTypeId = order.ServiceTypeId,
                            ExtraService = order.ExtraService,
                            Weight = item.Weight, // Individual item weight
                            RealWeight = item.RealWeight, // Individual item real weight
                            CalWeight = item.CalWeight, // Individual item calculated weight
                            IsDeleted = item.IsDeleted
                        }) ?? Enumerable.Empty<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity>())
                        .ToList();

                    batchOrderEntities.AddRange(orderEntities);
                }

                // Bulk upsert all orders for this batch
                // Key columns must match unique index: composite FK + order identifiers + partition key
                // Each snapshot (RouteAggregationId, RouteAggregationCreatedAt) has its own set of orders
                if (batchOrderEntities.Any())
                {
                    var affectedOrderRows = await routeOrderRepository.UpsertRangeAsync(
                        batchOrderEntities,
                        keyColumns: new System.Linq.Expressions.Expression<Func<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity, object>>[]
                        {
                            e => e.RouteAggregationId,
                            e => e.RouteAggregationCreatedAt,  // Part of composite FK
                            e => e.MailerId,
                            e => e.ChildMailerId,
                            e => e.CreatedAt  // Partition key - required for partitioned table
                        },
                        updateColumns: new System.Linq.Expressions.Expression<Func<TMS.PlanningService.Domain.Entities.RouteAggregationOrderEntity, object>>[]
                        {
                            e => e.Status,
                            e => e.Weight,
                            e => e.RealWeight,
                            e => e.CalWeight,
                            e => e.IsDeleted,
                        },
                        cancellationToken: cancellationToken
                    );

                    await unitOfWork.SaveChangesAsync(cancellationToken);
                    totalOrdersPersisted += batchOrderEntities.Count;

                    _logger.LogWarning(
                        "Batch {BatchNumber}: Upserted {OrderCount} order records ({AffectedRows} rows affected)",
                        batchNumber,
                        batchOrderEntities.Count,
                        affectedOrderRows);
                }

                // Update LastPersistedAt in Redis for this batch
                var batchRouteKeys = batchAggregations.Select(a => a.RouteKey).ToList();
                await UpdateLastPersistedAtAsync(batchRouteKeys, snapshotTime, cancellationToken);
            }

            _logger.LogInformation(
                "Completed batched persistence - Routes: {RouteCount}, Orders: {OrderCount}, Batches: {BatchCount}",
                totalRoutesPersisted,
                totalOrdersPersisted,
                batchNumber);

            return totalRoutesPersisted;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to persist route aggregations");
            throw;
        }
    }

    /// <summary>
    /// Creates a RouteAggregationEntity from a RouteAggregationSummary
    /// Virtual method allows derived classes to customize AggregationType and DailyPlanningId
    /// </summary>
    protected virtual TMS.PlanningService.Domain.Entities.RouteAggregationEntity CreateEntityFromAggregation(
        RouteAggregationSummary agg,
        DateTime snapshotTime)
    {
        return new TMS.PlanningService.Domain.Entities.RouteAggregationEntity
        {
            // Composite Primary Key (required for partitioned table)
            // Id is auto-generated by EntityBase using SequentialGuidGenerator.NewPostgreSqlGuid()
            // CreatedAt: Use original creation time from Redis (immutable)
            Id = agg.Id,
            CreatedAt = agg.CreatedAt,

            RouteKey = agg.RouteKey,
            AggregationType = "normal", // Base implementation: normal route aggregations
            SnapshotAt = snapshotTime,
            DailyPlanningId = null, // NULL for normal/priority (no daily plan link)
            FromOfficeId = agg.FromOfficeId,
            ToOfficeId = agg.ToOfficeId,
            FromTime = agg.FromTime,
            ToTime = agg.ToTime,
            ActualFromTime = agg.ActualFromTime,
            ActualToTime = agg.ActualToTime,
            TotalDurationMinutes = agg.TotalDurationMinutes,
            AverageDurationMinutes = agg.AverageDurationMinutes,
            EarliestStartTime = agg.EarliestStartTime,
            LatestEndTime = agg.LatestEndTime,
            TotalOrders = agg.TotalOrders,
            TotalItems = agg.TotalItems,
            TotalWeight = agg.TotalWeight,
            TotalRealWeight = agg.TotalRealWeight,
            TotalCalWeight = agg.TotalCalWeight,
            TotalDiffWeight = agg.TotalRealWeight - agg.TotalCalWeight,
            PriorityScore = agg.PriorityScore,
            NeedsOptimization = agg.NeedsOptimization,
            TransportProviderBreakdownJson = Newtonsoft.Json.JsonConvert.SerializeObject(agg.TransportProviderBreakdown),
            VehicleTypeBreakdownJson = Newtonsoft.Json.JsonConvert.SerializeObject(agg.VehicleTypeBreakdown),
            TransportMethodBreakdownJson = Newtonsoft.Json.JsonConvert.SerializeObject(agg.TransportMethodBreakdown),
            AggregatedAt = agg.AggregatedAt,
            PriorityPlanId = agg.PriorityPlanId
        };
    }
}
