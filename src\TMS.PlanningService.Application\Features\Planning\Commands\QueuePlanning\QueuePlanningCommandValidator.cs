﻿using FluentValidation;

namespace TMS.PlanningService.Application.Features.Planning.Commands.QueuePlanning;

public class QueuePlanningCommandValidator : AbstractValidator<QueuePlanningCommand>
{
    public QueuePlanningCommandValidator()
    {
        RuleFor(x => x.PlanningRequest)
            .NotNull()
            .WithMessage("Planning request is required");

        RuleFor(x => x.PlanningRequest.MailerRouteMaster)
            .NotNull()
            .WithMessage("MailerRouteMaster is required");

        RuleFor(x => x.PlanningRequest.MailerRouteMaster.MailerId)
            .NotEmpty()
            .WithMessage("MailerId is required");
         
        RuleFor(x => x.PlanningRequest.MailerPlanRoutes)
            .NotNull()
            .WithMessage("MailerPlanRoutes cannot be null");
         
        RuleFor(x => x.PlanningRequest.MailerActualRoutes)
            .NotNull()
            .WithMessage("MailerActualRoutes cannot be null");

        RuleFor(x => x.Priority)
            .GreaterThanOrEqualTo(0)
            .WithMessage("Priority must be non-negative");
    }
}
