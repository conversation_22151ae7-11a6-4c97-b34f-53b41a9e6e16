﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.PlanningTemplate.Commands.CreatePlanningTemplate;
using TMS.PlanningService.Application.Features.PlanningTemplate.Commands.DeletePlanningTemplate;
using TMS.PlanningService.Application.Features.PlanningTemplate.Commands.ImportPlanningTemplates;
using TMS.PlanningService.Application.Features.PlanningTemplate.Commands.UpdatePlanningTemplate;
using TMS.PlanningService.Application.Features.PlanningTemplate.Commands.UpdatePriorities;
using TMS.PlanningService.Application.Features.PlanningTemplate.Queries.DownloadPlanningTemplate;
using TMS.PlanningService.Application.Features.PlanningTemplate.Queries.GetPlanningTemplateById;
using TMS.PlanningService.Application.Features.PlanningTemplate.Queries.GetPlanningTemplates;
using TMS.PlanningService.Application.Features.PlanningTemplate.Queries.GetPlanningTemplatesByRouteId;
using TMS.PlanningService.Contracts.PlanningTemplate;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers.Metadata;

[ApiController]
[Route("api/v{version:apiVersion}/planning-templates")]
[Produces("application/json")]
public class PlanningTemplateController : ControllerBase
{
    private readonly IMediator _mediator;

    public PlanningTemplateController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Get all planning templates with optional filters
    /// </summary> 
    /// <returns>List of planning templates</returns>
    [HttpPost("search")]
    [ProducesResponseType(typeof(PagedResult<PlanningTemplateDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<PagedResult<PlanningTemplateDto>>> GetPlanningTemplates([FromBody] GetPlanningTemplatesRequest request)
    {
        var query = new GetPlanningTemplatesQuery(request);
        var result = await _mediator.Send(query);
        return Ok(result);
    }

    /// <summary>
    /// Get planning template by Id
    /// </summary>
    /// <param name="id">Planning template Id</param>
    /// <returns>Planning template details</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(PlanningTemplateDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<PlanningTemplateDto>> GetPlanningTemplateById(Guid id)
    {
        var query = new GetPlanningTemplateByIdQuery(id);
        var result = await _mediator.Send(query);

        if (result == null)
            return NotFound();

        return Ok(result);
    }

    /// <summary>
    /// Create a new planning template
    /// </summary>
    /// <param name="request">Planning template data</param>
    /// <returns>Created planning template Id</returns>
    [HttpPost]
    [ProducesResponseType(typeof(Guid), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Guid>> CreatePlanningTemplate([FromBody] CreatePlanningTemplateRequest request)
    {
        var command = new CreatePlanningTemplateCommand(request);
        var result = await _mediator.Send(command);

        return CreatedAtAction(
            nameof(CreatePlanningTemplate),
            new { id = result },
            result);
    }

    /// <summary>
    /// Update an existing planning template
    /// </summary>
    /// <param name="id">Planning template Id</param>
    /// <param name="request">Updated planning template data</param>
    /// <returns>No content</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdatePlanningTemplate(Guid id, [FromBody] UpdatePlanningTemplateRequest request)
    {
        if (id != request.Id)
            return BadRequest("ID mismatch between route and body");

        var command = new UpdatePlanningTemplateCommand(request);
        await _mediator.Send(command);

        return NoContent();
    }

    /// <summary>
    /// Delete a planning template (soft delete)
    /// </summary>
    /// <param name="id">Planning template ID</param>
    /// <returns>No content</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DeletePlanningTemplate(Guid id)
    {
        var command = new DeletePlanningTemplateCommand(id);
        await _mediator.Send(command);

        return NoContent();
    }

    /// <summary>
    /// Import planning templates from Excel/CSV file
    /// </summary>
    /// <param name="request">File upload request</param>
    /// <returns>Import result with success/error summary</returns>
    [HttpPost("import")]
    [Consumes("multipart/form-data")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<IActionResult> Import([FromForm] ImportRequest request)
    {
        if (request.File == null || request.File.Length == 0)
            return BadRequest("File is required.");

        var command = new ImportPlanningTemplatesCommand(request.File);
        var result = await _mediator.Send(command);

        return Ok(result);
    }

    /// <summary>
    /// Download planning template Excel file template
    /// </summary>
    /// <returns>Excel template file for import</returns>
    [HttpGet("template")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> DownloadTemplate()
    {
        var query = new DownloadPlanningTemplateQuery();
        var file = await _mediator.Send(query);

        return File(file.Content, file.ContentType, file.FileName);
    }

    /// <summary>
    /// Update priority numbers for multiple planning templates
    /// </summary>
    [HttpPut("priorities")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> UpdatePriorities([FromBody] UpdatePlanningTemplatePrioritiesRequest request)
    {
        var result = await _mediator.Send(new UpdatePlanningTemplatePrioritiesCommand(request));
        return Ok(result);
    }

    /// <summary>
    /// Get planning templates by RouteId (code, name, isActive), ordered by PriorityNumber
    /// </summary>
    [HttpGet("route/{routeId:guid}")]
    [ProducesResponseType(typeof(List<PlanningTemplateByRouteIdDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(typeof(PlanningTemplateDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<List<PlanningTemplateByRouteIdDto>>> GetByRouteId(Guid routeId)
    {
        var result = await _mediator.Send(new GetPlanningTemplatesByRouteIdQuery(routeId));
        return Ok(result);
    }
}
