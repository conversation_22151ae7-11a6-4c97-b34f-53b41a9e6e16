﻿using FluentValidation;
using TMS.SharedKernel.Constants;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.CreatePlanningTemplate;

public class CreatePlanningTemplateValidator : AbstractValidator<CreatePlanningTemplateCommand>
{
    public CreatePlanningTemplateValidator()
    {
        RuleFor(x => x.ParamRequest.Name)
        .NotEmpty()
        .WithMessage(string.Format(ValidationMessages.Required, "Name"))
        .MaximumLength(255)
        .WithMessage(string.Format(ValidationMessages.MaxLength, "Name", 255));

        RuleFor(x => x.ParamRequest.RouteId)
        .NotEmpty()
        .WithMessage(string.Format(ValidationMessages.Required, "RouteId"));

        RuleFor(x => x.ParamRequest.VehicleTypeId)
        .NotEmpty()
        .WithMessage(string.Format(ValidationMessages.Required, "VehicleTypeId"));

        RuleFor(x => x.ParamRequest.Details)
        .NotNull()
        .NotEmpty()
        .WithMessage("Individual detail cannot be null or empty")
        .Must(items => (items?.Count ?? 0) >= 2)
        .WithMessage("A route must have at least 2 stops");

        // Todo: Add more validation rules for Details if needed like checking for valid PostOfficeId, Time ranges, etc.
    }
}
