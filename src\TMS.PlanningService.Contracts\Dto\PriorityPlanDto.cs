﻿namespace TMS.PlanningService.Contracts.Dto;
public class PriorityPlanDto: AuditDto
{
    public Guid? Id { get; set; }
    public string? PriorityPlanName { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }

    public bool? IsDeleted { get; set; }
    public DateTime? CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public Guid? UpdatedBy { get; set; }
    public List<PriorityPlanGroupDto> PriorityPlanGroups { get; set; } = new List<PriorityPlanGroupDto>();
};
