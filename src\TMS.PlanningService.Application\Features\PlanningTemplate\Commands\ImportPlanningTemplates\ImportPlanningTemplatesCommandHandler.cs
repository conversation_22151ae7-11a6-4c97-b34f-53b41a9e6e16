﻿using System.Text.RegularExpressions;
using MediatR;
using NPOI.SS.UserModel;
using Refit;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.PlanningTemplate;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.ImportPlanningTemplates;

public class ImportPlanningTemplatesCommandHandler : IRequestHandler<ImportPlanningTemplatesCommand, ImportResponse>
{
    private readonly IBaseRepository<PlanningTemplateEntity> _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IPlanningTemplateCalculateService _planningTemplateCalculateService;
    private readonly IFileServiceApi _fileServiceApi;
    private readonly IRouteServiceApi _routeServiceApi;
    private const char ItemSeparator = ',';
    private const char KeyValueSeparator = '*';

    public ImportPlanningTemplatesCommandHandler(
        IBaseRepository<PlanningTemplateEntity> repository,
        IUnitOfWork unitOfWork,
        ICurrentFactorProvider currentFactorProvider,
        IPlanningTemplateCalculateService planningTemplateCalculateService,
        IFileServiceApi fileServiceApi,
        IRouteServiceApi routeServiceApi)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _currentFactorProvider = currentFactorProvider;
        _planningTemplateCalculateService = planningTemplateCalculateService;
        _fileServiceApi = fileServiceApi;
        _routeServiceApi = routeServiceApi;
    }

    public async Task<ImportResponse> Handle(ImportPlanningTemplatesCommand request, CancellationToken cancellationToken)
    {
        await using var stream = request.File.OpenReadStream();
        var workbook = WorkbookFactory.Create(stream);
        var sheet = workbook.GetSheetAt(0) ?? throw new Exception("Sheet not found.");

        var importResult = new ImportResponse();
        var routes = new List<PlanningTemplateEntity>();
        var (routeCodes, templateNamesMap, dulicateInfo) = ExtractDataFromSheet(sheet);
        var routeDtos = routeCodes.Count > 0 ? await _routeServiceApi.GetRoutesByCodesAsync(routeCodes) : new List<RouteDto>();
        var columnConfigs = GetColumnConfigs();
        var hasErrors = false;
        for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
        {
            var row = sheet.GetRow(i);
            if (row == null || row.Cells.All(c => string.IsNullOrWhiteSpace(c.ToString())))
                continue;

            var validateResult = new RowValidationResult
            {
                Entity = new PlanningTemplateEntity
                {
                    CompanyId = _currentFactorProvider.CompanyId,
                }
            };

            var currentRouteDto = new RouteDto();
            foreach (var config in columnConfigs)
            {
                var cell = row.GetCell(config.Index) ?? row.CreateCell(config.Index);
                if (config.Index == 0)
                {
                    var code = cell.ToString()?.Trim();
                    currentRouteDto = routeDtos?.FirstOrDefault(x => !string.IsNullOrEmpty(x.Code) && x.Code.Equals(code, StringComparison.OrdinalIgnoreCase));
                }

                config.Validator(cell, validateResult, currentRouteDto, config.Name);
            }

            if (!validateResult.HasError)
                ValidateDuplicateFromExcel(row, validateResult, dulicateInfo, templateNamesMap);

            if (!validateResult.HasError)
                await ValidateDuplicateFromDb(row, validateResult, cancellationToken);

            if (validateResult.HasError)
            {
                hasErrors = true;
                importResult.RowErrorCount++;
                row.CreateCell(row.LastCellNum).SetCellValue(string.Join("; ", validateResult.ErrorMessages));
            }
            else
            {
                await _planningTemplateCalculateService.CalculateAsync(validateResult.Entity, currentRouteDto);
                routes.Add(validateResult.Entity);
            }
        }

        if (hasErrors)
        {
            importResult.FileId = await UploadErrorWorkbookAsync(workbook, request.File.FileName);
            return importResult;
        }

        await _repository.AddRangeAsync(routes);
        await _unitOfWork.SaveChangesAsync();

        return importResult;
    }

    private void ValidateDetails(ICell cell, RowValidationResult result, RouteDto? routeDto, string columnName)
    {
        //Một kế hoạch phải có ít nhất 2 điểm dừng(điểm đầu và điểm cuối). Hai điểm này có thể giống nhau.
        //Các nghiệp vụ sẽ được mapping theo địa điểm như sau, Điểm số 1 sẽ không có Nghiệp vụ
        //Các điểm nhập phải thuộc cùng 1 tuyến. Kiểm tra mapping giữa điểm dừng và Tuyến. 
        var raw = cell.ToString()?.Trim();
        if (string.IsNullOrWhiteSpace(raw))
        {
            result.ErrorMessages.Add($"'{columnName}' không được để trống");
            result.HasError = true;
            MarkCellAsError(cell);
            return;
        }

        var ranges = raw.Split(ItemSeparator, StringSplitOptions.RemoveEmptyEntries);
        if (!ranges.Any())
        {
            result.ErrorMessages.Add($"'{columnName}' không hợp lệ");
            result.HasError = true;
            MarkCellAsError(cell);
            return;
        }

        if (ranges.Count() < 2)
        {
            result.ErrorMessages.Add("Một kế hoạch phải có ít nhất 2 điểm dừng(điểm đầu và điểm cuối). Hai điểm này có thể giống nhau.");
            result.HasError = true;
            MarkCellAsError(cell);
            return;
        }

        var hasCellError = false;
        for (int i = 0; i < ranges.Count(); i++)
        {
            var range = ranges[i];
            if (!TryParseDetailParts(range, result, columnName, out var postOfficeCode, out var opRaw, out var timeRange))
            {
                hasCellError = true;
                continue;
            }

            if (TryValidateDetailParts(postOfficeCode, opRaw, i, timeRange, result, routeDto, out var detail))
            {
                result.Entity!.Details.Add(detail!);
            }
            else
            {
                hasCellError = true;
            }
        }

        if (!hasCellError)
            hasCellError = ValidateTimeOrderForRow(result);

        if (hasCellError)
        {
            MarkCellAsError(cell);
        }
        else
        {
            CalculateStepNumber(result);
        }
    }

    private bool ValidateTimeOrderForRow(RowValidationResult result)
    {
        var details = result.Entity?.Details?.OrderBy(d => d.StepNumber).ToList();
        if (details == null || details.Count <= 1)
            return false;

        for (int i = 0; i < details.Count - 1; i++)
        {
            var current = details[i];
            var next = details[i + 1];

            var currentStart = DateTime.MinValue
                .AddDays(current.FromAddDays)
                .Add(current.FromTime);

            var currentEnd = DateTime.MinValue
                .AddDays(current.ToTimeAddDays)
                .Add(current.ToTime);

            var nextStart = DateTime.MinValue
                .AddDays(next.FromAddDays)
                .Add(next.FromTime);

            if (currentEnd <= currentStart || nextStart < currentEnd)
            {
                result.ErrorMessages.Add("Thời gian hoạt động phải sắp xếp theo đúng thứ tự từ trước tới sau.");
                result.HasError = true;
                return true;
            }
        }

        return false;
    }

    private bool TryParseDetailParts(string range, RowValidationResult result, string columnName, out string postOfficeCode, out string opRaw, out string timeRange)
    {
        postOfficeCode = opRaw = timeRange = string.Empty;

        var parts = range.Split(KeyValueSeparator, StringSplitOptions.RemoveEmptyEntries);
        if (parts.Length != 3)
        {
            result.ErrorMessages.Add($"'{columnName}' không đúng định dạng: '{range}'");
            result.HasError = true;
            return false;
        }

        postOfficeCode = parts[0].Trim();
        opRaw = parts[1].Trim();
        timeRange = parts[2].Trim();
        return true;
    }

    private bool TryValidateDetailParts(
        string postOfficeCode,
        string opRaw,
        int opRawIndex,
        string timeRange,
        RowValidationResult result,
        RouteDto? routeDto,
        out PlanningTemplateDetailEntity? detail)
    {
        detail = null;

        if (!TryParsePostOffice(postOfficeCode, result, routeDto, out var postOfficeId, out var code))
            return false;

        if (!TryParseBusinessOperation(opRaw, opRawIndex, result, routeDto, out var op))
            return false;

        if (!TryParseTimeRange(timeRange, result, out var fromTime, out var fromAddDays, out var toTime, out var toAddDays))
            return false;

        detail = new PlanningTemplateDetailEntity
        {
            PostOfficeId = postOfficeId!.Value,
            PostOfficeCode = code ?? string.Empty,
            BusinessOperation = op!.Value,
            FromTime = fromTime,
            FromAddDays = fromAddDays,
            ToTime = toTime,
            ToTimeAddDays = toAddDays
        };

        result.Entity!.PostOfficeCodes += $" {code}";
        result.Entity!.OfficeCount++;

        return true;
    }

    private bool TryParsePostOffice(
        string postOfficeCode,
        RowValidationResult result,
        RouteDto? routeDto,
        out Guid? postOfficeId,
        out string? postOfficeCodeNormalized)
    {
        postOfficeId = null;
        postOfficeCodeNormalized = null;

        if (string.IsNullOrWhiteSpace(postOfficeCode))
        {
            result.ErrorMessages.Add($"Mã điểm dừng không được để trống");
            result.HasError = true;
            return false;
        }

        var postOffice = routeDto?.PostOffices?.FirstOrDefault(p =>
            !string.IsNullOrEmpty(p.PostOfficeCode) &&
            p.PostOfficeCode.Equals(postOfficeCode, StringComparison.OrdinalIgnoreCase));

        if (postOffice == null)
        {
            result.ErrorMessages.Add($"Mã điểm dừng '{postOfficeCode}' không hợp lệ theo tuyến xe tải");
            result.HasError = true;
            return false;
        }

        postOfficeId = postOffice.PostOfficeId;
        postOfficeCodeNormalized = postOffice.PostOfficeCode;
        return true;
    }

    private bool TryParseBusinessOperation(
        string opRaw,
        int opRawIndex,
        RowValidationResult result,
        RouteDto? routeDto,
        out BusinessOperation? operation)
    {
        operation = null;

        if (!Enum.TryParse<BusinessOperation>(opRaw, out var op) || !Enum.IsDefined(typeof(BusinessOperation), op))
        {
            result.ErrorMessages.Add($"Nghiệp vụ '{opRaw}' không hợp lệ");
            result.HasError = true;
            return false;
        }

        if (op == BusinessOperation.Departure && opRawIndex > 0)
        {
            result.ErrorMessages.Add($"Nghiệp vụ '{opRaw}' chỉ áp dụng cho điểm dừng đầu tiên");
            result.HasError = true;
            return false;
        }

        if (!CheckRouteOperations(routeDto, op))
        {
            result.ErrorMessages.Add($"Nghiệp vụ '{opRaw}' không hợp lệ theo tuyến xe tải");
            result.HasError = true;
            return false;
        }

        operation = op;
        return true;
    }

    private bool CheckRouteOperations(RouteDto? routeDto, BusinessOperation op)
    {
        if (routeDto?.RouteOperations == null || !routeDto.RouteOperations.Any())
            return false;

        if (op == BusinessOperation.Departure)
            return true;

        var operations = routeDto.RouteOperations.Select(x => x.OperationId).ToHashSet();
        if (operations.Contains(op))
            return true;

        if (op == BusinessOperation.ReceiveAndDelivery &&
            operations.Contains(BusinessOperation.Receive) &&
            operations.Contains(BusinessOperation.Delivery))
        {
            return true;
        }

        if (op == BusinessOperation.PickupAndSend &&
            operations.Contains(BusinessOperation.Pickup) &&
            operations.Contains(BusinessOperation.Send))
        {
            return true;
        }

        return false;
    }

    private bool TryParseTimeRange(
        string timeRange,
        RowValidationResult result,
        out TimeSpan fromTime,
        out int fromAddDays,
        out TimeSpan toTime,
        out int toAddDays)
    {
        fromTime = toTime = default;
        fromAddDays = toAddDays = 0;

        var times = timeRange.Split('-');
        if (times.Length != 2 ||
            !TryParseTimeWithDay(times[0].Trim(), out fromTime, out fromAddDays) ||
            !TryParseTimeWithDay(times[1].Trim(), out toTime, out toAddDays))
        {
            result.ErrorMessages.Add($"Thời gian hoạt động không đúng định dạng: '{timeRange}'");
            result.HasError = true;
            return false;
        }

        return true;
    }

    private bool TryParseTimeWithDay(string input, out TimeSpan time, out int addDays)
    {
        time = default;
        addDays = 0;

        // Regex: HH:mmN, HH:mmN1, HH:mmN2,...
        var match = Regex.Match(input, @"^(?<time>\d{2}:\d{2})N(?<day>\d*)$");
        if (!match.Success) return false;

        if (!TimeSpan.TryParse(match.Groups["time"].Value, out time)) return false;

        var dayGroup = match.Groups["day"].Value;
        addDays = string.IsNullOrEmpty(dayGroup) ? 0 : int.Parse(dayGroup);
        return true;
    }

    private (List<string> RouteCodes, Dictionary<string, int> TemplateNamesMap, Dictionary<string, int> DuplicateMap)
        ExtractDataFromSheet(ISheet sheet)
    {
        var routeCodes = new HashSet<string>(StringComparer.OrdinalIgnoreCase);
        var templateNamesMap = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);
        var duplicateMap = new Dictionary<string, int>(StringComparer.OrdinalIgnoreCase);

        for (int i = sheet.FirstRowNum + 1; i <= sheet.LastRowNum; i++)
        {
            var row = sheet.GetRow(i);
            if (row == null || row.Cells.All(c => string.IsNullOrWhiteSpace(c.ToString())))
                continue;

            var routeCode = row.GetCell((int)ExcelColumnIndexEnum.RouteCode)?.ToString()?.Trim();
            var templateName = row.GetCell((int)ExcelColumnIndexEnum.PlanningTemplateName)?.ToString()?.Trim();
            var detailRaw = row.GetCell((int)ExcelColumnIndexEnum.Details)?.ToString()?.Trim();

            if (!string.IsNullOrWhiteSpace(routeCode))
            {
                routeCodes.Add(routeCode);
                var dupKey = (routeCode + detailRaw).Trim();
                if (duplicateMap.ContainsKey(dupKey))
                    duplicateMap[dupKey]++;
                else
                    duplicateMap[dupKey] = 1;
            }

            if (!string.IsNullOrWhiteSpace(templateName))
            {
                if (templateNamesMap.ContainsKey(templateName))
                    templateNamesMap[templateName]++;
                else
                    templateNamesMap[templateName] = 1;
            }
        }

        return (routeCodes.ToList(), templateNamesMap, duplicateMap);
    }



    private async Task<Guid> UploadErrorWorkbookAsync(IWorkbook workbook, string originalFileName)
    {
        await using var stream = new MemoryStream();
        workbook.Write(stream, leaveOpen: true);
        stream.Position = 0;

        var filePart = new StreamPart(
            stream,
            $"ImportError_{originalFileName}",
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        );

        var uploadResult = await _fileServiceApi.UploadFileAsync(filePart);
        return uploadResult.FileId;
    }

    private void MarkCellAsError(ICell cell)
    {
        if (cell == null) return;

        var workbook = cell.Sheet.Workbook;
        var style = workbook.CreateCellStyle();

        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;

        style.TopBorderColor = IndexedColors.Red.Index;
        style.BottomBorderColor = IndexedColors.Red.Index;
        style.LeftBorderColor = IndexedColors.Red.Index;
        style.RightBorderColor = IndexedColors.Red.Index;

        cell.CellStyle = style;
    }

    private void ValidateDuplicateFromExcel(
        IRow row,
        RowValidationResult result,
        Dictionary<string, int> duplicateMap,
        Dictionary<string, int> templateNamesMap)
    {
        // ==== Check duplicate Route + Details ====
        var cellRoute = row.GetCell((int)ExcelColumnIndexEnum.RouteCode) ?? row.CreateCell((int)ExcelColumnIndexEnum.RouteCode);
        var routeCode = cellRoute.ToString()?.Trim();

        var cellDetails = row.GetCell((int)ExcelColumnIndexEnum.Details) ?? row.CreateCell((int)ExcelColumnIndexEnum.Details);
        var details = cellDetails.ToString()?.Trim();

        var routeDetailKey = (routeCode + details)?.Trim();

        if (!string.IsNullOrWhiteSpace(routeDetailKey)
            && duplicateMap.TryGetValue(routeDetailKey, out var routeDetailCount)
            && routeDetailCount > 1)
        {
            result.ErrorMessages.Add("Một KHVC chỉ có thể có một tuyến giống nhau duy nhất (các điểm và thứ tự giống nhau hoàn toàn giữa hai chuyến)");
            MarkCellAsError(cellRoute);
            result.HasError = true;
            return;
        }

        // ==== Check duplicate TemplateName ====
        var cellName = row.GetCell((int)ExcelColumnIndexEnum.PlanningTemplateName) ?? row.CreateCell((int)ExcelColumnIndexEnum.PlanningTemplateName);
        var templateName = cellName.ToString()?.Trim();

        if (!string.IsNullOrWhiteSpace(templateName)
            && templateNamesMap.TryGetValue(templateName, out var nameCount)
            && nameCount > 1)
        {
            result.ErrorMessages.Add("Tên KHVC Mẫu bị trùng trong file Excel");
            MarkCellAsError(cellName);
            result.HasError = true;
            return;
        }
    }

    private async Task ValidateDuplicateFromDb(IRow row, RowValidationResult result, CancellationToken cancellationToken)
    {
        var entity = result.Entity;
        if (entity == null) return;

        // ==== Check duplicate TemplateName ====
        var exists = await _repository.ExistsAsync(
            x => !x.IsDeleted && x.Name == entity.Name,
            cancellationToken);

        if (exists)
        {
            var cellName = row.GetCell((int)ExcelColumnIndexEnum.PlanningTemplateName)
                          ?? row.CreateCell((int)ExcelColumnIndexEnum.PlanningTemplateName);

            result.ErrorMessages.Add("Tên KHVC Mẫu đã tồn tại trong hệ thống");
            MarkCellAsError(cellName);
            result.HasError = true;
            return;
        }

        // ==== Check duplicate by Route + FULL Details Match ====
        var existingTemplates = await _repository.FindWithIncludeAsync(
            predicate: x => !x.IsDeleted && x.RouteCode == entity.RouteCode,
            includes: x => x.Details);

        foreach (var dbTemplate in existingTemplates)
        {
            var detailsDb = dbTemplate.Details.OrderBy(d => d.StepNumber).ToList();
            var detailsNew = entity.Details.OrderBy(d => d.StepNumber).ToList();

            if (detailsDb.Count != detailsNew.Count)
                continue;

            bool allMatch = true;
            for (int i = 0; i < detailsDb.Count; i++)
            {
                var d1 = detailsDb[i];
                var d2 = detailsNew[i];

                if (d1.PostOfficeId != d2.PostOfficeId
                    || d1.BusinessOperation != d2.BusinessOperation
                    || d1.FromTime != d2.FromTime
                    || d1.FromAddDays != d2.FromAddDays
                    || d1.ToTime != d2.ToTime
                    || d1.ToTimeAddDays != d2.ToTimeAddDays)
                {
                    allMatch = false;
                    break;
                }
            }

            if (allMatch)
            {
                var cellRoute = row.GetCell((int)ExcelColumnIndexEnum.RouteCode)
                               ?? row.CreateCell((int)ExcelColumnIndexEnum.RouteCode);

                result.ErrorMessages.Add("Một KHVC chỉ có thể có một tuyến giống nhau duy nhất (các điểm và thứ tự giống nhau hoàn toàn giữa hai chuyến)");
                MarkCellAsError(cellRoute);
                result.HasError = true;
                return;
            }
        }
    }

    private void ValidateRoute(ICell cell, RowValidationResult result, RouteDto? routeDto, string columnName)
    {
        var code = cell.ToString()?.Trim();
        var hasCellError = false;

        if (string.IsNullOrWhiteSpace(code))
        {
            result.ErrorMessages.Add($"{columnName} không được để trống");
            result.HasError = true;
            hasCellError = true;
        }

        if (routeDto != null)
        {
            result.Entity!.RouteId = routeDto.Id;
            result.Entity!.RouteCode = routeDto.Code;
        }
        else
        {
            result.ErrorMessages.Add($"{columnName} '{code}' không tồn tại");
            result.HasError = true;
            hasCellError = true;
        }

        if (hasCellError)
            MarkCellAsError(cell);
    }

    private void ValidateName(ICell cell, RowValidationResult result, string columnName)
    {
        var name = cell.ToString()?.Trim();
        if (string.IsNullOrWhiteSpace(name))
        {
            result.ErrorMessages.Add($"{columnName} không được để trống");
            MarkCellAsError(cell);
            result.HasError = true;
            return;
        }

        result.Entity!.Name = name;
    }

    private void ValidatePriorityNumber(ICell cell, RowValidationResult result, string columnName)
    {
        var raw = cell.ToString()?.Trim();
        var hasCellError = false;

        if (string.IsNullOrWhiteSpace(raw))
        {
            result.ErrorMessages.Add($"{columnName} không được để trống");
            result.HasError = true;
            hasCellError = true;
        }

        if (int.TryParse(raw, out var i) && i >= 1 && i <= 10)
        {
            result.Entity!.PriorityNumber = i;
        }
        else
        {
            result.ErrorMessages.Add($"{columnName} '{raw}' không hợp lệ");
            result.HasError = true;
            hasCellError = true;
        }

        if (hasCellError)
            MarkCellAsError(cell);
    }

    private void ValidateVehicleTypes(ICell cell, RowValidationResult result, RouteDto? routeDto, string columnName)
    {
        var raw = cell.ToString()?.Trim();
        var hasCellError = false;

        if (string.IsNullOrWhiteSpace(raw))
        {
            result.ErrorMessages.Add($"{columnName} không được để trống");
            result.HasError = true;
            hasCellError = true;
        }
        
        var vehicleTypes = routeDto?.VehicleTypes;
        var vehicleType = vehicleTypes?.FirstOrDefault(x => !string.IsNullOrEmpty(x.Code) && x.Code.Equals(raw, StringComparison.OrdinalIgnoreCase));
        if (vehicleType != null)
        {
            result.Entity!.VehicleTypeId = vehicleType.VehicleTypeId;
        }
        else
        {
            result.ErrorMessages.Add($"{columnName} '{raw}' không hợp lệ theo tuyến xe tải");
            result.HasError = true;
            hasCellError = true;
        }

        if (hasCellError)
            MarkCellAsError(cell);
    }

    private void CalculateStepNumber(RowValidationResult result)
    {
        int step = 1;
        foreach (var item in result.Entity!.Details)
        {
            item.StepNumber = step++;
        }
    }

    private List<ExcelColumnConfig> GetColumnConfigs()
    {
        return new List<ExcelColumnConfig>
        {
            new()
            {
                Index = (int)ExcelColumnIndexEnum.RouteCode,
                Name = "Mã Tuyến xe tải",
                Validator = (cell, result, routeDto, columnName) =>
                    ValidateRoute(cell, result, routeDto, columnName)
            },
            new()
            {
                Index = (int)ExcelColumnIndexEnum.PlanningTemplateName,
                Name = "Tên KHVC Mẫu",
                Validator = (cell, result, routeDto, columnName) =>
                    ValidateName(cell, result, columnName)
            },
            new()
            {
                Index = (int)ExcelColumnIndexEnum.VehicleType,
                Name = "Loại Phương tiện",
                Validator = (cell, result, routeDto, columnName) =>
                    ValidateVehicleTypes(cell, result, routeDto, columnName)
            },
            new()
            {
                Index = (int)ExcelColumnIndexEnum.PriorityNumber,
                Name = "Ưu tiên",
                Validator = (cell, result, routeDto, columnName) =>
                    ValidatePriorityNumber(cell, result, columnName)
            },
            new()
            {
                Index = (int)ExcelColumnIndexEnum.Details,
                Name = "Mã Điểm dừng * Nghiệp vụ * thời gian hoạt động",
                Validator = (cell, result, routeDto, columnName) =>
                    ValidateDetails(cell, result, routeDto, columnName)
            },
            //new()//TODO: handle modify item
            //{
            //    Index = (int)ExcelColumnIndexEnum.PlanningTemplateCode,
            //    Name = "Mã KHVC Mẫu",
            //    Validator = (cell, result, routeDto, columnName) =>
            //        ValidatePriorityNumber(cell, result, routeDto, columnName)
            //}
        };
    }
}

internal class ExcelColumnConfig
{
    public int Index { get; init; }
    public string Name { get; init; } = string.Empty;
    public Action<ICell, RowValidationResult, RouteDto?, string> Validator { get; init; }
}

internal enum ExcelColumnIndexEnum
{
    RouteCode,
    PlanningTemplateName,
    VehicleType,
    PriorityNumber,
    Details,
    PlanningTemplateCode
}
