﻿using System.ComponentModel;

namespace TMS.PlanningService.Domain.Enum;

public enum BusinessOperation
{
    /// <summary>
    /// Xuất phát
    /// </summary>
    [Description("Xuất phát")]
    Departure,

    /// <summary>
    /// Đi nhận
    /// </summary>
    [Description("Đi nhận")]
    Receive,

    /// <summary>
    /// Đi phát
    /// </summary>
    [Description("Đi phát")]
    Delivery,

    /// <summary>
    /// Đón hàng
    /// </summary>
    [Description("Đ<PERSON> hàng")]
    Pickup,

    /// <summary>
    /// G<PERSON>i hàng
    /// </summary>
    [Description("Gửi hàng")]
    Send,

    /// <summary>
    /// Kết nối
    /// </summary>
    [Description("Kết nối")]
    Connect,

    /// <summary>
    /// C<PERSON>u hộ
    /// </summary>
    [Description("Cứu hộ")]
    Rescue,

    /// <summary>
    /// Đi nhận/phát
    /// </summary>
    [Description("Đi nhận/phát")]
    ReceiveAndDelivery,

    /// <summary>
    /// Đón/Gửi hàng
    /// </summary>
    [Description("Đón/Gửi hàng")]
    PickupAndSend
}
