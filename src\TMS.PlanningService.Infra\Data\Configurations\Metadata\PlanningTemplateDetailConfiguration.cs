﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class PlanningTemplateDetailConfiguration : IEntityTypeConfiguration<PlanningTemplateDetailEntity>
{
    public void Configure(EntityTypeBuilder<PlanningTemplateDetailEntity> builder)
    {
        // Table
        builder.ToTable("planning_template_detail");

        // Primary Key
        builder.HasKey(x => x.Id)
               .HasName("pk_planning_template_detail");

        builder.Property(x => x.Id)
            .HasColumnName("id");

        builder.Property(x => x.PlanningTemplateId)
            .HasColumnName("planning_template_id")
            .IsRequired();

        builder.Property(x => x.PostOfficeId)
            .HasColumnName("post_office_id")
            .IsRequired();

        builder.Property(x => x.FromTime)
            .HasColumnName("from_time")
            .IsRequired();

        builder.Property(x => x.FromAddDays)
            .HasColumnName("from_add_days")
            .HasDefaultValue(0)
            .IsRequired();

        builder.Property(x => x.ToTime)
            .HasColumnName("to_time")
            .IsRequired();

        builder.Property(x => x.ToTimeAddDays)
            .HasColumnName("to_add_days")
            .HasDefaultValue(0)
            .IsRequired();

        builder.Property(x => x.BusinessOperation)
            .HasColumnName("business_operation")
            .IsRequired();
         
        builder.Property(x => x.DistanceBetweenPoints)
            .HasColumnName("distance_between_points")
            .HasColumnType("real")
            .IsRequired();

         
        // Relationship
        builder.HasOne(p => p.PlanningTemplate)
            .WithMany(pg => pg.Details)
            .HasForeignKey(p => p.PlanningTemplateId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
