﻿using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.PriorityPlan;

public class UpdatePriorityPlanRequest
{
    public Guid Id { get; set; }
    public string? PriorityPlanName { get; set; }
    public string? Description { get; set; }
    public bool? IsActive { get; set; }
    public List<UpdatePriorityPlanGroupRequest> PriorityPlanGroups { get; set; } = new List<UpdatePriorityPlanGroupRequest>();
}

public class UpdatePriorityPlanGroupRequest
{
    public Guid Id { get; set; }
    public Guid PriorityPlanId { get; set; }
    public LogicalOperator? LogicOperator { get; set; }
    public List<UpdatePriorityPlanGroupAttrRequest> PriorityPlanGroupAttributes { get; set; } = new List<UpdatePriorityPlanGroupAttrRequest>();
}

public class UpdatePriorityPlanGroupAttrRequest
{
    public Guid Id { get; set; }
    public Guid PriorityPlanGroupId { get; set; }
    public PriorityType PropertyType { get; set; }
    public LocationType? LocationType { get; set; }
    public PriorityTypeOperator PropertyOperator { get; set; }
    public string? Values { get; set; }
    public LogicalOperator? LogicOperator { get; set; }
}

public class UpdateIsActivePriorityPlanRequest
{
    public Guid Id { get; set; }
    public bool IsActive { get; set; }
}

