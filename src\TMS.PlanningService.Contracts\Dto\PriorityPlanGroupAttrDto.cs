﻿using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.Dto;
public class PriorityPlanGroupAttrDto
{
    public Guid? Id { get; set; }
    public Guid? PriorityPlanGroupId { get; set; }
    public PriorityType PropertyType { get; set; }
    public LocationType? LocationType { get; set; }
    public PriorityTypeOperator PropertyOperator { get; set; }
    public string? Values { get; set; }
    public LogicalOperator? LogicOperator { get; set; }
    public List<AttributeValuesDto>? ValueArray { get; set; }
    public int StepNumber { get; set; }
};

public class AttributeValuesDto
{
    public string? Id { get; set; }
    public string? Code { get; set; }
    public string? Name { get; set; }
}
