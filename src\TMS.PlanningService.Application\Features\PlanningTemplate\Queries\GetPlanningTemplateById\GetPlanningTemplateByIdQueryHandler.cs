﻿using LinqKit;
using MediatR;
using TMS.PlanningService.Application.Services.Implements;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.PlanningTemplate;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Utilities;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Queries.GetPlanningTemplateById;

public class GetPlanningTemplateByIdQueryHandler : IRequestHandler<GetPlanningTemplateByIdQuery, PlanningTemplateDto?>
{
    private readonly IBaseRepository<PlanningTemplateEntity> _repository;
    private readonly IExternalDataService _externalDataService;

    public GetPlanningTemplateByIdQueryHandler(
        IBaseRepository<PlanningTemplateEntity> repository,
        IExternalDataService externalDataService)
    {
        _repository = repository;
        _externalDataService = externalDataService;
    }

    public async Task<PlanningTemplateDto?> Handle(GetPlanningTemplateByIdQuery request, CancellationToken cancellationToken)
    {
        var predicate = PredicateBuilder.New<PlanningTemplateEntity>(true);

        predicate = predicate.And(e => e.Id == request.Id && !e.IsDeleted);

        var data = await _repository.FindWithIncludeAsync(predicate, x => x.Details);
        var entity = data.FirstOrDefault();

        if (entity is null)
            return null;

        var item = new PlanningTemplateDto
        {
            Id = entity.Id,
            CompanyId = entity.CompanyId,
            RouteId = entity.RouteId,
            Code = entity.Code ?? string.Empty,
            Name = entity.Name,
            PriorityNumber = entity.PriorityNumber,
            VehicleTypeId = entity.VehicleTypeId,
            TotalDistance = entity.TotalDistance,
            TotalDuration = entity.TotalDuration,
            StopCount = entity.OfficeCount,
            IsActive = entity.IsActive,
            CreatedBy = entity.CreatedBy,
            CreatedAt = entity.CreatedAt,
            Details = entity.Details.OrderBy(x => x.StepNumber).Select(d => new PlanningTemplateDetailDto
            {
                Id = d.Id,
                PlanningTemplateId = d.PlanningTemplateId,
                PostOfficeId = d.PostOfficeId,
                FromTime = d.FromTime,
                FromAddDays = d.FromAddDays,
                ToTime = d.ToTime,
                ToTimeAddDays = d.ToTimeAddDays,
                BusinessOperation = d.BusinessOperation,
                BusinessOperationName = d.BusinessOperation.GetDescription(),
                DistanceBetweenPoints = d.DistanceBetweenPoints,
                StepNumber = d.StepNumber

            }).ToList()
        };

        await _externalDataService.EnrichWithEmployeeDataAsync(new List<PlanningTemplateDto> { item });
        await _externalDataService.EnrichWithRouteDataAsync(new List<PlanningTemplateDto> { item });
        return item;
    }
}
