﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.DailyPlanning.Queries.GetDailyPlanById;
using TMS.PlanningService.Application.Features.DailyPlanning.Queries.GetDailyPlans;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Api.Controllers;

/// <summary>
/// Controller for managing daily planning operations
/// Provides endpoints for listing plans and viewing plan details with routes
/// </summary>
[ApiController]
[Route("api/v{version:apiVersion}/daily-planning-aggregates")]
[Produces("application/json")]
public class DailyPlanningController : ControllerBase
{
    private readonly IMediator _mediator;
    private readonly ILogger<DailyPlanningController> _logger;

    public DailyPlanningController(
        IMediator mediator,
        ILogger<DailyPlanningController> logger)
    {
        _mediator = mediator;
        _logger = logger;
    }

    /// <summary>
    /// Get all daily plans with optional filters and pagination
    /// Returns daily plans with route counts and total orders from Redis
    /// </summary>
    /// <param name="request">Filter and pagination parameters</param>
    /// <returns>Paginated list of daily plans</returns>
    /// <response code="200">Returns the paginated list of daily plans</response>
    /// <response code="400">If the request is invalid</response>
    [HttpPost("search")]
    [ProducesResponseType(typeof(PagedResult<DailyPlanDto>), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<PagedResult<DailyPlanDto>>> GetDailyPlans(
        [FromBody] GetDailyPlansRequest request)
    {
        _logger.LogInformation(
            "Getting daily plans - Page: {Page}, PageSize: {PageSize}, ExecutionDate: {ExecutionDate}",
            request.Page,
            request.PageSize,
            request.ExecutionDateFrom);

        var query = new GetDailyPlansQuery(request);
        var result = await _mediator.Send(query);

        return Ok(result);
    }

    /// <summary>
    /// Get daily plan detail by ID including all route aggregations
    /// Returns plan information with real-time route data from Redis
    /// </summary>
    /// <param name="id">Daily plan ID</param>
    /// <returns>Daily plan detail with routes</returns>
    /// <response code="200">Returns the daily plan detail</response>
    /// <response code="404">If the daily plan is not found</response>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(DailyPlanDetailDto), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<DailyPlanDetailDto>> GetDailyPlanById(Guid id)
    {
        _logger.LogInformation("Getting daily plan detail for ID: {DailyPlanId}", id);

        var query = new GetDailyPlanByIdQuery(id);
        var result = await _mediator.Send(query);

        if (result == null)
        {
            _logger.LogWarning("Daily plan not found: {DailyPlanId}", id);
        }

        return Ok(result);
    } 
}
