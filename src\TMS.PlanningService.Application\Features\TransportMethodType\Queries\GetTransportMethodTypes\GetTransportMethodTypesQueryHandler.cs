﻿using System.Linq.Expressions;
using LinqKit;
using Mapster;
using MediatR;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;
using Entity = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Features.TransportMethodType.Queries.GetTransportMethodTypes;

public class GetTransportMethodTypesQueryHandler : IRequestHandler<GetTransportMethodTypesQuery, List<TransportMethodTypeDto>>
{
    private readonly IMetadataCacheService _metadataCacheService;
    private readonly IBaseRepository<Entity.TransportMethodType> _baseRepository;
    public GetTransportMethodTypesQueryHandler(IMetadataCacheService metadataCacheService,
        IBaseRepository<Entity.TransportMethodType> baseRepository)
    {
        _metadataCacheService = metadataCacheService;
        _baseRepository = baseRepository;
    }

    public async Task<List<TransportMethodTypeDto>> Handle(GetTransportMethodTypesQuery request, CancellationToken cancellationToken)
    {
        var transportMethodTypes = await _metadataCacheService.GetTransportMethodTypesAsync<Entity.TransportMethodType>();
        var data = transportMethodTypes.Select(x => new TransportMethodTypeDto
        {
            Id = x.Id,
            Name = x.Name,
        }).ToList();

        if (!data.Any())
        {
            data = (await _baseRepository.GetAllAsync()).Select(x => new TransportMethodTypeDto
            {
                Id = x.Id,
                Name = x.Name,
            }).ToList();
        }

        return data.OrderBy(x => x.Id).ToList();
    }
}
