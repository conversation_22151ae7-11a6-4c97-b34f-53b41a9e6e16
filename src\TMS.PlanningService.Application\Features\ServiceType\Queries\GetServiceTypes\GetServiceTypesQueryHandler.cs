﻿
using MediatR;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Features.ExtraService.Queries.GetExtraServices;

public class GetServiceTypesQueryHandler : IRequestHandler<GetServiceTypesQuery, List<ServiceTypeDto>>
{
    private readonly IMetadataCacheService _metadataService;
    private readonly IBaseRepository<ServiceType> _baseRepository;
    public GetServiceTypesQueryHandler(IMetadataCacheService metadataService,
        IBaseRepository<ServiceType> baseRepository)
    {
        _metadataService = metadataService;
        _baseRepository = baseRepository;
    }

    public async Task<List<ServiceTypeDto>> Handle(GetServiceTypesQuery request, CancellationToken cancellationToken)
    {
        var extraServices = await _metadataService.GetServiceTypesAsync<ServiceType>();

        var data = extraServices.Select(x => new ServiceTypeDto
        {
            ServiceTypeId = x.ServiceTypeId,
            ServiceTypeName = x.ServiceTypeName,
            ServiceTypeStatus = x.ServiceTypeStatus,
            IsActive = x.IsActive ?? false
        }).ToList();

        if (!data.Any())
        {
            data = (await _baseRepository.GetAllAsync()).Select(x => new ServiceTypeDto
            {
                ServiceTypeId = x.ServiceTypeId,
                ServiceTypeName = x.ServiceTypeName,
                ServiceTypeStatus = x.ServiceTypeStatus,
                IsActive = x.IsActive ?? false
            }).ToList();
        }    

        return data.OrderBy(x => x.ServiceTypeId).ToList();
    }
}
