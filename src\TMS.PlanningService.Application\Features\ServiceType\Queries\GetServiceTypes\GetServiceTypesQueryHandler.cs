﻿
using MediatR;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.SharedKernal.Caching;

namespace TMS.PlanningService.Application.Features.ExtraService.Queries.GetExtraServices;

public class GetServiceTypesQueryHandler : IRequestHandler<GetServiceTypesQuery, List<ServiceTypeDto>>
{
    private readonly IMetadataCacheService _metadataService;
    public GetServiceTypesQueryHandler(IMetadataCacheService metadataService)
    {
        _metadataService = metadataService;
    }

    public async Task<List<ServiceTypeDto>> Handle(GetServiceTypesQuery request, CancellationToken cancellationToken)
    {
        var extraServices = await _metadataService.GetServiceTypesAsync<ServiceType>();

        var data = extraServices.Select(x => new ServiceTypeDto
        {
            ServiceTypeId = x.ServiceTypeId,
            ServiceTypeName = x.ServiceTypeName,
            ServiceTypeStatus = x.ServiceTypeStatus,
            IsActive = x.IsActive ?? false
        }).OrderBy(x => x.ServiceTypeId).ToList();
        return data;
    }
}
