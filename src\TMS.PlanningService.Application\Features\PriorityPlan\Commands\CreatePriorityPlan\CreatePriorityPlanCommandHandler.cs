﻿using MediatR;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.IRepository;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Constants.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using Entities = TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.CreatePriorityPlan;

public class CreatePriorityPlanCommandHandler : IRequestHandler<CreatePriorityPlanCommand, Guid>
{
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IPriorityPlanRepository _priorityPlanRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IMetadataCacheService _metadataCacheService;

    public CreatePriorityPlanCommandHandler(
        ICurrentFactorProvider currentFactorProvider,
        IPriorityPlanRepository priorityPlanRepository,
        IUnitOfWork unitOfWork,
        IMetadataCacheService metadataCacheService)
    {
        _currentFactorProvider = currentFactorProvider;
        _priorityPlanRepository = priorityPlanRepository;
        _unitOfWork = unitOfWork;
        _metadataCacheService = metadataCacheService;
    }

    public async Task<Guid> Handle(CreatePriorityPlanCommand request, CancellationToken cancellationToken)
    {
        var createDto = request.ParamRequest;
        var companyId = _currentFactorProvider.CompanyId;
        if (companyId == Guid.Empty)
            throw new BusinessRuleValidationException("Company", MessageFormatter.FormatNotFound("Company", companyId));

        var isExist = await _priorityPlanRepository.ExistsAsync(x => x.PriorityPlanName == createDto.PriorityPlanName && !x.IsDeleted, cancellationToken);
        if (isExist)
            throw new BusinessRuleValidationException(nameof(Entities.Metadata.PriorityPlan.PriorityPlanName), MessageFormatter.FormatAlreadyExists(nameof(PriorityPlan), createDto.PriorityPlanName), CommonErrorCodes.MS002);

        var entity = new Entities.Metadata.PriorityPlan
        {
            PriorityPlanName = createDto.PriorityPlanName,
            Description = createDto.Description,
            IsActive = createDto.IsActive ?? true,
            CompanyId = companyId,
        };

        entity.PriorityPlanGroups = createDto.PriorityPlanGroups?.Select((gp, gpIndex) => new Entities.Metadata.PriorityPlanGroup
        {
            PriorityPlanId = entity.Id,
            LogicOperator = gp.LogicOperator,
            StepNumber = gpIndex,
            PriorityAttributes = gp.PriorityPlanGroupAttributes?.Select((pp, attrIndex) => new Entities.Metadata.PriorityPlanGroupAttr
            {
                LocationType = pp.LocationType,
                LogicOperator = pp.LogicOperator,
                PropertyOperator = pp.PropertyOperator,
                PropertyType = pp.PropertyType,
                Values = pp.Values,
                StepNumber = attrIndex
            }).OrderByDescending(x => x.Id).ToList() ?? new List<PriorityPlanGroupAttr>()
        }).ToList() ?? new List<PriorityPlanGroup>();

        await _priorityPlanRepository.AddAsync(entity, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        //====Redis cache update
        var priorityPlans = await _priorityPlanRepository.GetActivePriorityPlanAsync(cancellationToken);
        await _metadataCacheService.SetPriorityPlansAsync(priorityPlans);
        return entity.Id;
    }


}
