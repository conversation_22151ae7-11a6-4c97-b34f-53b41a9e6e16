﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities;

namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class PlanningTemplateConfiguration : IEntityTypeConfiguration<PlanningTemplateEntity>
{
    public void Configure(EntityTypeBuilder<PlanningTemplateEntity> builder)
    {
        // Table
        builder.ToTable("planning_template");

        // Primary Key
        builder.HasKey(x => x.Id)
               .HasName("pk_planning_template");

        builder.Property(x => x.Id)
            .HasColumnName("id");

        builder.Property(x => x.CompanyId)
            .HasColumnName("company_id")
            .IsRequired();

        builder.Property(x => x.RouteId)
            .HasColumnName("route_id")
            .IsRequired();

        builder.Property(x => x.Code)
            .HasColumnName("code")
            .HasMaxLength(50)
            .IsRequired();

        builder.Property(x => x.Name)
            .HasColumnName("name")
            .HasMaxLength(255)
            .IsRequired();

        builder.Property(x => x.SearchValue)
            .HasColumnName("search_value")
            .HasMaxLength(500);

        builder.Property(x => x.PriorityNumber)
            .HasColumnName("priority_number")
            .HasDefaultValue(1)
            .IsRequired();

        builder.Property(x => x.VehicleTypeId)
            .HasColumnName("vehicle_type_id")
            .IsRequired();

        builder.Property(x => x.IsActive)
            .HasColumnName("is_active")
            .HasDefaultValue(true)
            .IsRequired();

        builder.Property(x => x.CreatedAt)
            .HasColumnName("created_at")
            .IsRequired();

        builder.Property(x => x.CreatedBy)
            .HasColumnName("created_by")
            .IsRequired();

        builder.Property(x => x.UpdatedAt)
            .HasColumnName("updated_at")
            .IsRequired();

        builder.Property(x => x.UpdatedBy)
            .HasColumnName("updated_by")
            .IsRequired();

        builder.Property(x => x.IsDeleted)
            .HasColumnName("is_deleted")
            .HasDefaultValue(false)
            .IsRequired();
         
    }
}
