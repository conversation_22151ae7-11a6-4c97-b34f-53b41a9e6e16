﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;
namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class ServiceTypeConfiguration : IEntityTypeConfiguration<ServiceType>
{
    public void Configure(EntityTypeBuilder<ServiceType> builder)
    {
        builder.ToTable("service_type");

        builder.<PERSON><PERSON>ey(o => new { o.ServiceTypeId });

        builder.Property(o => o.ServiceTypeId)
            .IsRequired()
            .HasMaxLength(50)
            .HasColumnName("service_type_id");

        builder.Property(o => o.ServiceTypeName)
            .HasMaxLength(250)
            .HasColumnName("service_type_name");

        builder.Property(x => x.ServiceTypeStatus)
               .HasColumnName("service_type_status");

        builder.Property(x => x.IsActive)
               .HasColumnName("is_active");
    }
}
