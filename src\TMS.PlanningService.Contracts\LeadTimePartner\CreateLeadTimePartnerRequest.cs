﻿namespace TMS.PlanningService.Contracts.LeadTimePartner;

public class CreateLeadTimePartnerRequest
{
    public string? LeadTimeId { get; set; }
    public Guid? PartnerId { get; set; }
    public TimeSpan? FromTime { get; set; }
    public TimeSpan? ToTime { get; set; }
    public int? FromAddDays { get; set; } = 0;
    public int? ToAddDays { get; set; } = 0;
    public string? SenderPostOffice { get; set; }
    public string? ReceivePostOffice { get; set; }
    public string? Description { get; set; }
}


