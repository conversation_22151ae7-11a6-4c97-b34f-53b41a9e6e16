﻿using Microsoft.EntityFrameworkCore;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.IRepository;
using TMS.PlanningService.Infra.Data;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.PlanningService.Infra.Repository;
public class ServiceTypeRepository : BaseRepository<ServiceType>, IServiceTypeRepository
{
    public ServiceTypeRepository(ApplicationDbContext context): base(context)
    {
    }
}
