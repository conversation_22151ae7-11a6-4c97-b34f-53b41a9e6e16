﻿using TMS.SharedKernal.Kafka.Abstractions;
using TMS.SharedKernal.RabbitMq.Abstractions;

namespace TMS.PlanningService.Contracts.Orders;

/// <summary>
/// Rabbit event from OrderService containing order id data for planning
/// </summary>
public class RabbitMqDeleteOrderEvent : IEvent
{
    public string EventType => "DeleteorderPlanning";
    public string EventId { get; set; } = Guid.NewGuid().ToString();
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// List of Order IDs (Mailer IDs)
    /// </summary>
    public List<string> OrderIds { get; set; } = new();

}
