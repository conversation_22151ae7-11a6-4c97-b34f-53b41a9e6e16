﻿using FluentValidation;

namespace TMS.PlanningService.Application.Features.Planning.Commands.QueueBatchPlanning;

public class QueueBatchPlanningCommandValidator : AbstractValidator<QueueBatchPlanningCommand>
{
    public QueueBatchPlanningCommandValidator()
    {
        RuleFor(x => x.BatchRequest)
            .NotNull()
            .WithMessage("Batch request is required");

        RuleFor(x => x.BatchRequest.BatchId)
            .NotEmpty()
            .WithMessage("Batch Id is required");

        RuleFor(x => x.BatchRequest.PlanningData)
            .NotNull()
            .NotEmpty()
            .WithMessage("Planning data list cannot be null or empty");

        RuleForEach(x => x.BatchRequest.PlanningData)
            .NotNull()
            .WithMessage("Individual planning requests cannot be null");

        RuleForEach(x => x.BatchRequest.PlanningData)
            .Must(planningRequest => planningRequest.MailerRouteMaster != null)
            .WithMessage("MailerRouteMaster is required for each planning request");

        RuleForEach(x => x.BatchRequest.PlanningData)
            .Must(planningRequest => !string.IsNullOrWhiteSpace(planningRequest.MailerRouteMaster?.MailerId))
            .WithMessage("MailerId is required for each planning request");
         
    }
}
