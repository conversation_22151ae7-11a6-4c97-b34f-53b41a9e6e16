﻿using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Contracts.PlanningTemplate;

namespace TMS.PlanningService.Application.Services.Implements;

public class ExternalDataService : IExternalDataService
{
    private readonly IDriverServiceApi _driverServiceApi;
    private readonly IRouteServiceApi _routeServiceApi;
    private readonly IFleetServiceApi _fleetServiceApi;

    public ExternalDataService(
        IDriverServiceApi driverServiceApi,
        IRouteServiceApi routeServiceApi,
        IFleetServiceApi fleetServiceApi)
    {
        _driverServiceApi = driverServiceApi;
        _routeServiceApi = routeServiceApi;
        _fleetServiceApi = fleetServiceApi;
    }

    public async Task EnrichWithEmployeeDataAsync(List<PlanningTemplateDto> planningTemplateDtos)
    {
        var employeeIds = planningTemplateDtos
            .Where(x => x.CreatedBy != Guid.Empty)
            .Select(x => x.CreatedBy)
            .Distinct()
            .ToList();

        if (!employeeIds.Any())
            return;

        //Todo: Try/catch can be added here for robustness in case the driver service is unavailable
        var employeeDtos = await _driverServiceApi.GetEmployeesByIdsAsync(employeeIds);

        if (employeeDtos?.Any() != true)
            return;

        foreach (var planningTemplate in planningTemplateDtos.Where(pt => pt.CreatedBy != Guid.Empty))
        {
            var employee = employeeDtos.FirstOrDefault(e => e.Id == planningTemplate.CreatedBy);
            if (employee != null)
            {
                planningTemplate.CreatedByCode = employee.Code ?? string.Empty;
                planningTemplate.CreatedByName = employee.Name ?? string.Empty;
                planningTemplate.CreatedByAvatar = employee.ProfileImageUrl;
            }
        }
    }
    public async Task EnrichWithRouteDataAsync(List<PlanningTemplateDto> planningTemplateDtos)
    {
        if (!planningTemplateDtos.Any())
            return;

        var routeIds = planningTemplateDtos
            .Select(x => x.RouteId)
            .Distinct()
            .ToList();

        //Todo: Try/catch can be added here for robustness in case the route service is unavailable
        var routes = await _routeServiceApi.GetRoutesByIdsAsync(routeIds);

        foreach (var planningTemplate in planningTemplateDtos)
        {
            var (startedOffice, endedOffice) = GetFirstAndLastOffices(planningTemplate.Details);

            if (planningTemplate.RouteId != Guid.Empty)
            {
                var route = routes.FirstOrDefault(r => r.Id == planningTemplate.RouteId);
                if (route != null)
                {
                    EnrichPlanningTemplateWithRouteData(planningTemplate, route, startedOffice, endedOffice);
                }
            }
        }
    }
    private static (PlanningTemplateDetailDto? startedOffice, PlanningTemplateDetailDto? endedOffice) GetFirstAndLastOffices(
        List<PlanningTemplateDetailDto> details)
    {
        var startedOffice = details
            .OrderBy(d => d.StepNumber)
            .FirstOrDefault();

        var endedOffice = details
            .OrderByDescending(d => d.StepNumber)
            .FirstOrDefault();

        return (startedOffice, endedOffice);
    }
    private static void EnrichPlanningTemplateWithRouteData(
        PlanningTemplateDto planningTemplate,
        RouteDto route,
        PlanningTemplateDetailDto? startedOffice,
        PlanningTemplateDetailDto? endedOffice)
    {
        planningTemplate.RouteName = route.Name ?? string.Empty;

        var vehicleType = route.VehicleTypes?.FirstOrDefault(v => v.VehicleTypeId == planningTemplate.VehicleTypeId);
        planningTemplate.VehicleTypeName = vehicleType?.Name ?? string.Empty;

        if (startedOffice != null)
        {
            var startPostOffice = route.PostOffices?.FirstOrDefault(p => p.PostOfficeId == startedOffice.PostOfficeId);
            planningTemplate.StartedOfficeName = startPostOffice?.PostOfficeName ?? string.Empty;
            planningTemplate.StartedOfficeCode = startPostOffice?.PostOfficeCode ?? string.Empty;
        }

        if (endedOffice != null)
        {
            var endPostOffice = route.PostOffices?.FirstOrDefault(p => p.PostOfficeId == endedOffice.PostOfficeId);
            planningTemplate.EndedOfficeName = endPostOffice?.PostOfficeName ?? string.Empty;
            planningTemplate.EndedOfficeCode = endPostOffice?.PostOfficeCode ?? string.Empty;
        }

        EnrichPlanningTemplateDetails(planningTemplate.Details, route);
    }
    private static void EnrichPlanningTemplateDetails(List<PlanningTemplateDetailDto> details, RouteDto route)
    {
        foreach (var detail in details)
        {
            var postOffice = route.PostOffices?.FirstOrDefault(p => p.PostOfficeId == detail.PostOfficeId);
            if (postOffice != null)
            {
                detail.PostOfficeName = postOffice.PostOfficeName ?? string.Empty;
                detail.PostOfficeTypeName = postOffice.PostOfficeTypeName ?? string.Empty;
                detail.PostOfficeCode = postOffice.PostOfficeCode ?? string.Empty;
                detail.Latitude = postOffice.Latitude;
                detail.Longitude = postOffice.Longitude;
            }
        }
    }

    public async Task GenericEnrichWithEmployeeDataAsync<T>(List<T> items, bool? getUserUpdate = false)
    {
        // Nếu danh sách rỗng thì dừng
        if (items == null || !items.Any())
            return;

        var type = typeof(T);
        var createdByProp = type.GetProperty("CreatedBy");
        var updatedByProp = getUserUpdate == true ? type.GetProperty("UpdatedBy") : null;
        /**
         * createdByProp => null: không có trường CreatedBy thì dừng
         * getUserUpdate == true && updatedByProp == null => có yêu cầu lấy thông tin người cập nhật nhưng không có trường UpdatedBy thì dừng
        **/
        if (createdByProp == null || (getUserUpdate == true && updatedByProp == null))
            return;

        var createdByCodeProp = type.GetProperty("CreatedByCode");
        var createdByNameProp = type.GetProperty("CreatedByName");
        var createdByAvatarProp = type.GetProperty("CreatedByAvatar");

        var updatedByCodeProp = getUserUpdate == true ? type.GetProperty("UpdatedByCode") : null;
        var updatedByNameProp = getUserUpdate == true ? type.GetProperty("UpdatedByName") : null;
        var updatedByAvatarProp = getUserUpdate == true ? type.GetProperty("UpdatedByAvatar") : null;

        // ==== Thu thập toàn bộ employeeId cần fetch ====
        var employeeIds = new HashSet<Guid>();

        foreach (var item in items)
        {
            if (createdByProp?.GetValue(item) is Guid createdBy && createdBy != Guid.Empty)
                employeeIds.Add(createdBy);

            if (getUserUpdate == true && updatedByProp?.GetValue(item) is Guid updatedBy && updatedBy != Guid.Empty)
                employeeIds.Add(updatedBy);
        }

        if (!employeeIds.Any())
            return;

        // ==== Gọi API lấy danh sách employee ====
        var employeeDtos = await _driverServiceApi.GetEmployeesByIdsAsync(employeeIds.ToList());
        if (employeeDtos?.Any() != true)
            return;

        // ==== Gán dữ liệu vào item ====
        foreach (var item in items)
        {
            // --- CreatedBy ---
            if (createdByProp?.GetValue(item) is Guid createdBy && createdBy != Guid.Empty)
            {
                var employee = employeeDtos.FirstOrDefault(e => e.Id == createdBy);
                if (employee != null)
                {
                    createdByCodeProp?.SetValue(item, employee.Code ?? string.Empty);
                    createdByNameProp?.SetValue(item, employee.Name ?? string.Empty);
                    createdByAvatarProp?.SetValue(item, employee.ProfileImageUrl);
                }
            }

            // --- UpdatedBy ---
            if (getUserUpdate == true && updatedByProp?.GetValue(item) is Guid updatedBy && updatedBy != Guid.Empty)
            {
                var employee = employeeDtos.FirstOrDefault(e => e.Id == updatedBy);
                if (employee != null)
                {
                    updatedByCodeProp?.SetValue(item, employee.Code ?? string.Empty);
                    updatedByNameProp?.SetValue(item, employee.Name ?? string.Empty);
                    updatedByAvatarProp?.SetValue(item, employee.ProfileImageUrl);
                }
            }
        }
    }

    public async Task EnrichWithDataDailyPlanAsync(List<DailyPlanDto> dtos)
    {
        if (!dtos.Any())
            return;

        var vehicleTypeIds = dtos
            .Where(x => x.VehicleTypeId != Guid.Empty)
            .Select(x => x.VehicleTypeId)
            .Distinct()
            .ToList();

        if (!vehicleTypeIds.Any())
            return;

        //Todo: Try/catch can be added here for robustness in case the fleet service is unavailable
        var vehicleTypeRequest = new GetVehiclesByIdsRequest(vehicleTypeIds);
        var vehicleTypeDtos = await _fleetServiceApi.GetVehicleTypesByIdsAsync(vehicleTypeRequest);

        if (vehicleTypeDtos?.Any() != true)
            return;

        foreach (var dailyPlan in dtos.Where(dp => dp.VehicleTypeId != Guid.Empty))
        {
            var vehicleType = vehicleTypeDtos.FirstOrDefault(vt => vt.Id == dailyPlan.VehicleTypeId);
            if (vehicleType != null)
            {
                dailyPlan.VehicleTypeName = vehicleType.Name ?? string.Empty;
            }
        }

        var officeCodes = new HashSet<string>();
        foreach (var route in dtos.SelectMany(t => t.PostOfficeDtos))
        {
            if (!string.IsNullOrEmpty(route.FromCode))
                officeCodes.Add(route.FromCode);
            if (!string.IsNullOrEmpty(route.ToCode))
                officeCodes.Add(route.ToCode);
        }

        // If you need a List<string>:
        var officeCodeList = officeCodes.ToList();
        if (!officeCodes.Any())
            return;

        var postOffices = await _routeServiceApi.GetPostOfficesByCodesAsync(officeCodeList);
        if (postOffices?.Any() != true)
            return;

        var postOfficeLookup = postOffices
              .Where(t => !string.IsNullOrEmpty(t.PostOfficeCode))
              .GroupBy(t => t.PostOfficeCode)
              .ToDictionary(g => g.Key, g => g.First());

        foreach (var route in dtos)
        {
            foreach (var p in route.PostOfficeDtos)
            {
                if (postOfficeLookup.TryGetValue(p.FromCode, out var fromOffice))
                    p.FromName = fromOffice.PostOfficeName ?? string.Empty;
                if (postOfficeLookup.TryGetValue(p.ToCode, out var toOffice))
                    p.ToName = toOffice.PostOfficeName ?? string.Empty;
            }
        }

    }

    public async Task EnrichWithDailyPlanDetailAsync(DailyPlanDetailDto dto)
    {
        if (dto == null || dto.Routes == null || !dto.Routes.Any())
            return;

        if (dto.VehicleTypeId != Guid.Empty)
        {
            var vehicleTypeRequest = new GetVehiclesByIdsRequest(new List<Guid> { dto.VehicleTypeId });
            var vehicleTypeDtos = await _fleetServiceApi.GetVehicleTypesByIdsAsync(vehicleTypeRequest);
            if (vehicleTypeDtos?.Any() == true)
            {
                var vehicleType = vehicleTypeDtos.FirstOrDefault(vt => vt.Id == dto.VehicleTypeId);
                if (vehicleType != null)
                    dto.VehicleTypeName = vehicleType.Name;
            }
        }

        var officeCodes = new HashSet<string>();
        foreach (var route in dto.Routes)
        {
            if (!string.IsNullOrEmpty(route.FromOfficeId))
                officeCodes.Add(route.FromOfficeId);
            if (!string.IsNullOrEmpty(route.ToOfficeId))
                officeCodes.Add(route.ToOfficeId);
        }
        // If you need a List<string>:
        var officeCodeList = officeCodes.ToList();
        if (!officeCodes.Any())
            return;

        var postOffices = await _routeServiceApi.GetPostOfficesByCodesAsync(officeCodeList);
        if (postOffices?.Any() != true)
            return;

        var postOfficeLookup = postOffices
              .Where(t => !string.IsNullOrEmpty(t.PostOfficeCode))
              .GroupBy(t => t.PostOfficeCode)
              .ToDictionary(g => g.Key, g => g.First());

        foreach (var route in dto.Routes)
        {
            if (postOfficeLookup.TryGetValue(route.FromOfficeId, out var fromOffice))
                route.FromOfficeName = fromOffice.PostOfficeName ?? string.Empty;
            if (postOfficeLookup.TryGetValue(route.ToOfficeId, out var toOffice))
                route.ToOfficeName = toOffice.PostOfficeName ?? string.Empty;
        }
    }

    public async Task EnrichWithPlanningTemplateByRouteAsync(List<PlanningTemplateByRouteIdDto> dtos)
    {
        var codes = dtos.SelectMany(dto => dto.PostOffices)
            .Select(po => po.Code)
            .Where(code => !string.IsNullOrWhiteSpace(code))
            .Distinct()
            .ToList();

        var offices = await _routeServiceApi.GetPostOfficesByCodesAsync(codes);
        var officeDict = offices.ToDictionary(o => o.PostOfficeCode, o => o.PostOfficeName);
        foreach (var dto in dtos)
        {
            foreach (var postOffice in dto.PostOffices)
            {
                if (officeDict.TryGetValue(postOffice.Code, out var name))
                {
                    postOffice.Name = name;
                }
            }
        }
    }
}
