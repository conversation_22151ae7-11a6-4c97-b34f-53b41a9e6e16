﻿namespace TMS.PlanningService.Domain.Entities;

public class MailerRouteMasterEntity
{ 
    public string MailerId { get; set; } = string.Empty;
    public string ChildMailerId { get; set; } = string.Empty;
    public string? CurrentPostOffice { get; set; }
    public string? NextPostOffice { get; set; }
    public bool? IsCorrectRoute { get; set; }
    public bool? IsFullMailerPlanRoute { get; set; }
    public bool IsHaveMailerPlanRoute { get; set; }
    public string? Status { get; set; }
    public string? FromPostOfficeId { get; set; }
    public string? ToPostOfficeId { get; set; }
    public string? SenderAddress { get; set; }
    public string? ReceiverAddress { get; set; }
    public string? LastPOInPlan { get; set; }
    public DateTime? PlanStartTime { get; set; }
    public DateTime? PlanEndTime { get; set; }
    public int PlanDurationMinutes { get; set; }
    public DateTime? AdjustStartTime { get; set; }
    public DateTime? AdjustEndTime { get; set; }
    public DateTime? ActualStartTime { get; set; }
    public DateTime? ActualEndTime { get; set; }
    public int AdjustDurationMinutes { get; set; }
    public bool IsForwarded { get; set; }
    public string? POsSendWrongWay { get; set; }
    public string? CurrentSLAType { get; set; }
    public DateTime? CurrentSLATime { get; set; }
    public DateTime CreatedDate { get; set; }
    public string? CreatedUserId { get; set; }
    public DateTime LastUpdateDate { get; set; }
    public string? LastUpdateUser { get; set; }

    // Navigation properties
    public List<MailerPlanRouteEntity> PlanRoutes { get; set; } = new();
    public List<MailerAdjustRouteEntity> AdjustRoutes { get; set; } = new();
    public List<MailerActualRouteEntity> ActualRoutes { get; set; } = new();
}
