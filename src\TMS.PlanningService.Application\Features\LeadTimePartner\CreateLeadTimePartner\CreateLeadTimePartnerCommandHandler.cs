﻿using MediatR;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.PlanningService.Domain.IRepository;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Constants.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using Entities = TMS.PlanningService.Domain.Entities.Metadata;

namespace TMS.PlanningService.Application.Features.LeadTimePartner.CreateLeadTimePartner;

public class CreateLeadTimePartnerCommandHandler : IRequestHandler<CreateLeadTimePartnerCommand, Guid>
{
    private readonly IBaseRepository<Entities.LeadTimePartner> _leadTimePartnerRepository;
    private readonly IUnitOfWork _unitOfWork;

    public CreateLeadTimePartnerCommandHandler(
        IBaseRepository<Entities.LeadTimePartner> leadTimePartnerRepository,
        IUnitOfWork unitOfWork)
    {
        _leadTimePartnerRepository = leadTimePartnerRepository;
        _unitOfWork = unitOfWork;
    }

    public async Task<Guid> Handle(CreateLeadTimePartnerCommand request, CancellationToken cancellationToken)
    {
        var requestDto = request.ParamRequest;

        var isExist = await _leadTimePartnerRepository.ExistsAsync(x => x.LeadTimeId == requestDto.LeadTimeId, cancellationToken);
        if (isExist)
            throw new BusinessRuleValidationException(
                nameof(Entities.LeadTimePartner), 
                MessageFormatter.FormatAlreadyExists(nameof(PriorityPlan), 
                requestDto.LeadTimeId ?? string.Empty
             ), CommonErrorCodes.MS002);

        var entity = new Entities.LeadTimePartner
        {
            LeadTimeId = requestDto.LeadTimeId,
            PartnerId = requestDto.PartnerId,
            FromTime = requestDto.FromTime,
            ToTime = requestDto.ToTime,
            FromAddDays = requestDto.FromAddDays,
            ToAddDays = requestDto.ToAddDays,
            ReceivePostOffice = requestDto.ReceivePostOffice,
            SenderPostOffice = requestDto.SenderPostOffice,
            Description = requestDto.Description,
        };

        await _leadTimePartnerRepository.AddAsync(entity, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return entity.Id;
    }


}
