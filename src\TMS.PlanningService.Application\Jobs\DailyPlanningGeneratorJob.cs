﻿//#define BUILD_N
using Microsoft.Extensions.Logging;
using Quartz;
using TMS.PlanningService.Application.Services.Step2.DailyPlanning;

namespace TMS.PlanningService.Application.Jobs;

/// <summary>
/// Quartz job that generates daily real execution plans from planning templates
/// Runs at midnight (0:00 AM) every day
/// Aggregations will be populated in real-time by RabbitMQ events via DailyPlanningAggregationService
/// </summary>
[DisallowConcurrentExecution]
public class DailyPlanningGeneratorJob : IJob
{
    private readonly IDailyPlanningGeneratorService _generatorService;
    private readonly ILogger<DailyPlanningGeneratorJob> _logger;

    public DailyPlanningGeneratorJob(
        IDailyPlanningGeneratorService generatorService,
        ILogger<DailyPlanningGeneratorJob> logger)
    {
        _generatorService = generatorService;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        var executionDate = DateOnly.FromDateTime(DateTime.UtcNow);

        try
        {
            _logger.LogInformation("DailyExecutionPlanGenerator started - generating plans for {Date}", executionDate);

            var generatedCount = await _generatorService.GenerateDailyPlansAsync(
                executionDate: executionDate,
                companyId: null, // Generate for all companies
                cancellationToken: context.CancellationToken);

            stopwatch.Stop();

            _logger.LogInformation(
                "DailyExecutionPlanGenerator completed successfully - generated {Count} plans for {Date} in {ElapsedMs}ms. Aggregations will be populated by RabbitMQ events.",
                generatedCount,
                executionDate,
                stopwatch.ElapsedMilliseconds);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "DailyExecutionPlanGenerator failed for date {Date} after {ElapsedMs}ms",
                executionDate,
                stopwatch.ElapsedMilliseconds);
        }
    }
}
