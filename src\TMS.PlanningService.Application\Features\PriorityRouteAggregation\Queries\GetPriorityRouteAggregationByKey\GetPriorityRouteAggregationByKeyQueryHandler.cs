﻿using MediatR;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application.Common.Queries;
using TMS.PlanningService.Application.Services.Step2.PriorityPlan;
using TMS.PlanningService.Contracts.Dto;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernal.Caching;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Features.PriorityRouteAggregation.Queries.GetPriorityRouteAggregationByKey;

/// <summary>
/// Handler for getting a single priority route aggregation (DE service type only) by FromOffice:ToOffice key
/// Inherits common Redis-first + DB fallback logic from base class
/// </summary>
public class GetPriorityRouteAggregationByKeyQueryHandler
    : RouteAggregationByKeyQueryHandlerBase<GetPriorityRouteAggregationByKeyQuery, PriorityRouteAggregationDto>,
      IRequestHandler<GetPriorityRouteAggregationByKeyQuery, PriorityRouteAggregationDto?>
{
    private readonly IPriorityPlanningAggregationService _priorityAggregationService;
    private readonly IRouteServiceApi _routeServiceApi;
    private readonly ILogger<GetPriorityRouteAggregationByKeyQueryHandler> _logger;
    private readonly IBaseRepository<RouteAggregationEntity> _routeAggregationRepository;
    private readonly IBaseRepository<RouteAggregationOrderEntity> _routeAggregationOrderRepository;
    private readonly IMetadataCacheService _metadataCacheService;

    public GetPriorityRouteAggregationByKeyQueryHandler(
        IPriorityPlanningAggregationService priorityAggregationService,
        IRouteServiceApi routeServiceApi,
        ILogger<GetPriorityRouteAggregationByKeyQueryHandler> logger,
        IBaseRepository<RouteAggregationEntity> routeAggregationRepository,
        IBaseRepository<RouteAggregationOrderEntity> routeAggregationOrderRepository,
        IMetadataCacheService metadataCacheService)
    {
        _priorityAggregationService = priorityAggregationService;
        _routeServiceApi = routeServiceApi;
        _logger = logger;
        _routeAggregationRepository = routeAggregationRepository;
        _routeAggregationOrderRepository = routeAggregationOrderRepository;
        _metadataCacheService = metadataCacheService;
    }

    // Base class property implementations
    protected override ILogger Logger => _logger;
    protected override IRouteServiceApi RouteServiceApi => _routeServiceApi;
    protected override IMetadataCacheService MetadataCacheService => _metadataCacheService;
    protected override IBaseRepository<RouteAggregationEntity> RouteAggregationRepository => _routeAggregationRepository;
    protected override IBaseRepository<RouteAggregationOrderEntity> RouteAggregationOrderRepository => _routeAggregationOrderRepository;
    protected override string AggregationType => "priority";
    protected override bool IncludeChildMailer => false;

    public async Task<PriorityRouteAggregationDto?> Handle(
        GetPriorityRouteAggregationByKeyQuery request,
        CancellationToken cancellationToken)
    {
        return await ExecuteAsync(
            request.RouteKey,
            request.Page,
            request.PageSize,
            request.SearchTerm,
            cancellationToken);
    }

    /// <summary>
    /// Fetches priority route aggregation from Redis using IPriorityPlanningAggregationService
    /// </summary>
    protected override async Task<RouteAggregationSummary?> GetRouteAggregationFromRedisAsync(
        string routeKey,
        CancellationToken cancellationToken)
    {
        return await _priorityAggregationService.GetPriorityRouteAggregationAsync(routeKey, cancellationToken);
    }

    /// <summary>
    /// Maps RouteAggregationSummary and paginated order details to PriorityRouteAggregationDto.
    /// Uses dictionary for O(1) post office name lookup.
    /// </summary>
    protected override Task<PriorityRouteAggregationDto> MapToDtoAsync(
        RouteAggregationSummary summary,
        Dictionary<string, string> postOfficeNames,
        PagedResult<OrderItemDetail>? orderDetails,
        CancellationToken cancellationToken)
    {
        // O(1) lookup using dictionary
        string GetOfficeName(string? code) =>
            code != null && postOfficeNames.TryGetValue(code, out var name) ? name : string.Empty;

        // Map paginated order details to DTO
        PagedResult<PriorityRouteAggregationDetailDto>? detailsDto = null;
        int totalAlmostDueOrders = 0;
        int totalOverdueOrders = 0;

        if (orderDetails != null && orderDetails.Items.Any())
        {
            var detailItems = orderDetails.Items.Select(od => new PriorityRouteAggregationDetailDto
            {
                MailerId = od.MailerId,
                CurrentParentId = od.CurrentParentType != nameof(OrderParentType.CTTAIKIEN)
                    ? (od.CurrentParentId ?? "")
                    : string.Empty,
                CurrentPackingListId = od.CurrentPackingListId ?? string.Empty,
                ServiceTypeId = od.ServiceTypeId ?? "",
                ActualFromTime = od.ActualFromTime,
                PlanFromTime = summary.FromTime,
                ActualToTime = od.ActualToTime,
                PlanToTime = summary.ToTime,
                Weight = od.Weight,
                RealWeight = od.RealWeight,
                CalWeight = od.CalWeight

            }).ToList();

            detailsDto = new PagedResult<PriorityRouteAggregationDetailDto>(
                detailItems,
                orderDetails.TotalCount,
                orderDetails.PageNumber,
                orderDetails.PageSize);

            // Calculate status counts from current page
            totalAlmostDueOrders = detailItems.Count(d => d.Status == PriorityRouteAggregationOrderStatus.AlmostDue);
            totalOverdueOrders = detailItems.Count(d => d.Status == PriorityRouteAggregationOrderStatus.Overdue);
        }

        var dto = new PriorityRouteAggregationDto
        {
            RouteKey = summary.RouteKey,
            FromOfficeId = summary.FromOfficeId,
            FromOfficeName = GetOfficeName(summary.FromOfficeId),
            ToOfficeId = summary.ToOfficeId,
            ToOfficeName = GetOfficeName(summary.ToOfficeId),
            PlanFromTime = summary.FromTime,
            PlanToTime = summary.ToTime,
            ActualFromTime = summary.ActualFromTime,
            ActualToTime = summary.ActualToTime,
            PriorityPlanName = summary.PriorityPlanName ?? string.Empty,
            TotalOrders = summary.TotalOrders,
            TotalAlmostDueOrders = totalAlmostDueOrders,
            TotalOverdueOrders = totalOverdueOrders,
            TotalWeight = summary.TotalWeight,
            Details = detailsDto
        };

        return Task.FromResult(dto);
    }
}
