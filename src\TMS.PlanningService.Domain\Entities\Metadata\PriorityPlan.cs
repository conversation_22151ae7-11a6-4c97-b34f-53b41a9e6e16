﻿using System.ComponentModel.DataAnnotations.Schema;
using TMS.SharedKernel.Domain.Entities;

namespace TMS.PlanningService.Domain.Entities.Metadata;
public class PriorityPlan : AuditableSoftDeleteEntity
{
    [Column("company_id")]
    public Guid? CompanyId { get; set; }

    [Column("priority_plan_name")]
    public string? PriorityPlanName { get; set; }

    [Column("description")]
    public string? Description { get; set; }

    [Column("is_active")]
    public bool? IsActive { get; set; }

    [Column("search_value")]
    [DatabaseGenerated(DatabaseGeneratedOption.Computed)]
    public string? SearchValue { get; set; }

    public ICollection<PriorityPlanGroup> PriorityPlanGroups { get; set; } = new List<PriorityPlanGroup>();
}
