﻿using MediatR;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Constants.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.UpdatePlanningTemplate;

public class UpdatePlanningTemplateCommandHandler : IRequestHandler<UpdatePlanningTemplateCommand, Unit>
{
    private readonly IBaseRepository<PlanningTemplateEntity> _repository;
    private readonly IBaseRepository<PlanningTemplateDetailEntity> _detailRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly IPlanningTemplateCalculateService _planningTemplateCalculateService;

    public UpdatePlanningTemplateCommandHandler(
        IBaseRepository<PlanningTemplateEntity> repository,
        IBaseRepository<PlanningTemplateDetailEntity> detailRepository,
        IUnitOfWork unitOfWork,
        IPlanningTemplateCalculateService planningTemplateCalculateService)
    {
        _repository = repository;
        _detailRepository = detailRepository;
        _unitOfWork = unitOfWork;
        _planningTemplateCalculateService = planningTemplateCalculateService;

    }

    public async Task<Unit> Handle(UpdatePlanningTemplateCommand request, CancellationToken cancellationToken)
    {
        var entity = await _repository.GetByIdAsync(request.ParamRequest.Id, cancellationToken);

        if (entity == null || entity.IsDeleted)
            throw new Exception($"Planning template with id {request.ParamRequest.Id} not found");


        // Update main entity
        if (request.ParamRequest.RouteId.HasValue)
            entity.RouteId = request.ParamRequest.RouteId.Value;

        if (request.ParamRequest.Name is not null)
        {
            var isExist = await _repository.ExistsAsync(x => x.Name == request.ParamRequest.Name && !x.IsDeleted && x.Id != request.ParamRequest.Id, cancellationToken);
            if (isExist)
                throw new BusinessRuleValidationException(nameof(PlanningTemplateEntity.Name), MessageFormatter.FormatAlreadyExists(nameof(PlanningTemplateEntity), request.ParamRequest.Name), CommonErrorCodes.MS002);

            entity.Name = request.ParamRequest.Name;
        }

        if (request.ParamRequest.Details.Any())
            entity.OfficeCount = request.ParamRequest.Details.Count;

        if (request.ParamRequest.PriorityNumber is not null)
            entity.PriorityNumber = (int)request.ParamRequest.PriorityNumber;

        if (request.ParamRequest.VehicleTypeId.HasValue)
            entity.VehicleTypeId = request.ParamRequest.VehicleTypeId.Value;

        if (request.ParamRequest.IsActive.HasValue)
            entity.IsActive = request.ParamRequest.IsActive.Value;

        if (request.ParamRequest.RouteCode is not null)
            entity.RouteCode = request.ParamRequest.RouteCode;
        if (request.ParamRequest.PostOfficeCodes is not null)
            entity.PostOfficeCodes = request.ParamRequest.PostOfficeCodes;

        // Get existing details
        var existingDetails = await _detailRepository.FindAsync(
            d => d.PlanningTemplateId == entity.Id,
            cancellationToken
        );

        var requestDetailIds = request.ParamRequest.Details
            .Where(d => d.Id.HasValue)
            .Select(d => d.Id!.Value)
            .ToList();

        // Delete details that are not in the ParamRequest
        var detailsToDelete = existingDetails
            .Where(d => !requestDetailIds.Contains(d.Id))
            .ToList();

        foreach (var detail in detailsToDelete)
        {
            _detailRepository.Remove(detail);
        }

        // Upsert details
        var details = new List<PlanningTemplateDetailEntity>();
        foreach (var requestDetail in request.ParamRequest.Details)
        {
            if (requestDetail.Id.HasValue)
            {
                // Update existing detail
                var existingDetail = existingDetails.FirstOrDefault(d => d.Id == requestDetail.Id.Value);
                if (existingDetail != null)
                {
                    existingDetail.PostOfficeId = requestDetail.PostOfficeId;
                    existingDetail.PostOfficeCode = requestDetail.PostOfficeCode;
                    existingDetail.FromTime = requestDetail.FromTime;
                    existingDetail.FromAddDays = requestDetail.FromAddDays;
                    existingDetail.ToTime = requestDetail.ToTime;
                    existingDetail.ToTimeAddDays = requestDetail.ToTimeAddDays;
                    existingDetail.BusinessOperation = requestDetail.BusinessOperation;
                    existingDetail.DistanceBetweenPoints = requestDetail.DistanceBetweenPoints;
                    details.Add(existingDetail);
                    _detailRepository.Update(existingDetail);
                }
            }
            else
            {
                // Insert new detail
                var newDetail = new PlanningTemplateDetailEntity
                {
                    PlanningTemplateId = entity.Id,
                    PostOfficeId = requestDetail.PostOfficeId,
                    PostOfficeCode = requestDetail.PostOfficeCode,
                    FromTime = requestDetail.FromTime,
                    FromAddDays = requestDetail.FromAddDays,
                    ToTime = requestDetail.ToTime,
                    ToTimeAddDays = requestDetail.ToTimeAddDays,
                    BusinessOperation = requestDetail.BusinessOperation,
                    DistanceBetweenPoints = requestDetail.DistanceBetweenPoints,
                    StepNumber = requestDetail.StepNumber
                };
                details.Add(newDetail);
                await _detailRepository.AddAsync(newDetail, cancellationToken);
            }
        }
        entity.Details = details;
        await _planningTemplateCalculateService.CalculateAsync(entity);

        _repository.Update(entity);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return Unit.Value;
    }
}
