﻿using MediatR;
using Microsoft.Extensions.Logging;
using TMS.PlanningService.Contracts.PlanningTemplate;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Queries.DownloadPlanningTemplate;
public class DownloadPlanningTemplateQueryHandler : IRequestHandler<DownloadPlanningTemplateQuery, FileResponse>
{
    private readonly ILogger<DownloadPlanningTemplateQueryHandler> _logger;

    public DownloadPlanningTemplateQueryHandler(ILogger<DownloadPlanningTemplateQueryHandler> logger)
    {
        _logger = logger;
    }

    public async Task<FileResponse> Handle(DownloadPlanningTemplateQuery request, CancellationToken cancellationToken)
    {
        var fileName = Constants.ImportPlanningTemplateFileName;
        var templateFilePath = Path.Combine(AppContext.BaseDirectory, Constants.TemplateFolder, fileName);

        if (!File.Exists(templateFilePath))
        {
            _logger.LogError("Template file not found at {templateFilePath}", templateFilePath);
            throw new Exception($"Template file not found at {templateFilePath}");
        }

        var content = await File.ReadAllBytesAsync(templateFilePath, cancellationToken);
        return new FileResponse(
            content,
            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            fileName
        );
    }
}
