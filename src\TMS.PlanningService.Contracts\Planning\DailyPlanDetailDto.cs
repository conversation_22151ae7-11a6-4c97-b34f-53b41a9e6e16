﻿namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// Detailed daily plan including all route aggregations
/// </summary>
public class DailyPlanDetailDto
{
    public Guid Id { get; set; }
    public Guid CompanyId { get; set; }
    public Guid PlanningTemplateId { get; set; }
    public Guid RouteId { get; set; }
    public DateOnly ExecutionDate { get; set; }
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public double TotalDistance { get; set; }
    public double TotalDuration { get; set; }
    public int OfficeCount { get; set; }
    public Guid VehicleTypeId { get; set; }
    public string VehicleTypeName { get; set; }
    public string RouteCode { get; set; } = string.Empty;
    public string PostOfficeCodes { get; set; } = string.Empty;
    public int PriorityNumber { get; set; }
    public string Status { get; set; } = string.Empty;
    public DateTime? ActualStartTime { get; set; }
    public DateTime? ActualEndTime { get; set; }
    public bool IsActive { get; set; }
    public decimal TotalWeight { get; set; }
    public decimal TotalRealWeight { get; set; }

    /// <summary>
    /// List of route aggregations for this daily plan
    /// Fetched from Redis with real-time order metrics
    /// </summary>
    public List<RouteAggregationDto> Routes { get; set; } = new();

    public DateTime CreatedAt { get; set; }
    public DateTime? UpdatedAt { get; set; }
    public decimal TotalDiffWeight { get; set; }
}
