﻿using Quartz;
using Refit;
using TMS.PlanningService.ApiClient;
using TMS.PlanningService.Application;
using TMS.PlanningService.Application.Jobs;
using TMS.PlanningService.Application.Services.Step1;
using TMS.PlanningService.Application.Services.Step3;
using TMS.PlanningService.Contracts.Orders;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Infra;
using TMS.PlanningService.Infra.Data;
using TMS.SharedKernal.Kafka.Extensions;
using TMS.SharedKernal.RabbitMq.Extensions;
using TMS.SharedKernal.SmoothRedis.Extensions;
using TMS.SharedKernel.Authentication;
using TMS.SharedKernel.Authentication.Handlers;
using TMS.SharedKernel.Utilities;

internal class Program
{
    private static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        var otlpEndpoint = builder.Configuration["OpenTelemetry:OtlpEndpoint"] ?? String.Empty;
        var otlpEndpointKey = builder.Configuration["OpenTelemetry:Key"] ?? String.Empty;
        var env = builder.Configuration["environment"] ?? "Development";
        var serviceName = builder.Environment.ApplicationName;
        var serviceVersion = "v1";
        var pmsPlanningPullCronTime = builder.Configuration.GetSection("PmsPlanningPull:SyncCronTime").Value ?? string.Empty;
        var pmsPlanningPullApiUrl = builder.Configuration.GetSection("PmsPlanningPull:Url").Value ?? string.Empty;
        var maintenanceTime = builder.Configuration.GetSection("Maintenance:Time").Value ?? "0 */2 * * * ?";

        builder.AddBaseRequired(otlpEndpoint, otlpEndpointKey, env, serviceName, serviceVersion);

        // Add Application and Infrastructure services
        builder.Services.AddApplication();
        builder.Services.AddInfrastructure(builder.Configuration);

        // Add inherit DbContext for design time migrations
        builder.Services.AddDerivedDbContext<ApplicationDbContext>();

        // Register Refit client using IHttpClientFactory
        builder.Services.AddRefitClient<IPlanningServiceApi>()
          .ConfigureHttpClient(c =>
          {
              c.BaseAddress = new Uri(pmsPlanningPullApiUrl);
          })
          .AddHttpMessageHandler(sp => new ApiKeyAuthenticationHandler(
              sp.GetRequiredService<IConfiguration>(),
              configurationKey: "PmsPlanningPull:Key",
              queryParameterName: "apikey"
          ));

        builder.Services.AddScoped<AuthorizationDelegatingHandler>();

        // Add DriverServiceApi with AuthorizationDelegatingHandler
        builder.Services.AddRefitClient<IDriverServiceApi>()
          .ConfigureHttpClient(c => c.BaseAddress = new Uri(builder.Configuration.GetSection("DriverService:Url").Value ?? string.Empty))
          .AddHttpMessageHandler<AuthorizationDelegatingHandler>();

        // Add RouteServiceApi with AuthorizationDelegatingHandler
        builder.Services.AddRefitClient<IRouteServiceApi>()
          .ConfigureHttpClient(c => c.BaseAddress = new Uri(builder.Configuration.GetSection("RouteService:Url").Value ?? string.Empty))
          .AddHttpMessageHandler<AuthorizationDelegatingHandler>();

        builder.Services.AddRefitClient<IFleetServiceApi>()
          .ConfigureHttpClient(c => c.BaseAddress = new Uri(builder.Configuration.GetSection("FleetService:Url").Value ?? string.Empty))
          .AddHttpMessageHandler<AuthorizationDelegatingHandler>();

        builder.Services.AddRefitClient<IFileServiceApi>()
            .ConfigureHttpClient(c => c.BaseAddress = new Uri(builder.Configuration.GetSection("FileService:Url").Value ?? string.Empty))
            .AddHttpMessageHandler<AuthorizationDelegatingHandler>();

        builder.Services.AddPolicyBasedAuthorization(PermissionDefinition.AllPermissions);

        // SmoothRedis configuration
        builder.Services.AddSmoothRedis(builder.Configuration);

        // Get connection string
        var connString = builder.Configuration.GetConnectionString("PlanningDBConnection") ?? "";

        builder.Services.AddClusteredQuartz(
            connString,
            schedulerName: $"{serviceName}-scheduler");

        //Kafka configuration
        builder.Services.AddKafkaFlowBuilder(Constants.KafkaFlow)
            .WithBootstrapServers(builder.Configuration.GetSection("KafkaFlow:BootstrapServers").Value ?? "")
            .AddTopic<OrderAggregationEvent, KafkaOrderAggregationHandler>(Constants.KafkaOrderAggregationEvent, $"group-{Constants.KafkaOrderAggregationEvent}")
            .EnableDeadLetter("failed-messages")
            .Build();

        // RabbitMq configuration
        builder.Services.AddRabbitMq(builder.Configuration, Constants.RabbitMq);

        // Planning events consumer
        builder.Services.AddEventHandler<RabbitMqPlanningEvent, RabbitMqPlanningMessageHandler>();
        builder.Services.AddEventConsumer<RabbitMqPlanningEvent>(Constants.RabbitMqPlanningEvents, Constants.RabbitMqQueuePlannings);

        // Order events publisher - for sending RequestOrderRecalculationEvent to OrderService
        // Note: Publisher is registered as keyed service with key "RabbitMq:order_events"
        
        builder.Services.AddQuartz(q =>
        {
            // Use the Quartz database initializer
            // --------------------------------
            // Add planning pull jobs
            var jobKey = new JobKey($"PmsPlanningPull");
            q.AddJob<PlanningPullingProcessingJob>(opts => opts.WithIdentity(jobKey));
            {
                q.AddTrigger(opts => opts
                    .ForJob(jobKey)
                    .WithIdentity($"PmsPlanningPull-startup")
                    .StartAt(DateTimeOffset.Now.AddSeconds(30)) // 30 second delay after startup
                );
            }

            q.AddTrigger(opts => opts
                .ForJob(jobKey)
                .WithIdentity($"PmsPlanningPull-trigger")
                .WithCronSchedule(pmsPlanningPullCronTime));

            // --------------------------------
            // Add Real Plan Generator Job - generates daily execution plans at midnight
            var realPlanJobKey = new JobKey("RealPlanGenerator");
            q.AddJob<DailyPlanningGeneratorJob>(opts => opts.WithIdentity(realPlanJobKey));
            // Startup trigger to run once after 30 seconds
            {
                q.AddTrigger(opts => opts
                    .ForJob(realPlanJobKey)
                    .WithIdentity($"RealPlanGenerator-startup")
                    .StartAt(DateTimeOffset.Now.AddSeconds(30)) // 30 second delay after startup
                );
            }
            q.AddTrigger(opts => opts
                .ForJob(realPlanJobKey)
                .WithIdentity("RealPlanGenerator-trigger")
                .WithCronSchedule("0 */30 * * * ?")
                .StartAt(DateTimeOffset.Now.AddSeconds(45))); // 45 second delay after startup for testing

            // --------------------------------
            // Add Redis Maintenance Job - performs Redis BGSAVE every minute for data persistence
            var redisMaintenanceJobKey = new JobKey("PlanningSerMaintenanceJob");
            q.AddJob<PlanningSerMaintenanceJob>(opts => opts.WithIdentity(redisMaintenanceJobKey));

            q.AddTrigger(opts => opts
                .ForJob(redisMaintenanceJobKey)
                .WithIdentity("PlanningSerMaintenanceJob-trigger")
                .WithCronSchedule(maintenanceTime)
                .StartAt(DateTimeOffset.Now.AddSeconds(10))); // 10s delay after startup

            // --------------------------------
            // Add Route Aggregation Snapshot Job - saves Redis aggregations to database every 10 minutes
            var routeAggSnapshotJobKey = new JobKey("RouteAggregationSnapshotJob");
            q.AddJob<RouteAggregationSnapshotJob>(opts => opts.WithIdentity(routeAggSnapshotJobKey));

            q.AddTrigger(opts => opts
                .ForJob(routeAggSnapshotJobKey)
                .WithIdentity("RouteAggregationSnapshotJob-trigger")
                .WithCronSchedule("0 */10 * * * ?") // Every 10 minutes
                .StartAt(DateTimeOffset.Now.AddSeconds(60))); // 60s delay after startup
        });

        var app = builder.Build();
        
        var tenantId = "9cd1fdb5-7610-466e-9df6-cac822811798";
        if (string.Equals(env, "stg", StringComparison.OrdinalIgnoreCase))
        {
            tenantId = "aed29428-35e5-4f82-98c1-ec76beff12bf";
        }

        var dbParameters = new Dictionary<string, string>
          {
              { "TENANT_ID", tenantId },
          };

        // Auto-create database
        app.EnsureDatabaseCreated<ApplicationDbContext>();
        var schemaOperations = new List<(string TableName, string Indicator)>
        {
            ("qrtz_job_details", "Default"),
            ("qrtz_job_details", "01"),
            ("", "02"),
            ("qrtz_job_details", "Quartz"),
        };

        app.EnsureAllDatabaseSchemasCreated(connectionString: connString, schemaOperations, dbParameters);
        app.UseBaseRequired(env, serviceName, serviceVersion);

        app.Run();
    }
}
