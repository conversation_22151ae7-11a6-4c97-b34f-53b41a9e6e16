﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using TMS.PlanningService.Domain.Entities.Metadata;
namespace TMS.PlanningService.Infra.Data.Configurations.Metadata;

public class ExtraServiceConfiguration : IEntityTypeConfiguration<ExtraService>
{
    public void Configure(EntityTypeBuilder<ExtraService> builder)
    {
        builder.ToTable("extra_service");

        builder.HasKey(o => new { o.ServiceId });

        builder.Property(o => o.ServiceId)
            .IsRequired()
            .HasMaxLength(50)
            .HasColumnName("service_id");

        builder.Property(o => o.ServiceName)
            .HasMaxLength(250)
            .HasColumnName("service_name");

        builder.Property(x => x.IsActive)
               .HasColumnName("is_active");
    }
}
