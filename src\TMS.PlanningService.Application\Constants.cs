﻿namespace TMS.PlanningService.Application;

public class Constants
{
    /// <summary>
    /// Config topic .v.v.v
    /// </summary>
    public const string RabbitMq = "RabbitMq";
    public const string RabbitMqPlanningEvents = "RabbitMq:pms_events";
    public const string RabbitMqQueuePlannings = "plannings";
    public const string RabbitMqOrderEvents = "RabbitMq:order_events";
    public const string RabbitMqQueueReqOrders = "reqorders";

    /// <summary>
    /// Config topic .v.v.v
    /// </summary>
    public const string KafkaFlow = "KafkaFlow";
    public const string KafkaPlanningEvent = "planning-events";
    public const string KafkaOrderAggregationEvent = "order-aggregation-events";

    /// <summary>
    /// Api key Pms
    /// </summary>
    public const string PmsPlanningPullApiKey = "api-key";

    public const string ImportPlanningTemplateFileName = "Template_import_KHM.xlsx";
    public const string TemplateFolder = "Templates";


    /// <summary>
    /// GroupId of order status from PMS
    /// </summary>

    public const string OrderStatusGroup = "MT";

}
