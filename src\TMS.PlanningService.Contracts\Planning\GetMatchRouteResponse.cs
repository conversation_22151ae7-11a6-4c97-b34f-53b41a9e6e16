﻿namespace TMS.PlanningService.Contracts.Planning;
 
public class GetMatchRouteResponse
{
    /// <summary>
    /// Mailer ID (Order ID)
    /// </summary>
    public string MailerId { get; set; } = string.Empty;
    /// <summary>
    /// Is this route the correct/valid route
    /// </summary>
    public bool IsCorrectRoute { get; set; }

    /// <summary>
    ///  Plan start time
    /// </summary>
    public DateTime? PlanStartTime { get; set; }
    
    /// <summary>
    ///  Plan end time
    /// </summary>
    public DateTime? PlanEndTime { get; set; } 
    
    /// <summary>
    ///  Actual start time
    /// </summary>
    public DateTime? ActualStartTime { get; set; }
    
    /// <summary>
    ///  Actual end time
    /// </summary>
    public DateTime? ActualEndTime { get; set; }

}
