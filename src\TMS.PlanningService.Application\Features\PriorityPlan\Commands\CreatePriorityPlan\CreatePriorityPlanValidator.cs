﻿using FluentValidation;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.SharedKernel.Constants;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.CreatePriorityPlan;

public class CreatePlanningTemplateValidator : AbstractValidator<CreatePriorityPlanRequest>
{
    public CreatePlanningTemplateValidator()
    {
        RuleFor(x => x.PriorityPlanName)
        .NotEmpty()
        .WithMessage(string.Format(ValidationMessages.Required, "PriorityPlanName"))
        .MaximumLength(50)
        .WithMessage(string.Format(ValidationMessages.MaxLength, "PriorityPlanName", 50));

        RuleFor(x => x.Description)
        .MaximumLength(250)
        .WithMessage(string.Format(ValidationMessages.MaxLength, "Description", 250));

        RuleFor(x => x.PriorityPlanGroups)
        .NotNull()
        .NotEmpty()
        .WithMessage("Group properties cannot be null or empty")
        .Must(items => (items?.Count ?? 0) >= 1)
        .WithMessage("Priority route must have at least 1 group");

        // Todo: Add more validation rules for Details if needed like checking for valid PostOfficeId, Time ranges, etc.
        RuleForEach(x => x.PriorityPlanGroups)
            .SetValidator(new CreatePriorityPlanGroupValidator());
    }
}
