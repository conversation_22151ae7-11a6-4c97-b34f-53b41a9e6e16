﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using TMS.PlanningService.Domain.IRepository;
using TMS.PlanningService.Infra.Data;
using TMS.PlanningService.Infra.Repository;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.PlanningService.Infra;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructure(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // Database
        services.AddDbContext<ApplicationDbContext>(options =>
        {
            var connectionString = configuration.GetConnectionString("PlanningDBConnection");
            options.UseNpgsql(connectionString, npgsqlOptions =>
            {
                npgsqlOptions.EnableRetryOnFailure(
                    maxRetryCount: 3,
                    maxRetryDelay: TimeSpan.FromSeconds(5),
                    errorCodesToAdd: null);
            });
        });

        services.AddScoped<IServiceTypeRepository, ServiceTypeRepository>();
        services.AddScoped<IPriorityPlanRepository, PriorityPlanRepository>();

        return services;
    }
}
