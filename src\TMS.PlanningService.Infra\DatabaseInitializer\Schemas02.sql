﻿-- Set client encoding to UTF8
SET client_encoding = 'UTF8';

DO $$
DECLARE
    v_system_user_id UUID := '00000000-0000-0000-0000-000000000001';
    v_company_id UUID := '${TENANT_ID}';
    v_data_exists boolean;
BEGIN
    -- ===================================================================
    -- CHECKPOINT: ensure migration runs only once
    -- ===================================================================
    SELECT EXISTS (SELECT 1 FROM public.priority_plan limit 1) INTO v_data_exists;

    IF v_data_exists THEN
        RAISE NOTICE 'Migration already applied. Skipping initialization...';
        RETURN;
    END IF;

    INSERT INTO public.priority_plan (id, company_id, priority_plan_name, description, is_active, created_at, updated_at, created_by, updated_by, is_deleted)
    VALUES
    ('00000000-0000-0000-0000-000000000001', v_company_id, 'Chuyến express', '', TRUE, now(), now(), v_system_user_id, v_system_user_id, FALSE),
    ('00000000-0000-0000-0000-000000000002', v_company_id, 'Chuyến K1a => Tây Bắc','', TRUE, now(), now(), v_system_user_id, v_system_user_id, FALSE),
    ('00000000-0000-0000-0000-000000000003', v_company_id, 'Chuyến K2a => Tây Nguyên', '',TRUE, now(), now(), v_system_user_id, v_system_user_id, FALSE);



    INSERT INTO public.priority_plan_group (
        id, priority_plan_id, logic_operator, step_number
    )
    VALUES
        -- EXPRESS
        ('00000000-0000-0000-0000-000000000001', '00000000-0000-0000-0000-000000000001', null, 0),
        ('00000000-0000-0000-0000-000000000002', '00000000-0000-0000-0000-000000000001', null, 1),

        -- K1A => Tây Bắc
        ('00000000-0000-0000-0000-000000000003', '00000000-0000-0000-0000-000000000002', null, 0),

        -- K2A => Tây Nguyên
        ('00000000-0000-0000-0000-000000000004', '00000000-0000-0000-0000-000000000003', null, 0),
        ('00000000-0000-0000-0000-000000000005', '00000000-0000-0000-0000-000000000003', null, 1);


    -- ============================
    -- 0 Dịch vụ cơ bản 
    -- 1 Dịch vụ gia tăng 
    -- 2 Trọng lượng tính cước
    -- 3 Nơi đi
    -- 4 Nơi tới
    -- ============================
    INSERT INTO public.priority_plan_group_attr (
        id, priority_plan_group_id, property_type, location_type, property_operator, "values", logic_operator, step_number
    )
    VALUES
       ('00000000-0000-0000-0000-000000000001','00000000-0000-0000-0000-000000000001',2,NULL,4,'2',NULL,0),
       ('00000000-0000-0000-0000-000000000002','00000000-0000-0000-0000-000000000001',0,NULL,0,'DE',NULL,1),
       ('00000000-0000-0000-0000-000000000003','00000000-0000-0000-0000-000000000001',3,4,0,'ABH,ADG,ALC,APG,APU,AVG,BBG,BCT,BDA,BDE,BHA,BHG,BHN,BLM,BNH,CHA,CLH,CLI,CTG,CUG,D11,D12,D32,D33,DAH,DAN,DBO,DKI,DKM,DKO,DNI,DTH,DTN,DVG,ETN,HAD,HHA,HMN,HPH,HPP,HPT,HPU,HRG,HTU,HXH,KBH,KDH,KMI,LBN,LED,LEN,LKG,LQN,MDH,MLH,MTI,NBE,NBI,NCH,NHE,NKH,NLG,NOH,NPG,NTI,PCH,PCY,PLG,PMY,PNI,PNN,PTH,PTR,Q08,Q11,QGG,SHH,SNY,TDN,TDU,TGN,TGO,THO,TKH,TMT,TNI,TNP,TOA,TOK,TPG,TTG,TTI,TVG,VLC,VYN,XPG',NULL,2),
       ('00000000-0000-0000-0000-000000000004','00000000-0000-0000-0000-000000000002',2,NULL,3,'2',NULL,0),
       ('00000000-0000-0000-0000-000000000005','00000000-0000-0000-0000-000000000002',0,NULL,0,'DE',NULL,1),
       ('00000000-0000-0000-0000-000000000006','00000000-0000-0000-0000-000000000002',1,NULL,0,'PHG,PTN,HNPN',NULL,2),
       ('00000000-0000-0000-0000-000000000007','00000000-0000-0000-0000-000000000002',3,4,0,'ABH,ADG,ALC,APG,APU,AVG,BBG,BCT,BDA,BDE,BHA,BHG,BHN,BLM,BNH,CHA,CLH,CLI,CTG,CUG,D11,D12,D32,D33,DAH,DAN,DBO,DKI,DKM,DKO,DNI,DTH,DTN,DVG,ETN,HAD,HHA,HMN,HPH,HPP,HPT,HPU,HRG,HTU,HXH,KBH,KDH,KMI,LBN,LED,LEN,LKG,LQN,MDH,MLH,MTI,NBE,NBI,NCH,NHE,NKH,NLG,NOH,NPG,NTI,PCH,PCY,PLG,PMY,PNI,PNN,PTH,PTR,Q08,Q11,QGG,SHH,SNY,TDN,TDU,TGN,TGO,THO,TKH,TMT,TNI,TNP,TOA,TOK,TPG,TTG,TTI,TVG,VLC,VYN,XPG',NULL,3),

    -- K1A => Tây Bắc
       ('00000000-0000-0000-0000-000000000008', '00000000-0000-0000-0000-000000000003', 3, 4, 0, 'K1A',null, 0),
       ('00000000-0000-0000-0000-000000000009', '00000000-0000-0000-0000-000000000003', 4, 1, 0, 'CBG, DBN, LCI, LCU, SLA',null, 1),

    -- Chuyến K2A => Tây Nguyên	
       ('00000000-0000-0000-0000-000000000010', '00000000-0000-0000-0000-000000000004', 3, 4, 0, 'K2A',null, 0),
       ('00000000-0000-0000-0000-000000000011', '00000000-0000-0000-0000-000000000004', 0, null, 0, 'DE', null, 1),
       ('00000000-0000-0000-0000-000000000012', '00000000-0000-0000-0000-000000000004', 4, 4, 0, 'BT,EHO,CKN,EKR,BTT,QGP,BND,BHO,PAN,KNG',null, 2),

       ('00000000-0000-0000-0000-000000000013', '00000000-0000-0000-0000-000000000005', 3, 4, 0, 'K2A',null, 0),
       ('00000000-0000-0000-0000-000000000014', '00000000-0000-0000-0000-000000000005', 4, 4, 0, 'PLU,AYA,AKE,DKD,CPH,CUS,KTM,NHI',null, 1);
END $$;

