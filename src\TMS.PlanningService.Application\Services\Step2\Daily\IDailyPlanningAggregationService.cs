﻿using NPOI.SS.Formula.Functions;
using TMS.PlanningService.Application.Services.Step2.Plan;
using TMS.PlanningService.Contracts.Planning;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Application.Services.Step2.DailyPlanning;

/// <summary>
/// Service for real-time aggregation of routes for daily execution plans
/// Similar to IPlanningAggregationService but linked to specific DailyPlanning instances
/// Updates aggregations incrementally via RabbitMQ events and calculates totals for parent plan
/// Inherits from IPlanningAggregationService for persistence and base functionality
/// </summary>
public interface IDailyPlanningAggregationService : IPlanningAggregationService
{

    /// <summary>
    /// Initializes route aggregations in Redis from generated daily plan
    /// Called by DailyPlanningGeneratorService after creating plan structure
    /// Populates Redis with initial route structure (weights = 0)
    /// Repository is passed as parameter since the aggregation service is a singleton
    /// </summary>
    /// <param name="initialAggregations">Initial route aggregations from template</param>
    /// <param name="dailyPlanRepository">Scoped repository for DailyPlanningEntity</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task InitializeRouteAggregationsAsync(
        List<RouteAggregationSummary> initialAggregations,
        IBaseRepository<DailyPlanningEntity> dailyPlanRepository,
        CancellationToken cancellationToken = default,
        bool clearTimeWindow = true);

    /// <summary>
    /// Gets all cached route aggregations for a specific daily execution plan from Redis
    /// </summary>
    /// <param name="dailyExecutionPlanId">ID of the daily execution plan</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of cached route aggregation summaries</returns>
    Task<List<RouteAggregationSummary>> GetRouteAggregationsAsync(
        Guid dailyExecutionPlanId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets lightweight metadata for all route aggregations of a daily execution plan
    /// Used for efficient filtering before fetching full aggregations
    /// </summary>
    /// <param name="dailyExecutionPlanId">ID of the daily execution plan</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of route metadata</returns>
    Task<List<RouteMetadata>> GetRouteMetadataListAsync(
        Guid dailyExecutionPlanId,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets lightweight metadata for all route aggregations (not filtered by plan)
    /// Used by snapshot job for efficient change detection
    /// </summary>
    /// <returns>List of route metadata</returns>
    Task<List<RouteMetadata>> GetAllRouteMetadataAsync();

    /// <summary>
    /// Calculates totals from all route aggregations and updates the parent DailyPlanning
    /// Called by jobs that have access to scoped repositories
    /// No DB queries for reading - only reads from Redis and writes to DB
    /// </summary>
    /// <param name="dailyExecutionPlanId">ID of the daily execution plan to update</param>
    /// <param name="dailyPlanRepository">Scoped repository for DailyPlanningEntity</param>
    /// <param name="unitOfWork">Unit of work for saving changes</param>
    /// <param name="cancellationToken">Cancellation token</param>
    Task UpdateDailyPlanTotalsAsync(
        Guid dailyExecutionPlanId,
        IBaseRepository<DailyPlanningEntity> dailyPlanRepository,
        IUnitOfWork unitOfWork,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets all active daily plan IDs from Redis
    /// Used by snapshot job to persist aggregations for all active plans
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>HashSet of active daily plan IDs</returns>
    Task<HashSet<Guid>> GetActiveDailyPlanIdsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets plan totals from Redis cache for multiple daily plans in batch
    /// Uses _redis.Batch.Cache.GetManyAsync for efficient single-pipeline batch retrieval
    /// Returns real-time weight data: TotalWeight, TotalRealWeight, TotalDiffWeight
    /// </summary>
    /// <param name="planIds">List of daily plan IDs to fetch totals for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Dictionary mapping plan IDs to their totals (only includes plans with cached data)</returns>
    Task<Dictionary<Guid, DailyPlanTotals>> GetPlanTotalsBatchAsync(
        List<Guid> planIds,
        CancellationToken cancellationToken = default);
}
