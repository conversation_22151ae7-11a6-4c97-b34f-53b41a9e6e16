﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace TMS.PlanningService.Contracts.Dto;
public class OrderByIdsDto
{
    public string OrderId { get; set; } = string.Empty;
    public string ReceiverPostOfficeId { get; set; } = string.Empty;
    public string ReceiverPostOfficeName { get; set; } = string.Empty;
    public string SenderPostOfficeId { get; set; } = string.Empty;
    public string SenderPostOfficeName { get; set; } = string.Empty;
    public DateTime? PlanStartTime { get; set; }
    public DateTime? PlanEndTime { get; set; }
    public DateTime? ActualStartTime { get; set; }
    public DateTime? ActualEndTime { get; set; }
    public decimal? Weight { get; set; }
    public decimal? RealWeight { get; set; }
    public decimal? CalWeight { get; set; }
    public string? ServiceTypeId { get; set; }
    public string? ServiceTypeName { get; set; }
    public string? ExtraService { get; set; }
    public string? ExtraServiceName { get; set; }
    public DateTime? AcceptedTime { get; set; }
    public string? CurrentStatusId { get; set; }
    public string? CurrentStatusName { get; set; }
    public int OrderItemCount { get; set; }

    private List<OrderItemDto> Items = new();
    public bool IsCorrectRoute { get; set; }
}

public class OrderItemDto
{
    public string OrderItemId { get; set; } = string.Empty;
    public string OrderId { get; set; } = string.Empty;
    public DateTime OrderCreatedAt { get; set; }
    public decimal? Weight { get; set; }
    public decimal? RealWeight { get; set; }
    public decimal? CalWeight { get; set; }
    public decimal? L { get; set; }
    public decimal? H { get; set; }
    public decimal? W { get; set; }
    public string? CurrentPostOfficeId { get; set; }
    public string? CurrentPostOfficeName { get; set; }
    public string? CurrentStatusId { get; set; }
    public string? CurrentStatusName { get; set; }
}
