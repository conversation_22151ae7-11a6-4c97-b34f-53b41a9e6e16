﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TMS.PlanningService.Api", "src\TMS.PlanningService.Api\TMS.PlanningService.Api.csproj", "{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TMS.PlanningService.ApiClient", "src\TMS.PlanningService.ApiClient\TMS.PlanningService.ApiClient.csproj", "{04570E7B-1F72-8E81-F879-50E14F4DA472}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TMS.PlanningService.Application", "src\TMS.PlanningService.Application\TMS.PlanningService.Application.csproj", "{1AA473DA-66BA-89EC-3852-506FCC65F1C0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TMS.PlanningService.Contracts", "src\TMS.PlanningService.Contracts\TMS.PlanningService.Contracts.csproj", "{61515634-5CAA-60D5-912C-8E0B4AE657A3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TMS.PlanningService.Domain", "src\TMS.PlanningService.Domain\TMS.PlanningService.Domain.csproj", "{18B3A085-92DD-1BD0-0C35-F98485948595}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TMS.PlanningService.Shared", "src\TMS.PlanningService.Shared\TMS.PlanningService.Shared.csproj", "{948F4CEB-2521-FEE8-DC01-95F741A3A28C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TMS.PlanningService.Infra", "src\TMS.PlanningService.Infra\TMS.PlanningService.Infra.csproj", "{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{0AB3BF05-4346-4AA6-1389-037BE0695223}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "TMS.PlanningService.Tests", "tests\TMS.PlanningService.Tests\TMS.PlanningService.Tests.csproj", "{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Debug|x64.ActiveCfg = Debug|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Debug|x64.Build.0 = Debug|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Debug|x86.Build.0 = Debug|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Release|Any CPU.Build.0 = Release|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Release|x64.ActiveCfg = Release|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Release|x64.Build.0 = Release|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Release|x86.ActiveCfg = Release|Any CPU
		{D2FFAC4C-FAFC-21D2-EA2D-BD63E56EB57F}.Release|x86.Build.0 = Release|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Debug|x64.ActiveCfg = Debug|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Debug|x64.Build.0 = Debug|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Debug|x86.ActiveCfg = Debug|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Debug|x86.Build.0 = Debug|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Release|Any CPU.Build.0 = Release|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Release|x64.ActiveCfg = Release|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Release|x64.Build.0 = Release|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Release|x86.ActiveCfg = Release|Any CPU
		{04570E7B-1F72-8E81-F879-50E14F4DA472}.Release|x86.Build.0 = Release|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Debug|x64.Build.0 = Debug|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Debug|x86.ActiveCfg = Debug|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Debug|x86.Build.0 = Debug|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Release|Any CPU.Build.0 = Release|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Release|x64.ActiveCfg = Release|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Release|x64.Build.0 = Release|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Release|x86.ActiveCfg = Release|Any CPU
		{1AA473DA-66BA-89EC-3852-506FCC65F1C0}.Release|x86.Build.0 = Release|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Debug|x64.ActiveCfg = Debug|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Debug|x64.Build.0 = Debug|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Debug|x86.ActiveCfg = Debug|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Debug|x86.Build.0 = Debug|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Release|Any CPU.Build.0 = Release|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Release|x64.ActiveCfg = Release|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Release|x64.Build.0 = Release|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Release|x86.ActiveCfg = Release|Any CPU
		{61515634-5CAA-60D5-912C-8E0B4AE657A3}.Release|x86.Build.0 = Release|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Debug|x64.ActiveCfg = Debug|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Debug|x64.Build.0 = Debug|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Debug|x86.ActiveCfg = Debug|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Debug|x86.Build.0 = Debug|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Release|Any CPU.Build.0 = Release|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Release|x64.ActiveCfg = Release|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Release|x64.Build.0 = Release|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Release|x86.ActiveCfg = Release|Any CPU
		{18B3A085-92DD-1BD0-0C35-F98485948595}.Release|x86.Build.0 = Release|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Debug|x64.ActiveCfg = Debug|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Debug|x64.Build.0 = Debug|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Debug|x86.ActiveCfg = Debug|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Debug|x86.Build.0 = Debug|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Release|Any CPU.Build.0 = Release|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Release|x64.ActiveCfg = Release|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Release|x64.Build.0 = Release|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Release|x86.ActiveCfg = Release|Any CPU
		{948F4CEB-2521-FEE8-DC01-95F741A3A28C}.Release|x86.Build.0 = Release|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Debug|x64.Build.0 = Debug|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Debug|x86.Build.0 = Debug|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Release|Any CPU.Build.0 = Release|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Release|x64.ActiveCfg = Release|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Release|x64.Build.0 = Release|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Release|x86.ActiveCfg = Release|Any CPU
		{E159E0DB-D85B-4F52-6F3C-DF60FFE8990D}.Release|x86.Build.0 = Release|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Debug|x64.ActiveCfg = Debug|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Debug|x64.Build.0 = Debug|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Debug|x86.ActiveCfg = Debug|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Debug|x86.Build.0 = Debug|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Release|Any CPU.Build.0 = Release|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Release|x64.ActiveCfg = Release|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Release|x64.Build.0 = Release|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Release|x86.ActiveCfg = Release|Any CPU
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{8ABC7773-DC8D-4CA5-8EE2-03E7B77DA870} = {0AB3BF05-4346-4AA6-1389-037BE0695223}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A18121E1-8529-47D6-BD63-ABFAD5C3E620}
	EndGlobalSection
EndGlobal
