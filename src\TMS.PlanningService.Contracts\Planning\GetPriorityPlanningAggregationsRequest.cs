﻿using System.Collections.Generic;
using TMS.PlanningService.Domain.Enum;

namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// Request for getting priority planning aggregations (DE service type only) with filtering and pagination
/// </summary>
public record GetPriorityPlanningAggregationsRequest(
    string? SearchTerm,
    int Page,
    int PageSize,
    List<string>? FromOfficeIds,
    List<string>? ToOfficeIds,
    List<Guid>? PriorityPlanIds,
    PriorityRouteAggregationSortOrder? SortOrder
);
