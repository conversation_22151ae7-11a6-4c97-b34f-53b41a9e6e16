﻿using MediatR;
using TMS.PlanningService.Application.Services.Inferfaces;
using TMS.PlanningService.Domain.Entities;
using TMS.SharedKernel.Constants;
using TMS.SharedKernel.Constants.Extensions;
using TMS.SharedKernel.Domain;
using TMS.SharedKernel.Domain.Exceptions;
using TMS.SharedKernel.Domain.Provider.Interfaces;

namespace TMS.PlanningService.Application.Features.PlanningTemplate.Commands.CreatePlanningTemplate;

public class CreatePlanningTemplateCommandHandler : IRequestHandler<CreatePlanningTemplateCommand, Guid>
{
    private readonly IBaseRepository<PlanningTemplateEntity> _repository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICurrentFactorProvider _currentFactorProvider;
    private readonly IPlanningTemplateCalculateService _planningTemplateCalculateService;


    public CreatePlanningTemplateCommandHandler(
        IBaseRepository<PlanningTemplateEntity> repository,
        IUnitOfWork unitOfWork,
        ICurrentFactorProvider currentFactorProvider,
        IPlanningTemplateCalculateService planningTemplateCalculateService)
    {
        _repository = repository;
        _unitOfWork = unitOfWork;
        _currentFactorProvider = currentFactorProvider;
        _planningTemplateCalculateService = planningTemplateCalculateService;
    }

    public async Task<Guid> Handle(CreatePlanningTemplateCommand request, CancellationToken cancellationToken)
    {
        if (_currentFactorProvider.CompanyId == Guid.Empty)
            throw new BusinessRuleValidationException("Company", MessageFormatter.FormatNotFound("Company", _currentFactorProvider.CompanyId));

        var isExist = await _repository.ExistsAsync(x => x.Name == request.ParamRequest.Name && !x.IsDeleted, cancellationToken);
        if (isExist)
            throw new BusinessRuleValidationException(nameof(PlanningTemplateEntity.Name), MessageFormatter.FormatAlreadyExists(nameof(PlanningTemplateEntity), request.ParamRequest.Name), CommonErrorCodes.MS002);

        //[TMS01-53] Determine PriorityNumber: use provided positive value, otherwise set to (max existing +1) for the company
        var priorityNumber = request.ParamRequest.PriorityNumber;
        if (priorityNumber <= 0)
        {
            var count = await _repository.CountAsync(t => t.CompanyId == _currentFactorProvider.CompanyId && t.RouteId == request.ParamRequest.RouteId && !t.IsDeleted, cancellationToken);
            priorityNumber = count + 1;
        }

        var entity = new PlanningTemplateEntity
        {
            CompanyId = _currentFactorProvider.CompanyId,
            RouteId = request.ParamRequest.RouteId,
            Name = request.ParamRequest.Name,
            OfficeCount = request.ParamRequest.Details.Count,
            PriorityNumber = priorityNumber,
            VehicleTypeId = request.ParamRequest.VehicleTypeId,
            IsActive = request.ParamRequest.IsActive,
            RouteCode = request.ParamRequest.RouteCode,
            PostOfficeCodes = request.ParamRequest.PostOfficeCodes,
            Details = request.ParamRequest.Details.Select(d => new PlanningTemplateDetailEntity
            {
                PostOfficeId = d.PostOfficeId,
                PostOfficeCode = d.PostOfficeCode,
                FromTime = d.FromTime,
                FromAddDays = d.FromAddDays,
                ToTime = d.ToTime,
                ToTimeAddDays = d.ToTimeAddDays,
                BusinessOperation = d.BusinessOperation,
                DistanceBetweenPoints = d.DistanceBetweenPoints,
                StepNumber = d.StepNumber
            }).ToList()
        };
        await _planningTemplateCalculateService.CalculateAsync(entity);
        await _repository.AddAsync(entity, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        return entity.Id;
    }
}
