﻿using Microsoft.Extensions.Logging;
using Quartz;
using StackExchange.Redis;

namespace TMS.PlanningService.Application.Jobs;

/// <summary>
/// Quartz job that performs Redis maintenance by triggering BGSAVE every minute
/// Ensures data persistence and fast recovery (5-30 seconds) after Redis restarts
/// Minimal data loss window (max 1 minute between saves)
/// </summary>
[DisallowConcurrentExecution]
public class PlanningSerMaintenanceJob : IJob
{
    private readonly IConnectionMultiplexer _redis;
    private readonly ILogger<PlanningSerMaintenanceJob> _logger;

    public PlanningSerMaintenanceJob(
        IConnectionMultiplexer redis,
        ILogger<PlanningSerMaintenanceJob> logger)
    {
        _redis = redis;
        _logger = logger;
    }

    public async Task Execute(IJobExecutionContext context)
    {
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        try
        {
            var server = _redis.GetServer(_redis.GetEndPoints().First());

            // Check if previous BGSAVE is still running
            var info = await server.InfoAsync("persistence");

            // Flatten IGrouping to get key-value pairs
            var infoDict = info
                .SelectMany(group => group)
                .ToDictionary(kvp => kvp.Key, kvp => kvp.Value);

            // Check if save is in progress
            if (infoDict.TryGetValue("rdb_bgsave_in_progress", out var bgsaveInProgress)
                && bgsaveInProgress == "1")
            {
                _logger.LogWarning("Skipping PlanningService maintenance - previous save still in progress");
                return;
            }

            // Get current database size before save
            var dbSize = await server.DatabaseSizeAsync();
            var lastSaveTime = server.LastSave();

            _logger.LogWarning(
                "Triggering PlanningService BGSAVE - Database size: {DbSize} keys, Last save: {LastSave}",
                dbSize,
                lastSaveTime);

            // Trigger background save
            await server.SaveAsync(SaveType.BackgroundSave);

            stopwatch.Stop();

            _logger.LogInformation(
                "PlanningService maintenance completed - BGSAVE triggered successfully, Database size: {DbSize} keys, Duration: {ElapsedMs}ms",
                dbSize,
                stopwatch.ElapsedMilliseconds);

            // Log additional metrics
            if (infoDict.TryGetValue("rdb_last_bgsave_status", out var lastStatus)
                && infoDict.TryGetValue("rdb_last_bgsave_time_sec", out var lastDuration))
            {
                _logger.LogWarning(
                    "Previous BGSAVE - Status: {Status}, Duration: {Duration}s",
                    lastStatus,
                    lastDuration);
            }
        }
        catch (RedisConnectionException ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "PlanningService connection failed during maintenance - Operation skipped (non-critical)");
            // Don't throw - connection issues shouldn't crash the job
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(
                ex,
                "PlanningService maintenance failed after {ElapsedMs}ms",
                stopwatch.ElapsedMilliseconds);
            // Don't throw - allow job to retry on next schedule
        }
    }
}
