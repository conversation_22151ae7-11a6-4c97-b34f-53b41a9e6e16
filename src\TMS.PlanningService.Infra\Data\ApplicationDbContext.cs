﻿using System.Runtime.ConstrainedExecution;
using Microsoft.EntityFrameworkCore;
using TMS.PlanningService.Domain.Entities;
using TMS.PlanningService.Domain.Entities.Metadata;
using TMS.SharedKernel.Domain.Provider.Interfaces;
using TMS.SharedKernel.EntityFrameworkCore;

namespace TMS.PlanningService.Infra.Data;

public class ApplicationDbContext : BaseDbContext
{
    public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options, ICurrentFactorProvider currentFactorProvider) : 
        base(options, currentFactorProvider)
    {
    }

    // Planning entities
    public DbSet<MailerRouteMasterEntity> MailerRouteMasters { get; set; }
    public DbSet<MailerPlanRouteEntity> MailerPlanRoutes { get; set; }
    public DbSet<MailerAdjustRouteEntity> MailerAdjustRoutes { get; set; }
    public DbSet<MailerActualRouteEntity> MailerActualRoutes { get; set; }
    public DbSet<LeadTime> LeadTimes { get; set; }
    public DbSet<PlanningTemplateEntity> PlanningTemplates { get; set; }
    public DbSet<PlanningTemplateDetailEntity> PlanningTemplateDetails { get; set; }
    public DbSet<ServiceType> ServiceTypes { get; set; }
    public DbSet<ExtraService> ExtraServices { get; set; }
    public DbSet<OrderStatus> OrderStatuses { get; set; }
    public DbSet<PriorityPlan> PriorityRoutes { get; set; }
    public DbSet<PriorityPlanGroup> PriorityGroupProperties { get; set; }
    public DbSet<PriorityPlanGroupAttr> PriorityProperties { get; set; }

    // Daily plannings (generated daily from templates)
    public DbSet<DailyPlanningEntity> DailyPlannings { get; set; }

    // Route aggregation snapshots (persisted from Redis every 10 minutes)
    // Supports multiple aggregation types: 'normal', 'daily', 'priority'
    // AggregationType column determines the type, DailyPlanningId links daily aggregations
    public DbSet<RouteAggregationEntity> RouteAggregations { get; set; }

    // Orders associated with route aggregations (many-to-many relationship)
    // Stores which orders are on which routes, maintaining history even after orders move
    public DbSet<RouteAggregationOrderEntity> RouteAggregationOrders { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ApplicationDbContext).Assembly);
    }
}
