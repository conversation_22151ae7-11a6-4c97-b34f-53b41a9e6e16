﻿using TMS.PlanningService.Domain.Enum;
using TMS.SharedKernel.Domain;

namespace TMS.PlanningService.Contracts.Planning;

/// <summary>
/// DTO for route aggregation summary with order enrichment
/// </summary>
public class RouteAggregationDto
{
    public string RouteKey { get; set; } = string.Empty;
    public string FromOfficeId { get; set; } = string.Empty;
    public string FromOfficeName { get; set; } = string.Empty;
    public BusinessOperation FromBusinessOperation { get; set; }
    public string FromBusinessOperationName { get; set; } = string.Empty;
    public DateTime? ActualFromTime { get; set; }
    public DateTime? PlanFromTime { get; set; }
    public string ToOfficeId { get; set; } = string.Empty;
    public string ToOfficeName { get; set; } = string.Empty;
    public BusinessOperation ToBusinessOperation { get; set; }
    public string ToBusinessOperationName { get; set; } = string.Empty;
    public DateTime? ActualToTime { get; set; }
    public DateTime? PlanToTime { get; set; }
    public int TotalDurationMinutes { get; set; }
    public double AverageDurationMinutes { get; set; }
    public DateTime? EarliestStartTime { get; set; }
    public DateTime? LatestEndTime { get; set; }

    // Order metrics (enriched from OrderService)
    public int TotalOrders { get; set; }
    public int TotalItems { get; set; }
    public decimal TotalWeight { get; set; }
    public decimal TotalRealWeight { get; set; }
    public decimal TotalCalWeight { get; set; }
    public List<OptionsCountDto> TransportProviderBreakdown { get; set; } = new();
    public List<OptionsCountDto> VehicleTypeBreakdown { get; set; } = new();
    public List<OptionsCountDto> TransportMethodBreakdown { get; set; } = new();
    public int PriorityScore { get; set; }
    public bool NeedsOptimization { get; set; }
    public DateTime AggregatedAt { get; set; }

    public decimal TotalEstimateWeight { get; set; }
    public decimal TotalOnVehicleWeight { get; set; }
    public decimal TotalDiffWeight => TotalOnVehicleWeight - TotalEstimateWeight;
    public PagedResult<OrderItemDetail>? OrderDetails { get; set; }
}
 
