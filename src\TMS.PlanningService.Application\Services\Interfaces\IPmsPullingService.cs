﻿using static TMS.PlanningService.Application.Jobs.PlanningPullingProcessingJob;

namespace TMS.PlanningService.Application.Services.Inferfaces;

public interface IPmsPullingService
{
    /// <summary>
    /// Sync service-type from Pms API
    /// </summary>
    Task<int> SyncServiceTypeAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync extra-service from Pms API
    /// </summary>
    Task<int> SyncExtraServiceAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync order status from Pms API
    /// </summary>
    Task<int> SyncOrderStatusAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Sync lead time from Pms API
    /// </summary>
    Task<SyncStats> SynLeadTimeAsync(CancellationToken cancellationToken = default);

}
