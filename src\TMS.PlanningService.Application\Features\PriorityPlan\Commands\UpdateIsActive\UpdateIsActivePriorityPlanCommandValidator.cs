﻿using FluentValidation;
using TMS.PlanningService.Contracts.PriorityPlan;
using TMS.SharedKernel.Constants;

namespace TMS.PlanningService.Application.Features.PriorityPlan.Commands.UpdateIsActive;

public class DeletePlanningTemplateValidator : AbstractValidator<UpdateIsActivePriorityPlanRequest>
{
    public DeletePlanningTemplateValidator()
    {
        RuleFor(c => c.Id)
           .NotEmpty()
           .WithMessage(string.Format(ValidationMessages.Required, "Id"))
           .WithErrorCode("Id");

        RuleFor(c => c.IsActive)
           .NotNull()
           .WithMessage(string.Format(ValidationMessages.Required, "IsActive"))
           .WithErrorCode("IsActive");
    }
}
