﻿using MediatR;
using Microsoft.AspNetCore.Mvc;
using TMS.PlanningService.Application.Features.TransportVehicleType.Queries.GetTransportVehicleTypes;
using TMS.PlanningService.Contracts.Dto;

namespace TMS.PlanningService.Api.Controllers;

[ApiController]
[Route("api/v{version:apiVersion}/transport-vehicle-types")]
[Produces("application/json")]
public class TransportVehicleTypeController : ControllerBase
{
    private readonly IMediator _mediator;

    public TransportVehicleTypeController(IMediator mediator)
    {
        _mediator = mediator;
    }

    ///// <summary>
    ///// Get all lead time types
    ///// </summary> 
    ///// <returns>List of lead time type</returns>
    [HttpGet("")]
    [ProducesResponseType(typeof(List<TransportVehicleTypeDto>), StatusCodes.Status200OK)]
    public async Task<ActionResult<List<TransportVehicleTypeDto>>> GetTransportVehicleTypes()
    {
        var query = new GetTransportVehicleTypesQuery();
        var result = await _mediator.Send(query);
        return Ok(result);
    }
}
